<!--pages/order-allocation/my-order/my-order.wxml-->
<!-- <text>pages/order-allocation/my-order/my-order.wxml</text> -->
<!-- 搜索区域 -->
<view class="search-container">
  <t-search model:value="{{searchValue}}" placeholder="请输入提单号" bind:submit="onSearch" bind:clear="onClearSearch" bind:change="onSearchInput" action-text="搜索" left-icon="search" />
</view>

<!-- 时间查询区域 -->
<view class="time-filter">
  <view class="time-filter-item">
    <view class="time-label">起始时间：</view>
    <view class="time-picker" bindtap="selectStartTime">
      <text class="{{startDate ? 'time-selected' : 'time-placeholder'}}">{{startDate || '请选择起始时间'}}</text>
      <text class="time-icon">📅</text>
    </view>
  </view>
  <view class="time-filter-item">
    <view class="time-label">截止时间：</view>
    <view class="time-picker" bindtap="selectEndTime">
      <text class="{{endDate ? 'time-selected' : 'time-placeholder'}}">{{endDate || '请选择截止时间'}}</text>
      <text class="time-icon">📅</text>
    </view>
  </view>
  <view class="time-clear" bindtap="clearTimeFilter">清空</view>
</view>

<!-- 状态筛选器 -->
<view class="status-filter">
  <view class="filter-label">状态筛选：</view>
  <view class="filter-buttons">
    <view class="filter-btn {{status20Active ? 'active' : ''}}" bindtap="toggleStatus" data-status="20">
      生效
    </view>
    <view class="filter-btn {{status99Active ? 'active' : ''}}" bindtap="toggleStatus" data-status="99">
      完成
    </view>
  </view>
</view>
<view class="container {{dataList.length>0 ? '' : 'dataList-empty' }}">
  <view class="my-list" wx:for="{{dataList}}" data-index="{{index}}" wx:key="index" wx:for-item="item" bind:tap="toInfo">
    <!-- 右上角启动状态标签 -->
    <view class="start-status-tag {{item.status != '20' ? 'completed' : (item.isStart == '1' ? 'started' : 'not-started')}}">
      {{item.status != '20' ? '完成' : (item.isStart == '1' ? '已启动' : '未启动')}}
    </view>

    <view class="my-list-li">
      <view class="my-list-li-name">配单号：</view>
      <view class="my-list-li-value">{{item.allocateVehicleNo}}</view>
    </view>
    <view class="my-list-li">
      <view class="my-list-li-name">配单状态：</view>
      <view class="my-list-li-value">{{item.status == '20' ? '生效' : '完成'}}</view>
    </view>
    <view class="my-list-li">
      <view class="my-list-li-name">装卸业务：</view>
      <view class="my-list-li-value">{{item.allocType}}</view>
    </view>
    <view class="my-list-li">
      <view class="my-list-li-name">车牌号：</view>
      <view class="my-list-li-value">{{item.vehicleNo}}</view>
    </view>
    <view class="my-list-li">
      <view class="my-list-li-name">进厂时间：</view>
      <view class="my-list-li-value">{{item.intoFactoryDate}}</view>
    </view>
    <view class="my-list-li">
      <view class="my-list-li-name">配单时间：</view>
      <view class="my-list-li-value">{{item.recCreateTimeStr}}</view>
    </view>
    <!-- <view class="my-list-li">
      <view class="my-list-li-name">配单结果：</view>
      <view class="my-list-li-value">{{item.remark}}</view>
    </view> -->
    <!-- 统一的按钮容器 -->
    <view class="my-list-li-button">
      <!-- 物流公司用户(reservationIdentity == '30')的按钮 -->
      <view wx:if="{{userInfo.reservationIdentity == '30' || userInfo.reservationIdentity == '50'}}" class="start-queuing" catchtap="getAddress" data-item="{{item}}">启动排队</view>
      <view wx:if="{{userInfo.reservationIdentity == '30' || userInfo.reservationIdentity == '50'}}" class="canle-queuing" catchtap="canclePai" data-item="{{item}}">取消排队</view>
      <view wx:if="{{(userInfo.reservationIdentity == '30' || userInfo.reservationIdentity == '30') && item.allocType == '卸货配单'}}" class="canle-queuing" catchtap="cancleOrder" data-item="{{item}}">取消配单</view>

      <!-- 非物流公司用户装货配单的按钮 -->
      <view  wx:if="{{(userInfo.reservationIdentity == '10' || userInfo.reservationIdentity == '20' || userInfo.reservationIdentity == '40' || userInfo.reservationIdentity == '70') && item.allocType == '装货配单'}}" class="start-queuing edit-btn" catchtap="editOrder" data-item="{{item}}">修改配单</view>
      <view  wx:if="{{(userInfo.reservationIdentity == '10' || userInfo.reservationIdentity == '20' || userInfo.reservationIdentity == '40' || userInfo.reservationIdentity == '70') && item.allocType == '装货配单'}}" class="canle-queuing" catchtap="cancleOrder" data-item="{{item}}">取消配单</view>


      <!-- 暂停按钮 - 只针对司机身份显示 -->
      <view wx:if="{{userInfo.reservationIdentity == '30' || userInfo.reservationIdentity == '50'}}" class="pause-btn" catchtap="pauseOrder" data-item="{{item}}">暂停</view>
    </view>

  </view>
  <view wx:if="{{!dataList.length>0}}">
    <image src="/assets/image/empty_data.png" class="my-order-img"></image>
    <view class="my-order-text">暂无数据</view>
  </view>
  <!-- 弹窗 -->
  <t-popup visible="{{visible}}" usingCustomNavbar bind:visible-change="onVisibleChange" placement="{{'center'}}">
    <view class="block-center">
      <view class="block-title">请填写数据</view>
      <t-cell class="mb-16" title="厂区" arrow hover note="{{factoryArea}}" bind:click="selectFactory" t-class-note="app-t-class" />
      <t-cell class="mb-16" title="卸货点" arrow hover note="{{unloadingPoint}}" bind:click="selectPoint" t-class-note="app-t-class" />
      <view class="bottom">
        <view class="bottom-cancle" bindtap="handleCancle">取消</view>
        <view class="bottom-confirm" bindtap="handleConfirm">确定</view>
      </view>
    </view>
  </t-popup>
  <t-picker visible="{{factoryVisible}}" value="{{factoryValue}}" title="选择厂区" cancelBtn="取消" confirmBtn="确认" usingCustomNavbar bindchange="onPickerChange">
    <t-picker-item options="{{customerCodeList}}" />
  </t-picker>
  <t-picker visible="{{unloadingPointVisible}}" value="{{unloadingPointValue}}" title="选择卸货点" cancelBtn="取消" confirmBtn="确认" usingCustomNavbar bindchange="onPointChange">
    <t-picker-item options="{{unloadingPointList}}" />
  </t-picker>
  <t-toast id="t-toast" />

  <t-dialog visible="{{showTextAndTitle}}" title="提示" content="{{content}}" confirm-btn="{{ confirmBtn }}" bind:confirm="closeDialog" />

  <!-- 时间选择器弹窗 -->
  <t-popup visible="{{timePickerVisible}}" usingCustomNavbar bind:visible-change="onTimePickerVisibleChange" placement="bottom">
    <view class="time-picker-popup">
      <view class="time-picker-header">
        <view class="time-picker-cancel" bindtap="onTimePickerCancel">取消</view>
        <view class="time-picker-title">{{timePickerTitle}}</view>
        <view class="time-picker-confirm" bindtap="onTimePickerConfirm">确定</view>
      </view>
      <picker mode="date" value="{{timePickerValue}}" bindchange="onTimePickerDateChange">
        <view class="time-picker-content">
          <text>{{timePickerValue || '请选择日期'}}</text>
        </view>
      </picker>
    </view>
  </t-popup>
</view>