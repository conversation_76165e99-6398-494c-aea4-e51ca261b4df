<!--pages/order-allocation/discharge-cargo/discharge-cargo.wxml-->
<!-- <text>pages/order-allocation/discharge-cargo/discharge-cargo.wxml</text> -->
<view class="container">
  <view class="container-con">
    <view id="fixed-height">
      <!-- marquee="{{marquee2}}" prefixIcon="sound" -->
      <t-notice-bar theme="error" visible="{{visible}}" content="若为卷材类货物，请按车头至车尾顺序依次扫描标签，包含自带货物；若为板材或零部件，仅需填写板材包数（零部件按独立包装清点数量），无需扫描标签。" t-class-content="notice-content"></t-notice-bar>
      <view class="form-group">
        <!-- 车牌号选择 - 改为自定义带搜索的下拉框 -->
        <view class="car-selector">
          <t-cell class="mb-16" title="车牌号" arrow hover note="{{carTitle}}" bind:click="selectCar" t-class-note="app-t-class" />
          
          <!-- 搜索框和下拉列表 -->
          <view class="car-dropdown {{carFlag ? 'show' : ''}}">
            <view class="search-box">
              <t-input placeholder="{{searchPlaceholder}}" value="{{searchKeyword}}" bindchange="onSearchCar" t-class-input="search-input" />
            </view>
            <view class="car-list">
              <view wx:for="{{filteredCarList}}" wx:key="value" 
                    class="car-item {{selectedCarItem && selectedCarItem.value === item.value && selectedCarItem.driverName === item.driverName ? 'selected' : ''}}" 
                    bind:tap="selectCarItem" 
                    data-car="{{item}}">
                <view class="car-plate">{{item.label}}</view>
                <view class="driver-name">{{item.driverName}}</view>
              </view>
              <view wx:if="{{filteredCarList.length === 0}}" class="no-data">
                暂无匹配的车牌号或司机
              </view>
            </view>
            <view class="dropdown-actions">
              <view class="btn-cancel" bind:tap="cancelSelectCar">取消</view>
              <view class="btn-confirm" bind:tap="confirmSelectCar">确认</view>
            </view>
          </view>
          
          <!-- 遮罩层，点击关闭下拉框 -->
          <view wx:if="{{carFlag}}" class="dropdown-mask" bind:tap="cancelSelectCar"></view>
        </view>
        <t-cell class="mb-16" t-class-note="app-t-class" title="扫描卷/板" arrow hover note="{{typeText}}" bind:click="onChangeType" />
        <t-picker visible="{{scanVisible}}" value="{{scanType}}" title="选择扫描类型" cancelBtn="取消" confirmBtn="确认" usingCustomNavbar bindchange="typeChange">
          <t-picker-item options="{{scanTypeList}}" />
        </t-picker>
                <t-cell title="是否排队">
          <view slot="note">
            <t-radio-group default-value="0" borderless t-class="box" bind:change="onQueueChange">
              <view style="margin-right: 22rpx;display: inline-block;">
                <t-radio block="{{false}}" label="是" value="1" />
              </view>
              <t-radio block="{{false}}" label="否" value="0" />
            </t-radio-group>
          </view>
        </t-cell>
      </view>
        <t-input label="捆包个数" placeholder="请输入板的捆包个数" value="{{plateNumber}}" disabled="{{scanType == '卷'}}" bindchange="numberChange" />
        <t-input label="捆包号" placeholder="请输入或扫描捆包号" data-key="packId" value="{{packId}}" confirm-type="search" bindenter="enterEvent" bindchange="packChange" style="text-align: right;" disabled="{{scanType=='板'}}">
          <t-button slot="suffix" theme="primary" style="margin-left: 32rpx;" size="extra-small" bind:tap="onScanCode">
            <image src="/assets/image/scan.png" class="scan-img" />
          </t-button>
        </t-input>
      <view class='discharge-list'>
        <view> 已扫捆包明细:</view>
        <view class="discharge-list-btn" bindtap="goToList">修改已扫描捆包</view>
      </view>
    </view>
    <view class='discharge-all' bindtap="cheackOrder" style="height: 500rpx;overflow-y: auto;">
      <view class="scan-header">
        <view class="scan-header-li">顺序</view>
        <view class="scan-header-li">捆包号</view>
        <view class="scan-header-li">自带货标记</view>
        <!-- <view class="scan-header-li">个数</view> -->
      </view>
      <view class="scan-body" style="height: calc(100% - 90rpx);">
        <view class="scan-body-auto">
          <view class="scan-header" wx:for="{{scanList}}" wx:key="index" wx:for-item="item">
            <view class="scan-body-li">{{item.order}}</view>
            <view class="scan-body-li">{{item.packId}}</view>
            <view class="scan-body-li">{{item.outPackFlag==1 ? '是' : '否'}}</view>
            <!-- <view class="scan-body-li">{{item.piceNum}}</view> -->
          </view>
        </view>
      </view>

    </view>
    <view class="order-button" id="order-button">
      <view class="whole-order" bindtap="orderCompletion">配单完成</view>
    </view>
    <!-- 弹出层的信息确认 -->
    <t-toast id="t-toast" />
    <!-- 是否自带货弹窗 -->
    <t-overlay visible="{{overlayVisible}}" duration="{{500}}">
      <view class="overlay-con">
        <view class="overlay-main overlay-main-special">
          <view class="overlay-title">提示</view>
          <view class="overlay-tip">此捆包未有入库计划，请确认是否为自带货</view>
          <t-radio-group value="{{flag}}" bind:change="onChange" borderless t-class="box" data-key="flag" style="text-align: center;">
            <t-radio block="{{false}}" label="是" value="1" />
            <t-radio block="{{false}}" label="否" value="0" />
          </t-radio-group>
          <view class="overlay-button">
            <view class="overlay-button-cancle" bindtap="cancle">取消</view>
            <view class="overlay-button-confirm" bindtap="confirm">确定</view>
          </view>
        </view>
      </view>
    </t-overlay>
    <!-- 选装卸货配单号弹窗 -->
    <t-overlay visible="{{OrderNumberVisible}}" duration="{{500}}">
      <view class="overlay-con">
        <view class="overlay-main-radio">
          <view class="overlay-title">请选择要执行的配单号</view>
          <scroll-view scroll-y class="dialog-content" enhanced show-scrollbar="{{false}}" bindtouchstart="true">
            <radio-group bindchange="radioChange">
              <view class="radio-item" wx:for="{{vehicleNoList}}" wx:key="allocateVehicleNo">
                <label class="radio-label">
                  <radio value="{{item.allocateVehicleNo}}" checked="{{selectedAllocateVehicleNo == item.allocateVehicleNo}}" color="#0052d9" />

                  <view class="text-container">
                    <text class="radio-text">业务类型: {{item.allocTypeName}}</text>
                    <text class="radio-text">配单时间: {{item.recCreateTimeStr}}</text>
                    <text class="radio-text">客户名称: {{item. customerName}}</text>
                    <text class="radio-text">规格: {{item.specsDesc}}</text>
                    <text class="radio-text">总件数/重量: {{item.sumPiceNum}}/{{item.sumWeight}}</text>
                    <!-- <text class="radio-text">总件数: {{item.customerName}}</text> -->
                  </view>

                </label>
              </view>
            </radio-group>
          </scroll-view>

          <view class="overlay-button">
            <view class="overlay-button-cancle" bindtap="vehicleNoCancle">取消</view>
            <view class="overlay-button-confirm" bindtap="vehicleNoConfirm">确定</view>
          </view>
        </view>
      </view>
    </t-overlay>
  </view>

  <t-dialog visible="{{showTextAndTitle}}" title="提示" content="{{content}}" confirm-btn="{{ confirmBtn }}" bind:confirm="closeDialog" />
</view>