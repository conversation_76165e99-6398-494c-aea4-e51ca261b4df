// pages/order-allocation/my-review/my-review.js
import {
  Toast
} from 'tdesign-miniprogram';
const $api = require('../../../api/request')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {},
    dataList: [],
    auditRecords: [],
    oddNumbers: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      userInfo: wx.getStorageSync('userInfo')
    })
    this.queryReservation('', '10');
    this.queryReservation('', '20');
  },
  changeHandle(e) {
    this.setData({
      oddNumbers: e.detail.value
    });
  },
  actionHandle() {
    this.queryReservation(this.data.oddNumbers, '10');
  },
  clearValue(e) {
    this.queryReservation('', '10');
  },
  queryReservation(number, e) {
    var data = {
      "segNo": this.data.userInfo.segNo,
      "reservationNumber": number || '',
      "status": e
    }
    $api.request('S_LI_RL_0143', '', data).then((res) => {
      console.log(res)
      let arr = [];
      arr = res.reservationNumberList;
      console.log(arr, '哈哈哈');
      arr = arr.map(a => {
        const formattedDate = a.reservationDate.replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1/$2/$3 $4:$5:$6');
        return {
          ...a,
          reservationDateStr: formattedDate,
        }
      });
      if (e == '10') {
        this.setData({
          dataList: arr,
        })
      } else {
        this.setData({
          auditRecords: arr,
        })
      }
    }).catch((err) => {
      console.log(err)
    })
  },
  //   勾选数据
  cheackList(e) {
    let index = e.currentTarget.dataset.index;
    let dataList = this.data.dataList;
    dataList[index].flag = !dataList[index].flag
    this.setData({
      dataList: dataList
    })
    console.log(dataList)
  },
  // 提交预约审批单
  cheackOrder() {
    let cheackData = []
    let that = this
    cheackData = this.data.dataList.filter(item => {
      return item.flag == true
    })
    if (cheackData.length > 0) {
      var data = {
        result: cheackData,
        recCreator: this.data.userInfo.recCreator,
        recCreatorName: this.data.userInfo.recCreatorName
      }
      $api.request('S_LI_RL_0144', '', data).then((res) => {
        wx.showToast({
          title: res.__sys__.msg,
          duration: 1000,
        });
        setTimeout(() => {
          that.queryReservation("", "10")
          that.queryReservation("", "20")
        }, 1000);
      }).catch((err) => {
        console.log(err)
      })
    } else {
      wx.showToast({
        title: '请先勾选数据',
        icon: 'none',
        duration: 1000
      })
    }
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },
  onTabsChange(event) {
    console.log(`Change tab, tab-panel value is ${event.detail.value}.`);
  },

  onTabsClick(event) {
    console.log(`Click tab, tab-panel value is ${event.detail.value}.`);
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})