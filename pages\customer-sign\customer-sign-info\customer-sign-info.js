// pages/customer-sign/customer-sign-info/customer-sign-info.js
import Toast from 'tdesign-miniprogram/toast/index';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    data: {},
    value: [],
    options: [],
    originFiles: [],
    gridConfig: {
      column: 4,
      width: 160,
      height: 160,
    },
    showTextAndTitle: false,
    content: '', // 提示信息
    confirmBtn: { content: '确定', variant: 'base' },
    showPopup: false, // 控制 Popup 显示
    isDrawing: false, // 是否正在绘制
    ctx: null,        // Canvas 上下文
    canvas: null,     // Canvas 节点
    startX: 0,        // 起始点 X 坐标
    startY: 0,        // 起始点 Y 坐标
    isAllinPack: false,
    isDisable: false,
    latitude: '', // 纬度
    longitude: '', // 经度
    address: '',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const eventChannel = this.getOpenerEventChannel();
    if (!eventChannel) {
      return;
    }
    eventChannel.emit('acceptDataFromOpenedPage', { data: '' });
    eventChannel.on('acceptDataFromOpenerPage', (data) => {
      const resultData = data.data;
      this.setData({
        data: resultData,
        options: resultData.listPack.map(r => {
          return {
            label: r.packId,
            value: r.packId,
            content: `提单号: ${r.voucherNum},
            状态: ${r.signatureFlag},
            牌号: ${r.shopsign},
            规格: ${r.specDesc},
            品种: ${r.prodTypeId}`,
            maxLabelRow: 5,
          };
        }),
        isDisable: resultData.listPack[0].signatureFlag.includes('已签收'),
      });
    });
  },

  onChange(e) {
    this.setData({ value: e.detail.value });
  },

  handleSuccess(e) {
    const { files } = e.detail;
    this.setData({
      originFiles: files,
    });
  },

  handleRemove(e) {
    const { index } = e.detail;
    const { originFiles } = this.data;
    originFiles.splice(index, 1);
    this.setData({
      originFiles,
    });
  },



  /**
   * 捆包签收, 打开签名板
   */
  packSave() {
    const packIdList = this.data.value;
    if (packIdList.length == 0) {
      this.setData({
        showTextAndTitle: true,
        content: '请选择捆包',
      });
      return;
    }
    if (this.data.originFiles.length == 0) {
      this.setData({
        showTextAndTitle: true,
        content: '请上传附件',
      });
      return;
    }
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        const latitude = res.latitude;
        const longitude = res.longitude;
        this.setData({
          latitude,
          longitude,
        });
        this.getAddressFromAMap(latitude, longitude, false); // 调用高德接口获取地址
      }
    });

    // this.setData({
    //   showPopup: true,
    //   isAllinPack: false,
    // });
    // this.initCanvas();
  },

  /** 签名上传 */
  saveCanvas() {
    const { canvas, value, isAllinPack } = this.data;

    if (!canvas) {
      console.error('Canvas 未初始化！');
      return;
    }

    wx.canvasToTempFilePath({
      canvas, // 新 API 使用 canvas 对象
      success: (res) => {
        const tempFilePath = res.tempFilePath;
        console.log('生成的临时文件路径:', tempFilePath);
        // 整单签收不需要捆包id
        const idList = isAllinPack ? [] : value;
        this.imgtoBase64AndUpLoad(tempFilePath, this.data.data.putoutId, idList, true, '1');
      },
      fail(err) {
        console.error('保存图片失败:', err);
      },
    });
  },

  /** 图片上传 */
  async uploadImg() {
    const { originFiles, value, isAllinPack, data } = this.data;

    if (originFiles.length === 0) {
      wx.hideLoading();
      wx.showToast({
        title: '上传成功',
        duration: 1000,
      });
      // 整单签收和捆包签收同一个接口
      this.packSign();
      return;
    }

    const carTraceNo = data.listPack[0].carTraceNo;
    const idList = isAllinPack ? [] : value;

    try {
      for (const file of originFiles) {
        const filePath = file.url || file.tempFilePath; // 兼容 url 和 tempFilePath
        await this.uploadSingleImg(filePath, carTraceNo, idList);
      }

      // 所有图片上传成功后调用 packSign
      this.packSign();
    } catch (err) {
      console.error('上传图片失败:', err);
      wx.showToast({
        title: '上传失败',
        icon: 'error',
        duration: 1000,
      });
    }
  },

  /**
   * 签收成功之后再次上传
   */
  async separatUploadImg() {
    const { originFiles, value, isAllinPack, data } = this.data;
    if (originFiles.length === 0) {
      this.setData({
        showTextAndTitle: true,
        content: '请选择附件',
      });
      return;
    }

    const carTraceNo = data.listPack[0].carTraceNo;
    const idList = isAllinPack ? [] : value;

    try {
      wx.showLoading({
        title: '上传中',
      });
      // 并发上传所有图片
      const uploadPromises = originFiles.map((file) => {
        const filePath = file.url || file.tempFilePath; // 兼容 url 和 tempFilePath
        return this.uploadSingleImg(filePath, carTraceNo, idList);
      });

      await Promise.all(uploadPromises); // 等待所有上传完成
      wx.hideLoading();
      wx.showToast({
        title: '上传成功',
        icon: 'success',
      });
      this.setData({
        originFiles: [],
      })
    } catch (err) {
      console.error('上传图片失败:', err);
      wx.showToast({
        title: '上传失败',
        icon: 'error',
        duration: 1000,
      });
    }
  },

  /**
* 关闭错误提示框
*/
  closeDialog() {
    this.setData({
      showTextAndTitle: false,
    });
  },

  /** signatureMark：1是签名, 0是图谱 */
  imgtoBase64AndUpLoad(filePath, id, id2, isContinue, signatureMark) {
    wx.showLoading({
      title: '上传中',
    })
    const app = getApp();
    const fs = wx.getFileSystemManager();
    fs.readFile({
      filePath, // 临时路径
      encoding: 'base64', // 转换为 Base64
      success: (data) => {
        const base64Data = 'data:image/png;base64,' + data.data;
        wx.request({
          url: app.mesUrl,
          method: 'POST',
          timeout: '180000',
          data: {
            file: base64Data,
            id,
            id2,
            signatureMark,
            segNo: this.data.data.listPack[0].segNo,
            serviceId: 'S_LI_RL_0049',
            type: '3',
          },
          success: (res) => {
            if (isContinue) {
              this.uploadImg();
              return;
            }

            wx.hideLoading();
            wx.showToast({
              title: '上传成功',
              duration: 1000,
            });
          },
          fail(err) {
            wx.hideLoading();
            console.error('上传失败:', err);
          },
        });
      },
      fail(err) {
        wx.hideLoading();
        console.error('Base64 转换失败:', err);
      },
    });
  },

  /**
   * 捆包签收
   */
  packSign() {
    const app = getApp();
    wx.showLoading({
      title: '签收中',
    });
    const { listPack } = this.data.data;
    const packList = listPack.filter(l => this.data.value.includes(l.packId));
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        serviceId: 'S_LI_RL_0044',
        segNo: listPack[0].segNo,
        carTraceNo: listPack[0].carTraceNo,
        recCreator: listPack[0].recCreator,
        recCreatorName: listPack[0].recCreatorName,
        packList: this.data.isAllinPack ? [] : packList,
        putoutId: this.data.data.putoutId,
        quantity: this.data.data.quantity,
        weight: this.data.data.weight,
        customerName: this.data.data.customerName,
        vehicleNo: this.data.data.vehicleNo,
        signingLocationLongitude: this.data.longitude.toString(), // 经度
        signinglocationlatitude: this.data.latitude.toString(), //纬度
        signingAddress: this.data.address, // 签到地地址
      },
      success: (res) => {
        console.log(res, '签收');
        wx.hideLoading();
        if (!res || !res.data || !res.data.__sys__ || res.data.__sys__.status == -1) {
          Toast({
            context: this,
            selector: '#t-toast',
            message: res.data.__sys__.msg,
            theme: 'error',
            direction: 'column',
            duration: 3000,
          });
          return;
        }


        wx.showToast({
          title: '签收成功',
          duration: 1000,
          icon: 'success',
        });
        this.setData({
          showPopup: false,
          isAllinPack: false,
        });

        setTimeout(() => {
          wx.navigateBack({
            delta: 1,  // 返回上一页
            success: function () {
              // 在成功返回时，可以通过页面栈来调用A页面的刷新方法
              const pages = getCurrentPages(); // 获取页面栈
              const prevPage = pages[pages.length - 1]; // 获取上一页A页面
              if (prevPage) {
                prevPage.loadData(); // 调用A页面的刷新方法
              }
            }
          });
        }, 1000);
      },
      fail(err) {
        wx.hideLoading();
        console.error('签收失败:', err);
      },
    });
  },

  /**
   * 整单签收
   */
  entirOrderSign() {
    if (this.data.originFiles.length == 0) {
      this.setData({
        showTextAndTitle: true,
        content: '请上传附件',
      });
      return;
    }

    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        const latitude = res.latitude;
        const longitude = res.longitude;
        this.setData({
          latitude,
          longitude,
        });
        this.getAddressFromAMap(latitude, longitude, true); // 调用高德接口获取地址
      }
    });

    // this.setData({
    //   showPopup: true,
    //   isAllinPack: true,
    // });
    // this.initCanvas();
  },

  /**
   * 关闭签字板
   */
  onClose() {
    this.clearCanvas();
    this.setData({
      showPopup: false,
    });
  },

  // 初始化 Canvas
  initCanvas() {
    if (this.data.canvas && this.data.ctx) {
      console.log('Canvas 已初始化');
      return;
    }

    const query = wx.createSelectorQuery();
    query
      .select('#myCanvas')
      .node()
      .exec((res) => {
        if (!res[0]) {
          console.error('未找到 Canvas 节点！');
          return;
        }
        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');

        canvas.width = 300; // 设置画布宽度
        canvas.height = 300; // 设置画布高度
        ctx.lineWidth = 5; // 设置线条宽度（加粗线条）
        // 清空画布，确保透明背景
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        this.setData({ canvas, ctx });
      });
  },

  startDraw(e) {
    const { x, y } = e.touches[0];
    const ctx = this.data.ctx;

    if (!ctx) {
      console.error('Canvas Context 未初始化');
      return;
    }

    ctx.beginPath();
    ctx.moveTo(x, y);

    this.setData({
      isDrawing: true,
      startX: x,
      startY: y,
    });
  },

  moveDraw(e) {
    if (!this.data.isDrawing) return;

    const { x, y } = e.touches[0];
    const ctx = this.data.ctx;

    ctx.lineTo(x, y);
    ctx.stroke(); // 只绘制线条，不填充背景

    this.setData({ startX: x, startY: y });
  },

  endDraw() {
    this.setData({ isDrawing: false });
  },

  clearCanvas() {
    const ctx = this.data.ctx;
    const canvas = this.data.canvas;

    if (!ctx || !canvas) {
      console.error('Canvas 或 Context 未初始化');
      return;
    }

    ctx.clearRect(0, 0, canvas.width, canvas.height);
  },

  /** 上传单张图片并返回 Promise */
  uploadSingleImg(filePath, id, id2) {
    return new Promise((resolve, reject) => {
      const app = getApp();
      const fs = wx.getFileSystemManager();

      fs.readFile({
        filePath, // 临时路径
        encoding: 'base64', // 转换为 Base64
        success: (data) => {
          const base64Data = 'data:image/png;base64,' + data.data;
          wx.request({
            url: app.mesUrl,
            method: 'POST',
            timeout: '180000',
            data: {
              serviceId: 'S_LI_RL_0049',
              type: '3',
              file: base64Data,
              id,
              id2,
              signatureMark: '0', // 图谱标识
              segNo: this.data.data.listPack[0].segNo,
            },
            success: (res) => {
              console.log('上传成功:', res);
              resolve(res); // 上传成功
            },
            fail: (err) => {
              console.error('上传失败:', err);
              reject(err); // 上传失败
            },
          });
        },
        fail: (err) => {
          console.error('Base64 转换失败:', err);
          reject(err); // 转换失败
        },
      });
    });
  },


  // 调用高德逆地理编码 API
  getAddressFromAMap(latitude, longitude, isFromAll) {
    wx.showLoading({
      title: '解析地址中',
    })
    const url = `https://restapi.amap.com/v3/geocode/regeo?key=${getApp().amapKey}&location=${longitude},${latitude}&extensions=all`;
    console.log(latitude, longitude);
    wx.request({
      url,
      method: 'GET',
      success: (res) => {
        wx.hideLoading();
        console.log("高德API返回的数据：", res);
        if (res.data.status === '1') {
          const address = res.data.regeocode.formatted_address; // 获取详细地址
          this.setData({
            address,
          });
          this.setData({
            showPopup: true,
            isAllinPack: isFromAll,
          });
          this.initCanvas();
        } else {
          console.error("高德API解析失败:", res.data);
          wx.showToast({
            title: '地址解析失败',
            icon: 'none',
          });
        }
      },
      fail: (err) => {
        console.error("请求高德API失败:", err);
        wx.showToast({
          title: '请求失败',
          icon: 'none',
        });
      }
    });
  }
})