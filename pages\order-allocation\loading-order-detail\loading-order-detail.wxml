<!--pages/order-allocation/loading-order-detail/loading-order-detail.wxml-->
<view class="container">
  <view class="scroll-container">
    <view class="order-scroll {{dataList.length>0 ? '' : 'dataList-empty' }}">
      <view wx:for="{{dataList}}" wx:key="index" wx:for-item="item">
        <view class='order-list'>
          <view class="list-name" bindtap="cheackList" data-index="{{index}}">
            <view class="list-name-checked">
              <image src="/assets/image/icon_UncheckBox.png" class='cheacked-img' wx:if="{{!item.flag}}"></image>
              <image src="/assets/image/icon_checkBox.png" class='cheacked-img' wx:if="{{item.flag}}"></image>
            </view>
            <view class="list-name-number">{{item.ladingBillId}}</view>
          </view>
          <view class="list-name-start">捆包号：{{item.packId}}</view>
          <view class="list-name-start">销售订单子项号：{{item.orderNum}}</view>
          <view class="list-name-start">钢厂资源号：{{item.factoryOrderNum}}</view>
          <view class="list-name-start">母卷号：{{item.m_packId}}</view>
          <view class="list-name-start">客户零件号：{{item.custPartId}}</view>
          <view class="list-name-start">客户零件号名称：{{item.custPartName}}</view>
          <view class="list-name-start">库位名称：{{item.locationId}}</view>
          <view class="list-name-cloum">
            <view class="list-name-cloum-one">品种：{{item.prodTypeId}}</view>
            <view class="list-name-cloum-one">规格：{{item.specsDesc}}</view>
            <view class="list-name-cloum-one">牌号：{{item.shopsign}}</view>
            <view class="list-name-cloum-one">净重：{{item.netWeight}}</view>
            <view class="list-name-cloum-one">毛重：{{item.grossWeight}}</view>
            <view class="list-name-cloum-one">件数：{{item.pieceNum}}</view>
          </view>
        </view>
      </view>
      <view wx:if="{{!dataList.length>0}}">
        <image src="/assets/image/empty_data.png" class="my-order-img"></image>
        <view class="my-order-text">暂无数据</view>
      </view>
    </view>
  </view>
  <view class="order-button" id="order-button">
    <view class="whole-order" bindtap="cheackOrder">配单</view>
  </view>
</view>