/* pages/order-allocation/modify-packaging/modify-packaging.wxss */
.container{
    width: 100%;
    height: 100%;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    box-sizing: border-box;
}
.scroll-container{
    width: 100%;
    height: calc(100% - 100rpx);
    flex-grow:1;
}
.order-scroll{ 
    width: 100%;
    height: 100%;
    overflow: auto;
}
.dataList-empty{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.order-list{
    width:calc(100% - 64rpx);
    padding:20rpx 30rpx;
    background-color: #ffffff;
    margin: 0 auto 20rpx;
    border-radius: 20rpx;
    box-sizing: border-box;
    position: relative;
}
.edit-infor{
    position: absolute;
    right: 20px;
    top:calc(50% - 10px);
    width: 20px;
    height: 20px;
}
.list-name{
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    align-content: center;
    margin-bottom: 10rpx;
}
.list-name-checked image{
    width: 36rpx;
    height: 36rpx;
    display: flex;
    align-items: center;
}
.list-name-number{
    font-size: 32rpx;
    color: #333333;
    font-weight: 500;
    margin-left: 10rpx;
}
.list-name-start{
    font-size: 28rpx;
    color: #666666;
    line-height: 40rpx;
    margin-bottom: 10rpx;
}
.order-button{
    width: 100%;
    height: 100rpx;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
} 
.whole-order{
    width:90%;
    height:64rpx;
    background-color: rgba(255, 0, 0, 0.849);
    color: #ffffff;
    text-align: center;
    line-height: 64rpx;
    margin: 0 auto;
    font-size: 28rpx;
    border-radius: 8rpx;
}
.my-order-img{
    width: 150px;
    height: 150px;
    display: block;
}
.my-order-text{
    text-align: center;
	font-size: 16px;
	color: rgb(51, 51, 51);
}
.overlay-con{
    width: 100%;
    height: 100%;
    display: flex;
	flex-direction: column;
    align-items: center;
    justify-content: center;
}
.overlay-main{
    width:80%;
    height: auto;
    background-color: #ffffff;
    margin:0 auto;
    border-radius:10px;
    overflow: hidden;
}
.overlay-title{
    text-align: center;
    line-height: 2;
    font-size: 18px;
    margin-top: 5px;
}
.overlay-one{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding:16px 16px 16px 0;
    border-bottom:1px solid  #cccccc57;
    margin-left: 16px;
}
.overlay-button{
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: space-around;
    margin-top: 2px;
}
.overlay-button-cancle,.overlay-button-confirm{
    width: 50%;
    text-align: center;
    color: #ffffff;
    line-height: 50px;
    box-sizing: border-box;
}
.overlay-button-cancle{
  background-color: #cccccc;
}
.overlay-button-confirm{
    background-color: #1953E6;
}
