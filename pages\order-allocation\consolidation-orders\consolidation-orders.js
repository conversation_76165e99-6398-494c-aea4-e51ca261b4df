// pages/order-allocation/consolidation-orders/consolidation-orders.js
import Toast from 'tdesign-miniprogram/toast/index';
const $api = require('../../../api/request')
Page({

    /**
     * 页面的初始数据
     */
    data: {
        userInfo:{}, 
        dataList:[],
       pageNum: 0, //当前第几页
       pageSize: 6, //一页展示几条
       loading: false, //是否展示 “正在加载” 字样
       loaded: false, //是否展示 “已加载全部” 字样 
       triggered:false,
       loadingMore:false,
       ladingBillId:''
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        console.log(wx.getStorageSync('userInfo'))
        this.setData({
            userInfo:wx.getStorageSync('userInfo')
        }) 
        if(this.data.pageNum>0){
            this.setData({
                pageNum:0,
                loading: false, //是否展示 “正在加载” 字样
                loaded: false, //是否展示 “已加载全部” 字样 
                triggered:false,
                loadingMore:false,
                dataList:[]
            }) 
        }
        this.getList()    
    },
    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },
    backRefresh(){
        if(this.data.pageNum>0){
            this.setData({
                pageNum:0,
                loading: false, //是否展示 “正在加载” 字样
                loaded: false, //是否展示 “已加载全部” 字样 
                triggered:false,
                loadingMore:false,
                dataList:[]
            }) 
        }
        this.getList()   
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {

    },
  // 下拉刷新
  onRefresh() {
    this.setData({
        loaded: false,  
        loading: false,
        pageNum:0,
        triggered: true,
        dataList:[]
    }); 
    this.getList();
  },
    // 上拉到底部触发
    loadMore: function () {
        // if(!this.data.loaded&&!this.data.loadingMore){
        //     this.setData({
        //         pageNum:this.data.pageNum+1,
        //         loadingMore:true
        //     });
        //     this.getList();
        // } 
    },
    // 搜索框
    changeHandle(e) {
        this.setData({
            ladingBillId:e.detail.value
        });
    },
    actionHandle() {
        this.setData({
            pageNum:0,
            loading: false, //是否展示 “正在加载” 字样
            loaded: false, //是否展示 “已加载全部” 字样 
            triggered:false,
            loadingMore:false,
            dataList:[]
        })
        this.getList();
      },
    // 获取数据
    getList(){
        var that=this
        wx.showLoading({
            title: '加载中...',
        });
        let data = {
            segNo:this.data.userInfo.segNo,
            driverTel:this.data.userInfo.tel,
        }
        $api.request('S_LI_RL_0142','',data).then((res) => {
            wx.hideLoading();
            that.setData({
                dataList:res.list
            })
        }).catch((err) => {	
			console.log(err)					
        })
    },
    cheackList(e){
        let index=e.currentTarget.dataset.index;  
        let dataList = this.data.dataList;
        dataList[index].flag=!dataList[index].flag
        this.setData({
            dataList: dataList
        })
    },
    cheackOrder(){
        let cheackData=[]
        let that=this
        cheackData = this.data.dataList.filter(item => {
            return item.flag==true
        })
        console.log(cheackData)
		if(cheackData.length>1){
            var data = {
                result: cheackData
            }
            $api.request('S_LI_RL_0141','',data).then((res) => {
                wx.hideLoading();
                wx.showToast({
                    title: res.__sys__.msg,
                    icon: 'none',
                    duration: 1000
                })
                that.onRefresh()
            }).catch((err) => {	
                console.log(err)					
            })
        }else{
            wx.showToast({
                title: '请勾选2条以上的数据',
                icon: 'none',
                duration: 1000
            })  
        }		
    },
    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {
    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {
    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    },
})