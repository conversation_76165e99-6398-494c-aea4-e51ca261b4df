<!--pages/order-allocation/consolidation-orders/consolidation-orders.wxml-->
<wxs module="threeNumForTwo" src="/utils/tool.wxs"></wxs>
<view class="container">
    <view class="{{dataList.length>0 ? 'scroll-container' : 'dataList-empty' }}">
        <scroll-view class="order-scroll"
        wx:if="{{dataList.length>0}}"
        scroll-y="true" 
        refresher-enabled="{{true}}" 
        refresher-threshold="{{100}}"
        refresher-background="#f6f6f6"
        refresher-default-style="black"
        refresher-triggered="{{triggered}}"
        bindrefresherrefresh="onRefresh"  
        bindscrolltolower="loadMore"
        >
            <view wx:for="{{dataList}}" wx:key="index" wx:for-item="item">
                <view class='order-list'>
                    <view class="list-name" catchtap="cheackList" data-index="{{index}}">
                        <view class="list-name-checked">
                            <image src="/assets/image/icon_UncheckBox.png" class='cheacked-img' wx:if="{{!item.flag}}"></image>
                            <image src="/assets/image/icon_checkBox.png" class='cheacked-img' wx:if="{{item.flag}}"></image>
                        </view> 
                        <view class="list-name-number">{{item.allocateVehicleNo}}</view>
                    </view>
                    <view class="list-name-start">车牌号:{{item.vehicleNo}}</view>
                    <view class="list-name-start">承运商名称:{{item.customerName}}</view>
                    <view class="list-name-start">捆包个数:{{item.packCount}}</view>
                    <!-- <view class="list-name-cloum">
                        <view class="list-name-start list-name-flex">提单量：{{threeNumForTwo.money_three_for_two_thousands(item.totalWeight)}}</view>
                        <view class="list-name-start list-name-flex">提单件数：{{item.totalPackQty}}</view>
                    </view>  -->
                </view>
            </view>
            <!-- 加载时文字 -->
            <view class="bomTxt">
                <view hidden="{{!loading}}">正在加载...</view>
                <view hidden="{{!loaded}}">已加载全部</view>
            </view>
        </scroll-view>
        <view wx:if="{{!dataList.length>0}}">
            <image src="/assets/image/empty_data.png" class="my-order-img"></image>
            <view class="my-order-text">暂无数据</view>
        </view>
    </view> 
    <view class="order-button" id="order-button">
        <view class="whole-order" bindtap="cheackOrder">配单合并</view>
    </view>
</view>

