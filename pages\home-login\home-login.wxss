/* pages/home-login.wxss */
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.main-btn {
  width: 80vw;
  margin-bottom: auto;
}

.wechat-login {
  margin-bottom: 24rpx;
}

.agreement {
  margin-top: 1em;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
  white-space: nowrap;
  /* 防止换行 */
}

.link {
  color: #007AFF;
  text-decoration: none;
}

.t-radio__icon--left {
  margin-right: 0;
}

.slogan-container {
  margin-top: 1em;
}

.col {
  /* position: absolute; */
  /* top: 10%; */
  margin-top: 5em;
  text-align: center;
  margin-bottom: 5em;
}

.company-name-container {
  display: flex;
  justify-content: space-between;
  width: 100%;
  /* padding: 0 10px; */
  /* 为两边加上间隙 */
}

.company-name {
  font-size: 18px;
  /* 字体大小 */
  font-weight: bold;
  color: #333;
  /* 工业化颜色 */
}

.slogan-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  /* padding: 0 10px; */
  /* 为两边加上间隙 */
  margin-top: 0.8em;
}

.slogan-text-container {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 0 10px;
}

.slogan-text {
  font-size: 14px;
  /* 设置字体大小 */
  color: #888;
  /* 工业化颜色 */
  margin-right: 5px;
  /* 每个字之间的间隙 */
}

.vertical-divider {
  width: 1px;
  height: 20px;
  background-color: #ccc;
  /* 分割线颜色 */
  margin: 0 10px;
}

/* index.wxss */
.guide-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 40rpx 0;
  padding: 20rpx 0;
  border-radius: 12rpx;
}

.guide-item {
  padding: 20rpx 40rpx;
  transition: opacity 0.3s;
}

.guide-item:active {
  opacity: 0.7;
}

.guide-text {
  color: #007AFF;
  font-size: 28rpx;
  font-weight: 500;
}

.divider {
  width: 1rpx;
  height: 32rpx;
  background-color: #ddd;
}

/* 新用户注册按钮 */
.register-link {
  text-align: right;
  font-size: 14px;
  color: #007AFF;
  font-weight: 100;
  /* cursor: pointer; */
  margin-top: 26rpx;
}

/* 说明文字样式 */
.notice-text {
  text-align: center;
  margin: 20rpx 0;
  padding: 0 20rpx;
}

.notice-text text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

/* 版本号显示样式 */
.version-container {
  position: fixed;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  padding: 16rpx 32rpx;
  z-index: 100;
}

.version-text {
  font-size: 24rpx;
  color: #007AFF;
  font-weight: 400;
  text-align: center;
}

