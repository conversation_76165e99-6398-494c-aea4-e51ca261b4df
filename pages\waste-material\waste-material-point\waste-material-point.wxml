<!--pages/waste-material/waste-material-point/waste-material-point.wxml-->
<view class="container">
  <view class="form-content">
    <t-cell-group>
      <!-- 车辆选择 -->
      <t-cell class="cell-item" title="选择车辆" arrow hover note="{{selectedCar}}" bind:click="selectCar" t-class-note="note-text" required />

      <!-- 装卸点选择 -->
      <t-cell class="cell-item" title="选择装卸点" arrow hover note="{{selectedLoadingPoint}}" bind:click="selectLoadingPoint" t-class-note="note-text" required />
    </t-cell-group>
    
    <!-- 已选择的装卸点详情 -->
    <view wx:if="{{selectedPointNames.length > 0}}" class="selected-points-tip">
      <text>已选择：{{selectedPointNames}}</text>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <t-button theme="primary" size="large" block loading="{{submitLoading}}" bind:tap="handleSubmit">
        {{submitLoading ? '提交中...' : '提交'}}
      </t-button>
    </view>
  </view>
</view>

<!-- 车辆选择器 -->
<t-picker visible="{{carVisible}}" value="{{selectedCarValue}}" title="选择车辆" cancelBtn="取消" confirmBtn="确认" usingCustomNavbar bindchange="onCarChange">
  <t-picker-item options="{{carList}}" />
</t-picker>

<!-- 装卸点多选对话框 -->
<t-dialog 
  visible="{{loadingPointVisible}}" 
  title="请选择装卸点" 
  cancel-btn="取消" 
  confirm-btn="确认" 
  bind:confirm="confirmLoadingPoints" 
  bind:cancel="cancelLoadingPoints"
>
  <view slot="content">
    <scroll-view scroll-y class="dialog-content" enhanced show-scrollbar="{{false}}" bindtouchstart="true">
      <t-checkbox-group 
        value="{{selectedLoadingPointValue}}" 
        bind:change="onLoadingPointChange"
      >
        <t-checkbox 
          wx:for="{{loadingPointList}}" 
          wx:key="value" 
          value="{{item.value}}" 
          label="{{item.label}}" 
        />
      </t-checkbox-group>
    </scroll-view>
  </view>
</t-dialog>

<!-- Toast 提示 -->
<t-toast id="t-toast" />