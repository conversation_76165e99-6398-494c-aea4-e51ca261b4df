<!--custom-tab-bar/index.wxml-->
<view>
  <!-- slot实现自定义content内容 -->
  <t-notice-bar visible="{{isVisible}}">
    <view slot="content" class="inline"> 有司机待审核 </view>
    <t-link slot="operation" content="跳转" theme="primary" underline="{{false}}" navigator-props="{{navigatorProps}}" />
    <t-icon slot="suffix-icon" name="close" size="44rpx" bind:tap="clearNoticBar"></t-icon>
  </t-notice-bar>


  <view id="tabbar-box">
    <t-tab-bar value="{{value}}" t-class="t-tab-bar" defaultValue="0" split="{{true}}" fixed="{{false}}" bindchange="onChange">
      <t-tab-bar-item value="0" icon="home">首页</t-tab-bar-item>
      <t-tab-bar-item value="1" icon="user">我的</t-tab-bar-item>
    </t-tab-bar>
  </view>
  <view style="height: {{screenHeight}}px;overflow-y: hidden;">
    <!-- 首页内容 -->
    <view wx:if="{{currentTab == 0}}" style="height: 100%;overflow-y: auto;">
      <home-page />
    </view>

    <!-- 我的内容 -->
    <view wx:if="{{currentTab == 1}}">
      <my-page id="myComponent" />
    </view>
  </view>
</view>