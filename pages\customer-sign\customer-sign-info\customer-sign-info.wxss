.theme-card {
  border-radius: 24rpx;
  margin: 32rpx;
  overflow: auto;
  height: 720rpx;
}

page {
  overflow-y: hidden;
}

/* 底部容器样式 */
.bottom-container {
  position: fixed;
  bottom: 0;
  width: 100%;
  background-color: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  padding-bottom: 20px;

}

.plate-button-wrapper {
  margin: 32rpx;
}

button {
  margin-bottom: 18rpx;
}

.wrapper {
  margin-bottom: 18rpx;
  max-height: 200rpx;
  overflow-y: auto;
}

/* 签字板 */
.canvas-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.buttons {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.block {
  position: absolute;
  left: 50%;
  margin-left: -32rpx;
  bottom: calc(-1 * (48rpx + 64rpx));
}

.t-class-content1 {
  font-size: 16px !important;
  color: var(--td-checkbox-title-color, var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))) !important;
}