// pages/order-allocation/reservation-factory/reservation-factory.js
import Message from 'tdesign-miniprogram/message/index';
import { Toast } from 'tdesign-miniprogram';
const $api = require('../../../api/request')
Page({

    /**
     * 页面的初始数据
     */
    data: {
        reserveCommpy:"",
        copyName:"请选择拜访单位",
        copyVisible:false,
        copyList:[],
        // copyList:[
        //     {name:'智慧停车',value:'1',flag:false,
        //     detailList:[
        //         {name:'重庆宝钢本部',value:'10',flag:false,detailList:[{name:'综合财务部',value:'100',flag:false,detailList:[{name:'宝伟重庆分公司',value:'1000',detailList:[]}]},{name:'业务部',value:'10001',flag:false,detailList:[]}]},
        //         {name:'板材（港城）作业区',value:'11',flag:false,detailList:[{name:'板材（港城）作业区预约点此处',value:'110',flag:false,detailList:[]}]},
        //         {name:'巴南作业区',value:'12',flag:false,detailList:[{name:'巴南作业区预约点此处',value:'120',flag:false,detailList:[]}]}
        //     ]}
        // ],
        styleFlag:"",
        appointmentPerson:"",
        showDialogCompany:false,
        gender:"请选择您的性别",
        appointmentGender:"男",
        appointmentPhone:"",
        appointmentId:"",
        appointmentStart:"请选择预约时间",
        appointmentEnd:"请选择截止时间",
        datetimeVisible:false,
        appointmentStartTime:"",
        appointmentEndTime:"",
        dataFlag:"",
        shartTime:"",
        appointmentRemrk:"",
        originFiles: [
            // {
            //   url: 'https://tdesign.gtimg.com/mobile/demos/example4.png',
            //   name: 'uploaded1.png',
            //   type: 'image',
            // }
        ],
        gridConfig: {
            column: 1,
            width: 150,
            height: 200,
          },
          imgBase:''
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        console.log(wx.getStorageSync('userInfo'))
        var timestamp = Date.parse(new Date());
        var date = new Date(timestamp);
        //获取年份  
        var Y =date.getFullYear();
        //获取月份
        var M =date.getMonth()+1;
        //获取当前天
        var D = date.getDate();
        //获取当前小时
        var H = date.getHours();
        var m=new Date().getMinutes();
        var H2=date.getHours()+2;
        let time=new Date().toJSON().substring(0, 10)+' '+(H<10?'0'+H:H)+':'+(m<10?'0'+m:m)
        let endTime=new Date().toJSON().substring(0, 10)+' '+(H2<10?'0'+H2:H2)+':'+(m<10?'0'+m:m)
        // 获取拜访单位列表
        this.getCopyList()
        this.setData({
            appointmentStart:time,
            appointmentStartTime:time,
            appointmentEnd:endTime,
            appointmentEndTime:endTime
        })
    },
    
    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {

    },
    // 获取拜访单位列表
    getCopyList(){
        // console.log('1111')
        var data = {
            serviceId: "S_LI_RL_0094",
            segNo: wx.getStorageSync('userInfo').segNo,
       } 
       $api.request('S_LI_RL_0094','',data).then((res) => {
            console.log(res)
            if(res.visitUnit){
                this.setData({
                    copyList:[res.visitUnit]
                })
                // this.addFlag(this.data.copyList)
            }
       }).catch((err) => {	
        console.log(err)					
    })
    },
    // 添加拜访单位选中标识
    // addFlag(data){
    //     console.log(data)
    // },
    // 选拜访单位
    getValue(event){
        // 最外面的父组建事件
        // console.log('进来了')
        this.setData({
            styleFlag:event.detail.objOne.visitUnitCode
        })
        // 判断当前的id是不是能选的
        if(this.data.styleFlag==='ZHTC'){
                Message.info({
                  context: this,
                  offset: ['180rpx', 32],
                  content: '不能预约此部门,请重新选择预约的部门!',
                  duration: 4000,
                  closeBtn: true,
                });
        }else{
            this.setData({
                reserveCommpy:event.detail.objOne.visitUnitName,
                copyName:event.detail.objOne.visitUnitName,
            })   
        }
    },
    getId(event){
        let list= this.formatList(this.data.copyList,event.detail.id)
        this.setData({
            copyList: list
        })
    },
    formatList(list,id) {
        circle(list)
        function circle(_list) {
          _list.forEach(item => {
            if (item.detailList && item.detailList.length) {
                if (item.value === id) {
                    item.flag = !item.flag
                }
              circle(item.detailList)
            }
          })
        }
        return list
      },
    chooseCopyName(){
        this.setData({
            copyVisible: true
        });
    },
    onClose(){
        this.setData({
            copyVisible: false
        }); 
    },
    onVisibleChange(){
        this.setData({
            copyVisible: false
        }); 
    },
    // 姓名
    onAppointmentPerson(e){
        console.log(this.data.appointmentPerson)
        this.setData({
            appointmentPerson:e.detail.value,
        })
        // console.log(this.data.appointmentPerson)
    },
    // 性别
    onChangeGender(e){
        this.setData({
            appointmentGender:e.detail.value,
        }) 
         console.log(this.data.appointmentGender) 
    },
    // 手机号
    onAppointmentPhone(e){
        this.setData({
            appointmentPhone:e.detail.value,
        }) 
         console.log(this.data.appointmentPhone) 
    },
    // 身份证
    onAppointmentId(e){
        this.setData({
            appointmentId:e.detail.value,
        }) 
         console.log(this.data.appointmentId) 
    },
    //选择预约时间 
    selectTime(e){
        console.log(e.currentTarget.dataset.flag)
        this.setData({
            dataFlag:e.currentTarget.dataset.flag,
            datetimeVisible:true,
        })
        if(e.currentTarget.dataset.flag=='start'){
                this.setData({
                    shartTime:this.data.appointmentStartTime
                })
        }else{
            this.setData({
                shartTime:this.data.appointmentEndTime
            })
        }
    },
    // 确定选择时间
    onConfirm(e){
        console.log(e)
        console.log(e.detail)
        if(this.data.dataFlag=='start'){
            this.setData({
                appointmentStart:e.detail.value,
                appointmentStartTime:e.detail.value 
            })
        }else{
            this.setData({ 
                appointmentEnd:e.detail.value,
                appointmentEndTime:e.detail.value
            }) 
        } 
    },
    onRemrk(e){
        console.log(e.detail.value)
        this.setData({
            appointmentRemrk: e.detail.value 
        })
    },

    handleClick(e){
        console.log("测试")
        console.log(e)
    },
    handleRemove(e){
        console.log("移除事件")
        console.log(e)
        this.setData({
            originFiles:[],
            imgBase:""
        });
    },
    handleSuccess(e) {
        console.log(e)
        const { files } = e.detail;
        this.setData({
            originFiles: files
        });
        console.log(files)
        // 把图片转化为base64
        console.log(this.data.originFiles[0].url)
        wx.getFileSystemManager().readFile({ //读取本地文件内容
            filePath: this.data.originFiles[0].url, // 文件路径
            encoding: 'base64', // 返回格式
            success: res => { //成功的回调
                console.log(res)
                this.setData({
                    imgBase:'data:image/png;base64,'+res.data
                })
                console.log('读取到的文件', 'data:image/png;base64,'+res)
            }
          });
      },
    // selectimg(){
    //     let that=this;
    //     wx.chooseMedia({
    //         count: 2,
    //         mediaType: ['image','video'],
    //         sourceType: ['album', 'camera'],
    //         maxDuration: 30,
    //         camera: 'back',
    //         success(res) {
    //             that.setData({
    //                 originFiles: [
    //                     {
    //                       url: res.tempFiles[0].tempFilePath,
    //                       name: 'uploaded1.png',
    //                       type: 'image',
    //                     }
    //                 ]  
    //             })
    //           console.log(res.tempFiles[0].tempFilePath)
    //           console.log(res.tempFiles[0].size)
    //         }
    //       })
    // },
    confirmAppointment(){
        // 先判断数据是否填写完整
        if(this.checkForm()){
            var data = {
                serviceId: "S_LI_RL_0090",
                result:[{
                    segNo:wx.getStorageSync('userInfo').segNo,
                    visitUnitCode:this.data.styleFlag,
                    visitUnitName:this.data.reserveCommpy,
                    visitorName:this.data.appointmentPerson,
                    visitorTel:this.data.appointmentPhone,
                    visitorIdentity:this.data.appointmentId,
                    visitorGender:this.data.appointmentGender,
                    reservationDate:this.data.appointmentStartTime,
                    reservationDateEnd:this.data.appointmentEndTime,
                    remark:this.data.appointmentRemrk,
                    recCreator:wx.getStorageSync('userInfo').recCreator,
                    recCreatorName:wx.getStorageSync('userInfo').recCreatorName,
                }],
                fileList:[this.data.imgBase]
                           
           } 
           $api.request('S_LI_RL_0090','',data).then((res) => {
                this.showToast(res.__sys__.msg)  
           }).catch((err) => {	
            console.log(err)					
        })
        }
        // console.log(this.data.appointmentRemrk)
    },
    checkForm(){
        console.log(this.data.appointmentId)
        const app = getApp();
        let flag=false
        if(this.data.reserveCommpy){
            if(this.data.appointmentPerson){
                if(this.data.appointmentPhone&&app.isPhone(this.data.appointmentPhone)){
                    if(this.data.appointmentId&&app.regIdCard(this.data.appointmentId)){
                        if(this.data.imgBase){
                            flag=true
                            return flag   
                        }else{
                            this.showToast('请上传门禁照片') 
                            return flag   
                        }
                        // 判断上传照片
                    }else{
                        this.showToast('请正确的填写身份证号码') 
                        return flag  
                    }
                }else{
                    this.showToast('请正确的填写手机号码') 
                    return flag  
                }
            }else{
                this.showToast('请填写姓名') 
                return flag 
            }
        }else{ 
            this.showToast('请选择拜访单位') 
            return flag
        }
    },
    showToast(e){
        Toast({
            context: this,
            selector: '#t-toast',
            message: e,
          }); 
    },
    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    }
})