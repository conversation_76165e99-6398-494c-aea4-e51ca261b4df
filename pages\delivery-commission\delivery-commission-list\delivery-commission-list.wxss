/* pages/delivery-commission/delivery-commission-list/delivery-commission-list.wxss */
.example-search {
  background-color: var(--bg-color-demo);
  padding: 16rpx 32rpx;
}

.value {
  max-width: 70%;
}

/* 容器样式 */
.filter-container {
  display: flex;
  align-items: center;
  /* 垂直居中 */
  justify-content: space-between;
  /* 两端对齐 */
  width: 100%;
  /* 占满父容器宽度 */
  padding: 16rpx 24rpx;
  /* 适当内边距 */
}

/* 下拉菜单样式 */
.dropdown-menu {
  /* flex: 1; */
  /* 占据剩余空间 */
  margin-right: 24rpx;
  /* 与按钮间距 */
  background-color: transparent;
}

/* 筛选按钮容器 */
.filter-btn-wrapper {
  flex-shrink: 0;
  /* 禁止收缩 */
}

/* 筛选内容区域 */
.filter-content {
  padding: 24rpx;
}

/* 底部操作栏 */
.filter-footer {
  display: flex;
  padding: 24rpx;
  gap: 24rpx;
}

.filter-footer t-button {
  flex: 1;
}

/* 调整组件间距 */
.t-cell-group {
  margin-bottom: 32rpx;
}

.t-drawer__sidebar {
  height: 0 !important;
}

.aaa {
  display: flex;
  flex-direction: column;
  /* 使子元素垂直排列 */
}

.example-search-popup {
  order: 1;
  /* 搜索框在上方 */
  width: 100%;
  position: relative;
  /* 改为相对定位 */
  z-index: 2;
}

.example-picker {
  order: 2;
  /* 选择器在下方 */
  width: 100%;
  position: relative;
  z-index: 1;
  margin-top: 0;
  /* 移除原有 margin */
}

/* 图标容器，使图标在右侧并垂直居中 */
.icon-container {
  text-align: right;
}

.app-t-class {
  text-align: right;
}

/* 分页状态优化 */
.pagination-status {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx;
  color: #666;
  font-size: 24rpx;
  text-align: center;
}

.pagination-status .divider {
  width: 1px;
  height: 24rpx;
  background: #ddd;
  margin: 0 20rpx;
}