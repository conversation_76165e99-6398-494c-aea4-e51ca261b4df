/* 容器 */
.container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  background-color: #f7f7f7;
}

/* 顶部用户信息区 */
.user-info {
  display: flex;
  background: linear-gradient(to right, #ffd700, #ffcc00);
  padding: 20rpx;
  align-items: center;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-details {
  flex: 1;
}

.username {
  font-size: 32rpx;
  font-weight: bold;
  color: #000;
}

.phone {
  font-size: 26rpx;
  color: #666;
}

/* 主要操作功能区 */
.action-area {
  flex: 1;
  padding: 20rpx;
}

.card {
  background-color: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 28rpx;
  color: #000;
}

.count {
  margin-top: 10rpx;
  font-size: 32rpx;
}

.red {
  color: #e54d42;
}

.orange {
  color: #ff9900;
}

.subtitle {
  font-size: 24rpx;
  color: #888;
  margin-top: 10rpx;
}

/* 底部版本信息 */
.footer {
  text-align: center;
  padding: 20rpx;
  color: #aaa;
}

/* 底部导航栏 */
.bottom-nav {
  display: flex;
  justify-content: space-around;
  background-color: #fff;
  padding: 10rpx 0;
  border-top: 1rpx solid #eee;
}

.nav-item {
  text-align: center;
  flex: 1;
}

.icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.selected .icon {
  filter: brightness(1.5);
}

.selected text {
  color: #ffd700;
}

text {
  color: #666;
  font-size: 24rpx;
}

.avatar-example:not(:last-child) {
  margin-right: 64rpx;
}

.external-class-content {
  color: #fff;
  background-color: var(--td-brand-color, #0052d9);
  font-weight: 400;
}

/* custom-tab-bar/index.wxss */
#tabbar-box {
  position: fixed;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 9999;
}

.inline {
  display: inline;
}