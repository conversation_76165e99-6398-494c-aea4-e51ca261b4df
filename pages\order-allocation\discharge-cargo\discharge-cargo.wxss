/* pages/order-allocation/discharge-cargo/discharge-cargo.wxss */

/* 车牌号选择器样式 */
.car-selector {
  position: relative;
}

.car-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  overflow: hidden;
}

.car-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

.search-box {
  padding: 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.car-selector .search-input {
  background: white !important;
  border: 2px solid #e3f2fd !important;
  border-radius: 25px !important;
  padding-left: 20px !important;
  height: 44px !important;
  font-size: 16px !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  text-align: left !important;
}

/* 确保搜索框内的文本输入区域也是左对齐 */
.car-selector .search-input .t-input__control {
  text-align: left !important;
}

/* 覆盖全局的 t-input--left 样式，专门针对车牌选择器中的输入框 */
.car-selector .t-input--left {
  text-align: left !important;
}

.car-selector .search-input:focus {
  border-color: #2196f3 !important;
  box-shadow: 0 4px 16px rgba(33, 150, 243, 0.2) !important;
}

.car-list {
  max-height: 280px;
  overflow-y: auto;
  background: white;
}

.car-item {
  padding: 18px 24px;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  font-size: 16px;
  color: #333;
}

.car-plate {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.driver-name {
  font-size: 14px;
  color: #666;
  font-weight: 400;
}

.car-item:hover {
  background-color: #f8f9fa;
}

.car-item:last-child {
  border-bottom: none;
}

.car-item.selected {
  background: linear-gradient(90deg, #e3f2fd 0%, #bbdefb 100%);
}

.car-item.selected .car-plate {
  color: #1976d2;
  font-weight: 700;
}

.car-item.selected .driver-name {
  color: #1976d2;
  font-weight: 500;
}

.car-item.selected::after {
  content: "✓";
  position: absolute;
  right: 24px;
  color: #1976d2;
  font-weight: bold;
  font-size: 18px;
}

.no-data {
  padding: 40px 24px;
  text-align: center;
  color: #999;
  font-size: 15px;
  background: white;
}

.dropdown-actions {
  display: flex;
  border-top: 1px solid #eee;
  background: #fafafa;
}

.btn-cancel,
.btn-confirm {
  flex: 1;
  padding: 18px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.btn-cancel {
  color: #666;
  border-right: 1px solid #eee;
}

.btn-cancel:hover {
  background-color: #f0f0f0;
  color: #333;
}

.btn-confirm {
  color: #2196f3;
  font-weight: 600;
}

.btn-confirm:hover {
  background: linear-gradient(90deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1976d2;
}
.container {
  width: 100%;
  height: 100%;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
}

.container-con {
  width: 100%;
  height: 100%;
}

.t-input--left {
  text-align: right !important;
}

.scan-img {
  width: 20px;
  height: 20px;
  display: block;
}

.discharge-list {
  padding: 0 16px;
  font-size: 16px;
  line-height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.discharge-list-btn {
  width: 120px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  background-color: #1953E6;
  color: #ffffff;
  /* padding: 0 15rpx; */
  border-radius: 15rpx;
}

.discharge-all {
  width: 100%;
  /* height: calc(100% - 273px); */
  overflow: hidden;
  padding: 0 16px;
  box-sizing: border-box;
}

.scan-header {
  display: flex;
  border-right: 1px solid #cccccc;
  overflow: hidden;
}

.scan-body {
  width: 100%;
  /* height: calc(100% - 23px); */
  overflow: hidden;
}

.scan-body-auto {
  width: 100%;
  height: 100%;
  overflow-y: auto;
}

.scan-header-li {
  flex: 0 0 20%;
  text-align: center;
  font-size: 28rpx;
  border-top: 1px solid #cccccc;
  border-left: 1px solid #cccccc;
  border-bottom: 1px solid #cccccc;
  height: 22px;
  line-height: 22px;
  box-sizing: border-box;
}

.scan-header-li:nth-child(2),
.scan-body-li:nth-child(2) {
  flex: 0 0 50%;
}

.scan-body-li {
  flex: 0 0 20%;
  text-align: center;
  font-size: 28rpx;
  border-left: 1px solid #cccccc;
  border-bottom: 1px solid #cccccc;
  height: 22px;
  line-height: 22px;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.scan-body-li:nth-child(3),
.scan-header-li:nth-child(3) {
  flex: 0 0 30%;
}

.notice-content {
  color: red;
  font-size: 16px;
  font-weight: bold;
}

.order-button {
  width: 100%;
  /* height: 50px; */
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.whole-order {
  width: 80%;
  height: 64rpx;
  background-color: #1953E6;
  color: #ffffff;
  text-align: center;
  line-height: 64rpx;
  margin: 0 auto;
  font-size: 28rpx;
  border-radius: 8rpx;
}

.overlay-con {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.overlay-main {
  width: 80%;
  height: 200px;
  background-color: #ffffff;
  margin: 0 auto;
  border-radius: 10px;
  overflow: hidden;
}

.overlay-main-radio {
  width: 80%;
  height: auto;
  background-color: #ffffff;
  margin: 0 auto;
  border-radius: 10px;
  /* overflow: hidden;   */
}

.overlay-title {
  text-align: center;
  line-height: 2;
  font-size: 18px;
  margin-top: 5px;
}

.overlay-tip {
  font-size: 16px;
  padding: 0 15px;
  text-indent: 15px;
}

.overlay-main-special .t-radio {
  margin: 20px 40px;
}

.overlay-button {
  width: 100%;
  height: 50px;
  display: flex;
  justify-content: space-around;
  margin-top: 2px;
}

.overlay-button-cancle,
.overlay-button-confirm {
  width: 50%;
  text-align: center;
  color: #ffffff;
  line-height: 50px;
  box-sizing: border-box;
}

.overlay-button-cancle {
  background-color: #cccccc;
  border-radius: 0 0 0 10px;

}

.overlay-button-confirm {
  background-color: #1953E6;
  border-radius: 0 0 10px 0;
}

/* 样式 */
.dialog-content {
  height: 500rpx;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
}

.radio-item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #eee;
}

.radio-label {
  display: flex;
  align-items: flex-start;
  /* 顶部对齐 */
  width: 100%;
}

.text-container {
  margin-left: 20rpx;
  flex: 1;
}

.radio-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.4;
  display: block;
  /* 强制换行 */
}

.secondary-text {
  font-size: 26rpx;
  color: #999;
  line-height: 1.2;
  display: block;
  margin-top: 8rpx;
}