<view class="container">
  <!-- 司机货台 -->
  <view class="section" wx:if="{{user.identityType == '10'}}">
    <t-grid column="3" class="block" theme="card">
      <view class="section-title">
        <view class="line"></view>
        <view>司机</view>
      </view>
      <t-grid-item text="新增司机" image="/assets/image/添加司机.svg" bind:tap="insertDriver" />
      <t-grid-item text="维护司机" image="/assets/image/司机信息.svg" bind:tap="editDriver" />
    </t-grid>
  </view>

  <!-- 预约 -->
  <view class="section" wx:if="{{user.reservationIdentity != '40' && !isUser}}">
    <t-grid column="3" class="block" theme="card">
      <view class="section-title">
        <view class="line"></view>
        <view>预约</view>
      </view>
      <t-grid-item text="新增预约" image="/assets/image/CRM-新增预约.svg" bind:tap="toInsertReservation" />
      <!-- <t-grid-item text="审核预约" image="/assets/image/审核.svg" bind:tap="toExamineReservation" wx:if="{{user.segNo === 'JC000000' && user.reservationIdentity != '30'}}" /> -->
    </t-grid>
  </view>

  <view class="section" wx:if="{{user.reservationIdentity != '40' && user.segNo === 'KF000000'}}">
    <t-grid column="3" class="block" theme="card">
      <view class="section-title">
        <view class="line"></view>
        <view>签收</view>
      </view>
      <t-grid-item text="客户签收" image="/assets/image/客户签收.svg" bind:tap="toCustomerSign" />
    </t-grid>
  </view>

  <!-- 配单  -->
  <view class="section" wx:if="{{user.segNo != 'KF000000'}}">
    <t-grid column="3" class="block" theme="card">
      <view class="section-title">
        <view class="line"></view>
        <view>配单</view>
      </view>
      <t-grid-item wx:if="{{user.reservationIdentity != '30' && !isUser}}" text="装货配单" image="/assets/image/上传资料.svg" bind:tap="toOrderAllocation" />
      <t-grid-item wx:if="{{isDriver}}" text="卸货配单" image="/assets/image/下载资料.svg" bind:tap="toDischargeCargo" />
      <t-grid-item wx:if="{{isDriver}}" text="现货交易配单" image="/assets/image/现货交易配单.svg" bind:tap="toSpotTradingOrder" />
      <t-grid-item text="我的配单" image="/assets/image/文件资料.svg" bind:tap="toMyOrder" />
      <t-grid-item text="配单合并" image="/assets/image/下载资料.svg" bind:tap="toConsolidation" wx:if="{{ isShowConsolidation }}" />
      <t-grid-item wx:if="{{user.reservationIdentity != '30' && !isUser}}" text="人员预约进厂" image="/assets/image/人员许可.svg" bind:tap="enterFactory" />
      <!-- <t-grid-item wx:if="{{user.reservationIdentity != '30' && !isUser}}" text="我的审核" image="/assets/image/我的审核.svg" bind:tap="enterReview" /> -->
    </t-grid>
  </view>

  <view class="section" wx:if="{{user.reservationIdentity == '40' && user.segNo == 'KF000000'}}">
    <t-grid column="3" class="block" theme="card">
      <view class="section-title">
        <view class="line"></view>
        <view>内部员工</view>
      </view>
      <t-grid-item text="厂内物流" image="/assets/image/物流查询.svg" bind:tap="toInPlant" />
    </t-grid>
  </view>
  <view class="section" wx:if="{{user.reservationIdentity != '30' && user.segNo == 'KF000000' }}">
    <t-grid column="3" class="block" theme="card">
      <view class="section-title">
        <view class="line"></view>
        <view>其他</view>
      </view>
      <t-grid-item text="提货委托" image="/assets/image/我的委托.svg" bind:tap="toDelivery" />
    </t-grid>
  </view>

  <view class="section" wx:if="{{isDriver}}">
    <t-grid column="3" class="block" theme="card">
      <view class="section-title">
        <view class="line"></view>
        <view>其他</view>
      </view>
      <t-grid-item text="废次材车辆" image="/assets/image/清洁能源周报.svg" bind:tap="toWasteVehicle" />
      <t-grid-item wx:if="{{isUser}}" text="临时预约" image="/assets/image/CRM-新增预约.svg" bind:tap="toTouristReservation" />
    </t-grid>
  </view>
</view>