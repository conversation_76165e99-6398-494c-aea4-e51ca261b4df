// pages/my-car/my-car.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    carList: [],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let userInfo = wx.getStorageSync("userInfo");
    const { segNo, driverName, tel } = userInfo;
    const app = getApp();
    wx.showLoading({
      title: '加载中',
    })
    wx.request({
      url: app.mesUrl,
      method: 'POST', // 请求方法
      data: {
        serviceId: 'S_LI_RL_0027',
        tel,
        segNo,
        driverName
      },
      success: (res) => {
        wx.hideLoading();
        if (res?.data?.result?.length > 0) {
          const result = res.data.result;
          this.setData({
            carList: result,
          });
          return
        }
        this.setData({
          carList: [],
        });
      },
      fail: () => wx.hideLoading(),
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})