// pages/examine-reservation/examine-reservation.js
const $api = require('../../api/request')
Page({

    /**
     * 页面的初始数据
     */
    data: {
        oddNumbers:'',
        dataList:[],
        userInfo:{},
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.setData({
            userInfo:wx.getStorageSync('userInfo')
        })
        console.log(wx.getStorageSync('userInfo'))
        this.queryReservation();
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {

    },
//  
    changeHandle(e) {
        // const { value } = e.detail;
        this.setData({
            oddNumbers:e.detail.value
        });
    },
    actionHandle() {
        this.queryReservation(this.data.oddNumbers);
      },
    
      queryReservation(e) {
        var data = {
            "segNo":this.data.userInfo.segNo,
            "reservationNumber":e
        }
        $api.request('S_LI_RL_0139','',data).then((res) => {
            console.log(res)
            let arr=[]
            arr=res.reservationNumberList
            // arr.push(res.reservationNumberList[0])
            this.setData({
                dataList:arr,
            })  
        }).catch((err) => {	
			console.log(err)					
        })
      },
    //   勾选数据
    cheackList(e){
        let index=e.currentTarget.dataset.index;  
        let dataList = this.data.dataList;
        dataList[index].flag=!dataList[index].flag
        this.setData({
            dataList: dataList
        })
        console.log(dataList)
    },
    // 提交预约审批单
    cheackOrder(){
        let cheackData=[]
        let that=this
        cheackData = this.data.dataList.filter(item => {
            return item.flag==true
        })
        if(cheackData.length>0){
            var data = {
                result:cheackData,
                recCreator:this.data.userInfo.recCreator,
                recCreatorName:this.data.userInfo.recCreatorName
            }
            $api.request('S_LI_RL_0140','',data).then((res) => {
                wx.showToast({
                    title: res.__sys__.msg,
                    duration: 1000,
                  });
                  setTimeout(() => {
                    that.queryReservation()
                  }, 1000);
            }).catch((err) => {	
                console.log(err)					
            })
        }else{
            wx.showToast({
                title: '请先勾选数据',
                icon: 'none',
                duration: 1000
            })  
        }
    },
    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    }
})