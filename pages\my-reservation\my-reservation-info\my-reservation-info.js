// pages/my-reservation/my-reservation-info/my-reservation-info.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    item: {},
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const eventChannel = this.getOpenerEventChannel();
    if (!eventChannel) {
      return;
    }
    eventChannel.emit('acceptDataFromOpenedPage', { data: '' });
    eventChannel.on('acceptDataFromOpenerPage', (data) => {
      const resultData = data.data;
      console.log(data);
      this.setData({
        item: resultData,
      });
    });
  },

})