page {
    width: 100%;
    height: 100%;
  background: #f6f6f6;
}

.demo {
  padding-bottom: 56rpx;
}

.demo-title {
  font-size: 48rpx;
  font-weight: 700;
  line-height: 64rpx;
  margin: 48rpx 32rpx 0;
  color: var(--td-font-gray-1, rgba(0, 0, 0, 0.9));
}

.demo-desc {
  font-size: 28rpx;
  color: var(--td-font-gray-2, rgba(0, 0, 0, 0.6));
  margin: 16rpx 32rpx 0;
  line-height: 44rpx;
}

.app-t-class {
  color: var(--td-input-default-text-color, var(--td-font-gray-1, rgba(0, 0, 0, 0.9))) !important;
}

/* 卡片主容器 */
.card-main {
  background-color: #ffffff;
  /* 卡片背景颜色 */
  margin: 10px 15px;
  /* 上下10px间距，左右15px间距 */
  padding: 15px;
  /* 内部填充 */
  border-radius: 8px;
  /* 卡片圆角 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  /* 阴影效果 */
}

/* 卡片内部每一项的容器，使用flex布局 */
.card-item {
  display: flex;
  justify-content: space-between;
  /* 左右对齐 */
  padding: 8px 0;
  /* 每一项的上下内边距 */
  border-bottom: 1px solid #eaeaea;
  /* 每一项之间的分割线 */
}

/* 最后一项不需要分割线 */
.card-item:last-child {
  border-bottom: none;
}

/* 左边标签的样式 */
.label {
  font-size: 16px;
  /* 字体大小 */
  color: #333;
  /* 字体颜色 */
}

/* 右边值的样式 */
.value {
  font-size: 16px;
  /* 字体大小 */
  color: #666;
  /* 字体颜色 */
  text-align: right;
  /* 右对齐 */
  max-width: 60%;
  /* 右侧文本最长占比，避免溢出 */
}

.my-basic {
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* 让内容左右两边对齐 */
  margin-left: 50rpx;
  margin-top: 50rpx;
  margin-right: 50rpx;
  /* 给右侧一些边距 */
}

/* 头像 */
.avatar-example:not(:last-child) {
  margin-right: 16px;
  /* 调整头像与文本的间距 */
}

.external-class-content {
  color: #fff;
  background-color: var(--td-brand-color, #0052d9);
  font-weight: 400;
}

/* 信息容器，垂直排列姓名和电话号码 */
.info-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-grow: 1;
  /* 让信息容器占据中间的剩余空间 */
}

/* 姓名样式 */
.name {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 4px;
  /* 调整姓名与电话号码之间的间距 */
}

/* 电话号码样式 */
.phone {
  font-size: 14px;
  color: #666;
  /* 根据需求调整颜色 */
}

.t-overlay {
  top: 0 !important;
}