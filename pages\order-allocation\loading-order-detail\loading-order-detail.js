// pages/order-allocation/loading-order-detail/loading-order-detail.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    mainObj: {},
    dataList: [],
    mainObjList: [],
    isEdit: false,
    editObj: '',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const eventChannel = this.getOpenerEventChannel()
    // 监听acceptDataFromOpenerPage事件，获取上一页面通过eventChannel传送到当前页面的数据
    eventChannel.on('acceptDataFromLoadingOrder', (data) => {
      console.log(data);
      if (data.isEdit) {
        this.setData({
          isEdit: data.isEdit,
          editObj: data.editObj,
        });
      }

      if (data.checkList && data.checkList.length > 0) {
        let packList = [];
        data.checkList.forEach(c => {
          packList = [
            ...packList,
            ...c.packList,
          ];
        });
        if (packList.length > 0) {
          packList.forEach((item, i) => {
            item.flag = false
          })
        }

        this.setData({
          mainObjList: data.checkList,
          dataList: packList
        });
        return;
      }

      let packList = [];
      packList = data.data.packList;
      if (packList.length > 0) {
        packList.forEach((item, i) => {
          item.flag = false
        })
      }
      this.setData({
        mainObj: data.data,
        dataList: packList
      });
    })
  },

  cheackList(e) {
    let index = e.currentTarget.dataset.index;
    let dataList = this.data.dataList;
    dataList[index].flag = !dataList[index].flag
    this.setData({
      dataList: dataList
    })

  },

  cheackOrder() {
    let detailsList = this.data.dataList.filter(item => {
      return item.flag == true
    });

    if (detailsList.length == 0) {
      wx.showToast({
        title: '请先勾选数据',
        icon: 'none',
        duration: 1000
      });
      return;
    }

    let pageNum = 3;
    let mainObj = [];
    mainObj[0] = this.data.mainObj;
    mainObj[0].packList = detailsList;
    
    if (this.data.mainObjList.length > 0) {
      let { mainObjList } = this.data;
      mainObjList.forEach(m => {
        m.packList = detailsList.filter(d => d.ladingBillId == m.ladingBillId);
      });
      mainObj = mainObjList;
    }

    if (this.data.isEdit) {
      pageNum = 3;
    }
    wx.navigateTo({
      url: `/pages/order-allocation/submit-order/submit-order?pageNum=${pageNum}`,
      success: function (res) {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('acceptDataFromLoadingOrderList', { data: mainObj })
      }
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})