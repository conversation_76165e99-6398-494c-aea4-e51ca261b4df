/* pages/driver/driver-edit/driver-edit.wxss */
.container {
  padding: 20px;
  background-color: #f5f5f5;
  height: 75vh;
  overflow-y: auto;
  box-sizing: border-box;
}

.form-group {
  margin-bottom: 16px;
}

.form-group t-input {
  margin-bottom: 16px;
}

.verification-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.verification-group t-input {
  flex-grow: 1;
  margin-right: 8px;
}

.external-class {
  height: 256rpx;
}

/* 底部容器样式 */
.bottom-container {
  position: fixed;
  bottom: 0;
  width: 100%;
  background-color: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  padding-bottom: 20px;
}

/* 第一行按钮布局 */
.button-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  margin-bottom: 10px;
  margin-top: 16px;
}

/* 删除图标样式 */
.icon-wrapper {
  flex: 1;
  margin-right: 10px;
  text-align: center;
}

/* 保存按钮样式 */
.save-button-wrapper {
  flex: 1;
  margin-left: 10px;
}

/* 第二行按钮布局 */
.plate-button-wrapper {
  display: flex;
  justify-content: center;
  padding: 0 20px;
}

.wrapper {
  margin-bottom: 32rpx;
}

.placeholder {
  color: var(--td-text-color-placeholder);
  line-height: 96rpx;
  height: 96rpx !important;
  display: flex;
  align-items: center;
}

.dialog-input {
  padding-top: 12px;
  padding-bottom: 12px;
  text-align: left;
  margin-top: 32rpx;
  border-radius: 8rpx;
  background-color: #f3f3f3;
  box-sizing: border-box;
}

.t-dialog-class {
  width: unset !important;
}
