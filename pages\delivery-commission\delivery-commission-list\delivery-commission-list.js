// pages/delivery-commission/delivery-commission-list/delivery-commission-list.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    list: [],
    listType: {
      value: '10',
      options: [
        { value: '10', label: '提单' },
        { value: '20', label: '转库单' },
      ],
    },
    customer: {},
    value: '',
    user: '',
    confirmBtn: { content: '确定', variant: 'base' },
    belongCompanyValue: '请选择客户',
    belongCompanyQueryVisible: false,
    belongCompany: '',
    belongCompanyQuery: '',
    belongCompanyList: [],
    belongCompanyVisible: false,
    carrier: '',
    carrierValue: '请选择承运商',
    reservationIdentity: '',
    billValue: '请选择提单客户',
    bill: '',
    customerType: '',
    // 分页相关参数
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    isLoading: false,
    scrollTop: 0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let userInfoList = wx.getStorageSync('userInfoList');
    const user = wx.getStorageSync('userInfo');
    this.setData({
      user,
    })
    userInfoList = userInfoList.filter(u => u.reservationIdentity == user.reservationIdentity);
    const list = userInfoList.map(m => {
      return {
        label: m.customerName,
        value: m.customerId,
      };
    });

    const { customerId } = userInfoList[0];
    this.setData({
      'customer.options': list,
      'customer.value': customerId,
    });

    // 客户只有提单查询
    if (user.reservationIdentity == '10') {
      this.setData({
        'listType.value': '10',
        'listType.options': [
          { value: '10', label: '提单' },
        ],
      })
    }

  },

  onShow() {
    this.getList(true);
  },

  /**
* 伪双向绑定搜索框
* @param {*} e 
*/
  changeHandle(e) {
    const { value } = e.detail;
    this.setData({
      value,
    });
  },

  /**
* 显示客户选择框
*/
  onShowBelongCompany() {
    this.setData({
      reservationIdentity: '10',
      customerType: '10',
    });
    this.getCustomerList();
  },

  /**
* 显示客户选择框
*/
  onShowBill() {
    this.setData({
      reservationIdentity: '10',
      customerType: '30',
    });
    this.getCustomerList();
  },

  /**
* 显示承运商选择框
*/
  onShowCarrier() {
    this.setData({
      reservationIdentity: '20',
      customerType: '20',
    });
    this.getCustomerList();
  },

  /**
* 根据客户编码查询
*/
  belongCompanyQueryChange(e) {
    const { value } = e.detail;
    this.setData({
      belongCompanyQuery: value,
    })
  },

  belongSumbit() {
    this.getCustomerList();
  },

  belongClear() {
    this.setData({
      belongCompanyQuery: '',
    });
    this.getCustomerList();
  },

  /**
* 隐藏选择司机弹出框
*/
  belongCompanyCanCel() {
    this.setData({
      belongCompanyVisible: false,
      belongCompanyQueryVisible: false,
    });
  },

  /**
* 确定选择框值
*/
  onPickerChange(e) {
    const { value, label } = e.detail;
    const { key } = e.currentTarget.dataset;

    // 承运商
    if (this.data.customerType == '20') {
      this.setData({
        [`${key}Visible`]: false,
        carrierValue: value,
        carrier: label,
      });
    } else if (this.data.customerType == '10') {
      this.setData({
        belongCompanyVisible: false,
        belongCompanyValue: value,
        belongCompany: label,
      });
    } else {
      this.setData({
        belongCompanyVisible: false,
        billValue: value,
        bill: label,
      });
    }


    this.belongCompanyCanCel();
  },

  // 重置筛选
  resetFilter() {
    this.setData({
      belongCompanyValue: '请选择客户',
      belongCompany: '',
      belongCompanyQuery: '',
      belongCompanyList: [],
      carrierValue: '请选择承运商',
      carrier: '',
      bill: '',
      billValue: '请选择提单客户',
    });
    this.getList(true);
  },

  // 确认筛选
  confirmFilter() {
    if (!this.data.belongCompanyValue.includes('请选择') && !this.data.carrierValue.includes('请选择')) {
      this.setData({
        showTextAndTitle: true,
        content: '客户和承运商只能选其一',
      });
      return;
    }


    this.closeFilter();
    // 这里执行筛选逻辑
    this.getList(true);
  },

  // 关闭抽屉
  closeFilter() {
    this.setData({ filterVisible: false })
  },

  /**
* 关闭错误提示框
*/
  closeDialog() {
    this.setData({
      showTextAndTitle: false,
    });
  },

  /**
   * 提单或者转库单
   * @param {*} e 
   */
  onChange(e) {
    this.setData({
      'listType.value': e.detail.value,
    });
    this.getList(true);
  },

  onCustomerChange(e) {
    this.setData({
      'customer.value': e.detail.value,
    });
    this.getList(true);
  },

  /**
* 点击搜索按钮或者回车
*/
  actionHandle() {
    this.getList(true);
  },

  /**
 * 清空搜索框
 */
  clearValue() {
    this.setData({
      value: '',
    });
    this.getList(true);
  },

  /**
   * 打开筛选框
   */
  openFilter() {
    this.setData({ filterVisible: true });
  },

  /**
   * 跳转修改
   */
  editItem(e) {
    const index = e.currentTarget.dataset.index;
    const custInfo = this.data.list[index];
    wx.navigateTo({
      url: '/pages/delivery-commission/delivery-commission-edit/delivery-commission-edit',
      events: {
        // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
        acceptDataFromOpenedPage: () => { },
      },
      success: (res) => {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('acceptDataFromOpenerPage', { data: custInfo, })
      }
    });
  },

  /**
   * 获取提单数据
   */
  getList(resetPage = false) {
    if (this.data.isLoading) return;

    const newState = {};
    if (resetPage) {
      newState.currentPage = 1;
      newState.list = [];
      newState.hasMore = true;
      this.setData({
        scrollTop: 0,
      });
    }

    const user = wx.getStorageSync('userInfo');
    const { segNo, reservationIdentity, SEG_NO } = user;
    const app = getApp();
    wx.showLoading({
      title: '加载中',
    });

    let queryKey = 'customerId';
    let queryKeyValue = this.data.customer.value;
    if (this.data.listType.value == '10' && reservationIdentity == '10') {
      switch (reservationIdentity) {
        case '10':
          queryKey = 'userNum';
          break;
        case '20':
          queryKey = 'tproviderId';
          break;
        default:
          queryKey = 'customerId';
          break;
      }
    }

    if (!this.data.belongCompanyValue.includes('请选择')) {
      queryKey = 'userNum';
      queryKeyValue = this.data.belongCompanyValue[0];
    }

    if (!this.data.carrierValue.includes('请选择')) {
      queryKey = 'tproviderId';
      queryKeyValue = this.data.carrierValue[0];
    }

    if (reservationIdentity == '20') {
      queryKey = 'tproviderId';
    }

    const value = this.data.value;
    let valueKey = 'ladingBillId'; // 提单
    if (value.includes('ZK')) {
      valueKey = 'transBillId'; // 转库单
    } else if (value.includes('BL')) {
      valueKey = 'ladingBillId'; // 提单
    } else {
      valueKey = 'destSpotName';
    }

    this.setData({ ...newState, isLoading: true });

    wx.request({
      url: app.mesUrl,
      method: 'POST', // 请求方法
      data: {
        serviceId: 'S_LI_RL_0120',
        segNo: segNo ?? SEG_NO,
        ladingFlag: this.data.listType.value,
        reservationIdentity,
        [`${queryKey}`]: queryKeyValue,
        [`${valueKey}`]: value,
        customerId: this.data.billValue.includes('请选择') ? '' : this.data.billValue[0],
        offset: this.data.currentPage,
        limit: this.data.pageSize,
      },
      success: (res) => {
        wx.hideLoading();
        const result = res.data;
        if (result.__sys__?.status == -1) {
          this.setData({
            showTextAndTitle: true,
            content: result.__sys__.msg,
            isLoading: false,
          });
          return;
        }

        if (!result.list || result.list.length == 0) {
          this.setData({
            list: [],
            isLoading: false,
          });
          return;
        }

        const list = result.list.map(r => {
          return {
            ...r,
            totalWeight: getApp().truncateDecimals(r.totalWeight, 3),
            totalPackQtyNum: Number(r.totalPackQty || 0).toFixed(0),
          }
        });

        const currentList = list || [];
        this.setData({
          list: resetPage ?
            currentList :
            [...this.data.list, ...currentList],
          hasMore: list.length > 9,
          isLoading: false
        });

      },
      fail: () => {
        wx.hideLoading();
        this.setData({
          showTextAndTitle: true,
          content: '网络异常, 请稍后重试',
          isLoading: false
        });
      },
    });
  },

  /**
* 查询客户
*/
  getCustomerList() {
    const app = getApp();
    wx.showLoading({
      title: '查询中',
    });
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        segNo: app.segNo,
        customerName: this.data.belongCompanyQuery,
        reservationIdentity: this.data.reservationIdentity,
        serviceId: 'S_LI_RL_0113',
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          this.setData({
            showTextAndTitle: true,
            content: '网络异常, 请稍后重试',
          });
          return;
        }

        const result = res.data;
        if (result.__sys__?.status == -1) {
          this.setData({
            showTextAndTitle: true,
            content: result.__sys__.msg,
          });
          return;
        }

        const { list } = result;
        let belongCompanyList = list.map(l => {
          return {
            label: l.chineseUserName,
            value: l.userNum,
          }
        });

        this.setData({
          belongCompanyList,
          belongCompanyVisible: true,
          belongCompanyQueryVisible: true,
        });

      },
    });
  },

  // 滚动触底事件
  onReachBottom: function () {
    this.debounce(function () {
      if (this.data.hasMore && !this.data.isLoading) {
        this.setData(
          { currentPage: this.data.currentPage + 1 },
          () => this.getList()
        );
      }
    }.bind(this))();
  },

  // 新增防抖函数
  debounce(func, delay = 500) {
    let timer = null;
    return function (...args) {
      if (timer) clearTimeout(timer);
      timer = setTimeout(() => {
        func.apply(this, args);
      }, delay);
    };
  },

})