/* 卡片主容器 */
.card-main {
    background-color: #ffffff;
    /* 卡片背景颜色 */
    margin: 10px 15px;
    /* 上下10px间距，左右15px间距 */
    padding: 15px;
    /* 内部填充 */
    border-radius: 8px;
    /* 卡片圆角 */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    /* 阴影效果 */
}

/* 卡片内部每一项的容器，使用flex布局 */
.card-item {
    display: flex;
    justify-content: space-between;
    /* 左右对齐 */
    padding: 8px 0;
    /* 每一项的上下内边距 */
    border-bottom: 1px solid #eaeaea;
    /* 每一项之间的分割线 */
}

/* 最后一项不需要分割线 */
.card-item:last-child {
    border-bottom: none;
}

/* 左边标签的样式 */
.label {
    font-size: 16px;
    /* 字体大小 */
    color: #333;
    /* 字体颜色 */
}

/* 右边值的样式 */
.value {
    font-size: 16px;
    /* 字体大小 */
    color: #666;
    /* 字体颜色 */
    text-align: right;
    /* 右对齐 */
    max-width: 70%;
    /* 右侧文本最长占比，避免溢出 */
}

/* 图标容器，使图标在右侧并垂直居中 */
.icon-container {
    display: flex;
    justify-content: flex-end;
    /* 图标水平居右 */
    align-items: center;
    /* 图标垂直居中 */
    /* padding-top: 10px; */
    /* 与上方内容的间距 */

    /* text-align: right; */
}

.example-search {
    background-color: var(--bg-color-demo);
    padding: 16rpx 32rpx;
}

/* 无数据提示样式 */
.empty-container {
    padding: 100rpx 0;
}

/* 分页状态优化 */
.pagination-status {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20rpx;
    color: #666;
    font-size: 24rpx;
    text-align: center;
}

.pagination-status .divider {
    width: 1px;
    height: 24rpx;
    background: #ddd;
    margin: 0 20rpx;
}

/* 样式 */
.dialog-content {
    height: 500rpx;
    padding: 20rpx 30rpx;
    box-sizing: border-box;
}
.radio-item {
    padding: 24rpx 0;
    border-bottom: 1rpx solid #eee;
}

.radio-label {
    display: flex;
    align-items: center;
}

.radio-text {
    margin-left: 20rpx;
    font-size: 28rpx;
    color: #333;
}
