// pages/order-allocation/loading-order/loading-order.js
import Toast from 'tdesign-miniprogram/toast/index';
const $api = require('../../../api/request')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {},
    dataList: [],
    checkedList: [], // 已勾选的数据列表
    searchDataList: [], // 搜索结果数据列表
    triggered: false,
    ladingBillId: '',
    editObj: '', // 要修改的对象
    isEdit: false,
    // 客户选择相关
    customer: {
      value: '',
      options: []
    },
    customerList: [], // 客户列表
    // 筛选相关
    filterVisible: false, // 筛选弹窗显示状态
    filterRemark: '', // 筛选备注
    // 分页相关
    kdOffset: 0, // 每页数据量
    zkOffset: 0, // 当前页码，从0开始
    loading: false, // 是否正在加载
    loaded: false, // 是否已加载完所有数据
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      userInfo: wx.getStorageSync('userInfo')
    })
    
    // 初始化客户选择选项
    this.initCustomerOptions();
    
    this.resetPageData();
    
    // 我的配单跳转过来修改的
    const editObj = wx.getStorageSync('editData');
    if (options.isEdit || editObj) {
      const eventChannel = this.getOpenerEventChannel();
      if (eventChannel) {
        eventChannel.on('acceptDataFromLoadingOrder', (data) => {
          this.setData({
            editObj: data.data,
            isEdit: true,
          });
        });
      } else {
        this.setData({
          editObj,
          isEdit: true,
        });
      }
    }
  },
  
  // 初始化客户选择选项
  initCustomerOptions() {
    let userInfoList = wx.getStorageSync('userInfoList');
    const user = wx.getStorageSync('userInfo');
    userInfoList = userInfoList.filter(u => u.reservationIdentity == user.reservationIdentity);
    
    this.setData({
      customerList: userInfoList,
    });
    
    const list = userInfoList.map(m => {
      return {
        label: `${m.customerId}-${m.customerName}`,
        value: m.customerId,
      };
    });

    if (userInfoList.length == 1) {
      // 只有一个客户，自动选中且禁用
      const { customerId } = userInfoList[0];
      this.setData({
        'customer.options': list,
        'customer.value': customerId,
      });
      // 自动加载数据
      this.getList();
      return;
    }

    // 多个客户，添加全部客户选项作为默认
    list.unshift({
      value: 'all',
      label: `全部客户`,
    });
    this.setData({
      'customer': {
        value: 'all',
        options: list,
      },
    });
    // 默认加载所有客户数据
    this.getList();
  },
  
  // 客户下拉框选择
  onChange(e) {
    this.setData({
      'customer.value': e.detail.value,
    });
    this.resetPageData();
    this.getList();
  },
  
  // 重置分页数据
  resetPageData() {
    this.setData({
      triggered: false,
      dataList: [],
      checkedList: [], // 清空已勾选数据
      searchDataList: [], // 清空搜索结果
      kdOffset: 0,
      zkOffset: 0,
      loading: false,
      loaded: false
    });
  },
  
  // 重置搜索数据（保留已勾选数据）
  resetSearchData() {
    this.setData({
      triggered: false,
      searchDataList: [], // 清空搜索结果
      dataList: [...this.data.checkedList], // 保留已勾选数据
      kdOffset: 0,
      zkOffset: 0,
      loading: false,
      loaded: false
    });
  },
  
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },
  backRefresh() {
    this.resetPageData();
    this.getList();
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },
  // 下拉刷新
  onRefresh() {
    this.resetPageData();
    this.getList();
  },
  // 搜索框
  changeHandle(e) {
    this.setData({
      ladingBillId: e.detail.value
    });
  },
  actionHandle() {
    this.resetSearchData();
    this.getList();
  },
  // 获取数据
  getList(isLoadMore = false) {
    var that = this
    
    // 如果是加载更多且已经加载完毕，直接返回
    if (isLoadMore && this.data.loaded) {
      return;
    }
    
    // 如果是加载更多且正在加载，避免重复请求
    if (isLoadMore && this.data.loading) {
      return;
    }
    
    this.setData({
      loading: true
    });
    
    if (!isLoadMore) {
      wx.showLoading({
        title: '加载中...',
      });
    }

    const queryBill = this.data.ladingBillId;
    let queryKey = 'ladingBillId';
    if (/^[A-Za-z0-9]+$/.test(queryBill)) {
      queryKey = 'ladingBillId';
    } else {
      queryKey = 'ladingSpotAddr';
    }

    // 根据选择的客户获取客户ID数组
    const customerId = this.data.customer.value;
    let customerIds;
    
    if (this.data.customerList.length == 1) {
      // 单客户情况
      customerIds = [this.data.customerList[0].customerId];
    } else {
      // 多客户情况
      if (customerId === 'all') {
        // 选择全部客户，获取所有客户ID
        customerIds = this.data.customerList.map(u => u.customerId);
      } else {
        // 选择特定客户
        customerIds = [customerId];
      }
    }

    const data = {
      segNo: this.data.userInfo.segNo,
      [`${queryKey}`]: this.data.ladingBillId,
      reservationIdentity: this.data.userInfo.reservationIdentity,
      customerId: customerIds,
      ladingBillRemark: this.data.filterRemark, // 添加备注筛选参数
      zkOffset: this.data.zkOffset,
      kdOffset: this.data.kdOffset,
      limit: 10,
    }
    
    $api.request('S_LI_RL_0075', '', data).then((res) => {
      wx.hideLoading();
      that.setData({
        loading: false
      });
      
      if (res.result.length > 0) {
        const resList = res.result.map(r => {
          const { packList } = r;
          r['sumNetWeight'] = packList.reduce((sum, item) => {
            return sum + (Number(item.netWeight) || 0);
          }, 0);
          const totalWeight = Number(r.totalWeight) || 0;
          
          // 检查是否已在勾选列表中
          const isInCheckedList = that.data.checkedList.some(checked => checked.ladingBillId === r.ladingBillId);
          
          return {
            ...r,
            sumNetWeight: getApp().formatNumber(r.sumNetWeight),
            totalWeightNum: getApp().formatNumber(totalWeight),
            flag: isInCheckedList, // 如果已在勾选列表中，设置为勾选状态
          };
        });

        // 过滤掉已在勾选列表中的数据，避免重复显示
        const filteredResList = resList.filter(item => 
          !that.data.checkedList.some(checked => checked.ladingBillId === item.ladingBillId)
        );

        // 判断是否加载完毕
        const isLoaded = res.kdRemainPage <= 0 && res.zkRemainPage <= 0;
        
        // 根据remainPage决定是否增加对应的offset
        const newKdOffset = res.kdRemainPage > 0 ? res.kdOffset + 1 : that.data.kdOffset;
        const newZkOffset = res.zkRemainPage > 0 ? res.zkOffset + 1 : that.data.zkOffset;
        
        if (isLoadMore) {
          // 加载更多，追加数据
          that.setData({
            searchDataList: [...that.data.searchDataList, ...filteredResList],
            kdOffset: newKdOffset,
            zkOffset: newZkOffset,
            loaded: isLoaded,
            triggered: false
          });
        } else {
          // 首次加载或搜索，替换数据
          that.setData({
            searchDataList: filteredResList,
            kdOffset: newKdOffset,
            zkOffset: newZkOffset,
            loaded: isLoaded,
            triggered: false
          });
        }
        
        // 合并已勾选数据和搜索结果，已勾选数据在前面
        that.setData({
          dataList: [...that.data.checkedList, ...that.data.searchDataList]
        });
      } else {
        // 没有数据
        if (isLoadMore) {
          // 加载更多时没有新数据，标记为已加载完毕
          that.setData({
            loaded: true,
            triggered: false
          });
        } else {
          // 首次加载没有数据
          that.setData({
            triggered: false,
            loaded: true,
            dataList: [...that.data.checkedList] // 即使没有搜索结果，也要显示已勾选的数据
          });
        }
      }
    }).catch((err) => {
      console.log(err)
      wx.hideLoading();
      that.setData({
        loading: false
      });
    })
  },
  
  // 加载更多
  loadMore() {
    if (!this.data.loaded && !this.data.loading) {
      this.getList(true);
    }
  },
  
  gotoDetails: function (e) {
    var item = e.currentTarget.dataset.listobj;
    wx.navigateTo({
      url: '/pages/order-allocation/loading-order-detail/loading-order-detail',
      success: function (res) {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('acceptDataFromLoadingOrder', { data: item })
      }
    })
  },
  cheackList(e) {
    let index = e.currentTarget.dataset.index;
    let dataList = [...this.data.dataList]; // 创建副本
    let checkedList = [...this.data.checkedList]; // 创建副本
    
    const item = dataList[index];
    item.flag = !item.flag;
    
    if (item.flag) {
      // 勾选：添加到已勾选列表
      checkedList.push({...item});
    } else {
      // 取消勾选：从已勾选列表中移除
      checkedList = checkedList.filter(checkedItem => checkedItem.ladingBillId !== item.ladingBillId);
    }
    
    // 只更新勾选状态和已勾选列表，保持当前列表顺序不变
    this.setData({
      dataList: dataList,
      checkedList: checkedList
    });
    
    console.log('已勾选数据:', checkedList);
  },
  // 清空搜索
  clearValue() {
    this.setData({
      ladingBillId: ''
    });
    this.resetSearchData();
    this.getList();
  },
  cheackOrder() {
    // 直接使用已勾选数据列表
    let cheackData = this.data.checkedList;

    if (cheackData.length == 0) {
      wx.showToast({
        title: '请先勾选数据',
        icon: 'none',
        duration: 1000
      });
      return;
    }

    // let pageNum = 1;
    let pageNum = 2;
    // 是否是修改
    if (this.data.isEdit || this.data.editObj) {
      pageNum = 2;
    }

    wx.navigateTo({
      url: `/pages/order-allocation/submit-order/submit-order?pageNum=${pageNum}`,
      success: function (res) {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('acceptDataFromLoadingOrderList', { data: cheackData })
      }
    });
  },

  /**
   * 部分配单
   */
  partOrder() {
    // 直接使用已勾选数据列表
    let cheackData = this.data.checkedList;

    if (cheackData.length == 0) {
      wx.showToast({
        title: '请先勾选数据',
        icon: 'none',
        duration: 1000
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/order-allocation/loading-order-detail/loading-order-detail',
      success: (res) => {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('acceptDataFromLoadingOrder', { checkList: cheackData, isEdit: this.data.isEdit, editObj: this.data.editObj })
      }
    })

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 筛选相关方法
  // 打开筛选弹窗
  openFilter() {
    this.setData({
      filterVisible: true
    });
  },

  // 关闭筛选弹窗
  closeFilter() {
    this.setData({
      filterVisible: false
    });
  },

  // 筛选弹窗显示状态变化
  onFilterVisibleChange(e) {
    this.setData({
      filterVisible: e.detail.visible
    });
  },

  // 备注输入
  onRemarkInput(e) {
    this.setData({
      filterRemark: e.detail.value
    });
  },

  // 重置筛选条件
  resetFilter() {
    this.setData({
      filterRemark: ''
    });
  },

    // 确认筛选
  confirmFilter() {
    this.setData({
      filterVisible: false
    });
    this.resetSearchData();
    this.getList();
  },
})
//

