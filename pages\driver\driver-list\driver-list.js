// pages/driver/driver-list/driver-list.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    value: '',
    list: [],
    customer: {},
    showTextAndTitle: false,
    content: '', // 提示信息
    confirmBtn: { content: '确定', variant: 'base' },
    driverList: [],
    rIdName: '',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let userInfoList = wx.getStorageSync('userInfoList');
    const user = wx.getStorageSync('userInfo');
    userInfoList = userInfoList.filter(u => u.reservationIdentity == user.reservationIdentity);
    this.setData({
      list: userInfoList,
    });
    const list = userInfoList.map(m => {
      return {
        label: `${m.customerId}-${m.customerName}`,
        value: m.customerId,
      };
    });

    if (userInfoList.length == 1) {
      const { customerId } = userInfoList[0];
      this.setData({
        'customer.options': list,
        'customer.value': customerId,
      });
      return;
    }

    list.unshift({
      value: 'all',
      label: `请选择所属公司`,
    });
    this.setData({
      'customer': {
        value: 'all',
        options: list,
      },
    });

  },

  onShow() {
    this.queryDriverList();
  },

  /**
   * 伪双向绑定搜索框
   * @param {*} e 
   */
  changeHandle(e) {
    const { value } = e.detail;
    this.setData({
      value,
    });
  },

  /**
   * 点击搜索按钮或者回车
   */
  actionHandle() {
    this.queryDriverList();
  },

  /**
   * 客户下拉框选择
   * @param {*} e 
   */
  onChange(e) {
    this.setData({
      'customer.value': e.detail.value,
    });
    this.queryDriverList();
  },

  /**
   * 清空搜索框
   */
  clearValue() {
    this.setData({
      value: '',
    });
    this.queryDriverList();
  },

  /**
 * 关闭错误提示框
 */
  closeDialog() {
    this.setData({
      showTextAndTitle: false,
    });
  },

  /**
   * 转到详情
   * @param {*} e 
   */
  toInfo(e) {
    // 下标
    const { key } = e.currentTarget.dataset;
    let data = this.data.driverList[key];
    data['customerId'] = this.data.customer.value;
    const custer = this.data.list.find(l => l.customerId == data.customerId);
    data['customerName'] = custer?.customerName;
    data['segNo'] = custer?.segNo;
    data['administrator'] = custer?.administrator;
    wx.navigateTo({
      url: '/pages/driver/driver-edit/driver-edit',
      events: {
        // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
        acceptDataFromOpenedPage: () => { },
      },
      success: (res) => {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('acceptDataFromOpenerPage', { data, })
      }
    });
  },

  /**
   * 查询司机
   */
  queryDriverList() {
    const customerId = this.data.customer.value;
    if (!customerId || customerId.includes('all')) {
      this.setData({
        showTextAndTitle: true,
        content: `请选择所属公司`,
        driverList: [],
      });
      return;
    }

    const app = getApp();
    wx.showLoading({
      title: '加载中',
    });

    const isTelOrName = isNaN(this.data.value);
    let queryKey = 'driverName';
    if (isTelOrName) {
      // 有汉字再判断是否是车牌号
      if (!app.isChinese(this.data.value)) {
        queryKey = 'vehicleNo';
      }
    } else {
      queryKey = 'driverTel';
    }

    const user = wx.getStorageSync('userInfo');
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        segNo: user.segNo,
        tel: user.tel,
        customerId,
        [`${queryKey}`]: this.data.value,
        reservationIdentity: user.reservationIdentity,
        serviceId: 'S_LI_RL_0032',
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          this.setData({
            showTextAndTitle: true,
            content: '网络异常, 请稍后重试',
          });
          return;
        }

        const result = res.data;
        if (result.__sys__?.status == -1) {
          this.setData({
            showTextAndTitle: true,
            content: result.__sys__.msg,
          });
          return;
        }

        wx.setStorage({
          key:'auditList', 
          data: result.list.filter(l => l.status == '10'),
        });
        this.setData({
          driverList: result.list,
        });
      },
      fail: () => {
        wx.hideLoading();
        this.setData({
          showTextAndTitle: true,
          content: '网络异常, 请稍后重试',
        });
      }
    });
  },

  /**
   * 审核
   * @param {*} e 
   */
  onAudit(e) {
    const { index } = e.currentTarget.dataset;
    const data = this.data.driverList[index];
    console.log(data);
    const user = wx.getStorageSync('userInfo');
    const app = getApp();
    wx.showLoading({
      title: '审核中',
    });
    const result = {
      ...data,
      segNo: user.segNo,
      recCreator  : user.tel,
      recCreatorName: user.administrator,
    };
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        result,
        serviceId: 'S_LI_RL_0115',
        
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          this.setData({
            showTextAndTitle: true,
            content: '网络异常, 请稍后重试',
          });
          return;
        }

        const result = res.data;
        if (result.__sys__?.status == -1) {
          this.setData({
            showTextAndTitle: true,
            content: result.__sys__.msg,
          });
          return;
        }

        // 重新查询数据
        this.queryDriverList();
      },
      fail: () => {
        wx.hideLoading();
        this.setData({
          showTextAndTitle: true,
          content: '网络异常, 请稍后重试',
        });
      }
    });
  },

})