<view>
  <view class="container">
    <view class="form-group">
      <t-input disabled label="{{reservationIdentityName}}代码" t-class-input="app-t-class" align="right" value="{{driver.customerId}}"></t-input>
      <t-input disabled label="{{reservationIdentityName}}名称" t-class-input="app-t-class" align="right" value="{{driver.customerName}}"></t-input>

      <t-input data-key="driverName" label="姓名" t-class-input="app-t-class" align="right" value="{{driverName}}" bindchange="onInputChang">
        <t-tag slot="suffix" class="margin-16" theme="{{statusName == '10' ? 'primary' : 'success'}}">{{statusName == '10' ? '新增' : '生效'}}</t-tag>
      </t-input>

      <t-input data-key="tel" label="手机号" placeholder="手机号" align="right" value="{{tel}}" bindchange="onInputChang"></t-input>
      <t-input data-key="driverIdentity" label="身份证号" placeholder="身份证号" align="right" value="{{driverIdentity}}" bindchange="onInputChang"></t-input>

      <t-input placeholder="输入验证码" value="{{code}}" data-key="code" type="number" bindchange="onInputChang" borderless="{{true}}" maxlength="6">
        <t-button slot="suffix" theme="primary" size="extra-small" bind:tap="sendCode" variant="outline"> 发送验证码 </t-button>
      </t-input>


      <t-divider content="车牌号" align="left" />

      <view wx:for="{{vehicleNo}}" wx:key="index">
        <t-input disabled value="{{item}}" borderless="{{true}}" t-class-input="app-t-class">
          <t-button slot="suffix" theme="danger" size="extra-small" bind:tap="removeVehicleNo" variant="outline" data-key="{{index}}"> 删除车牌号 </t-button>
        </t-input>
      </view>

    </view>
  </view>

  <view class="bottom-container">

    <!-- 第一行：删除图标和保存按钮 -->
    <view class="button-row">

      <!-- 左侧的删除图标 -->
      <view class="icon-wrapper">
        <t-button size="large" theme="danger" bind:tap="delete" style="width: 100%;">删除司机</t-button>
        <!-- <t-icon name="delete" color="red" size="30" bind:tap="delete"></t-icon> -->
      </view>

      <!-- 右侧的保存按钮 -->
      <view class="save-button-wrapper">
        <t-button theme="primary" size="large" style="width: 100%;" bind:tap="save">
          保存
        </t-button>
      </view>

    </view>

    <!-- 第二行：增加车牌号按钮 -->
    <view class="plate-button-wrapper">
      <t-button theme="light" size="large" bind:tap="showDialog" block>
        增加车牌号
      </t-button>
    </view>
  </view>
</view>

<t-dialog visible="{{showWithInput}}" title="增加车牌号" content="非新能源车牌，请关闭软键盘后再点确定" confirm-btn="确定" cancel-btn="取消" bind:confirm="closeInsertCarDialog" bind:cancel="closeInsertCarDialog" t-class="t-dialog-class" style="width: 90%;">
  <car-plate-input activeIndex="{{activeIndex}}" codeArray="{{codeArray}}" showProvincePicker="{{showProvincePicker}}" bottom="-95%" data-key="car-number-div" bind:inputcomplete="onInputComplete" slot="content" style="text-align: left;margin-top: 32rpx;border-radius: 8rpx;box-sizing: border-box;"></car-plate-input>
</t-dialog>

<t-dialog visible="{{showWarnConfirm}}" content="确定删除吗?" confirm-btn="{{ { content: '确定', variant: 'base', theme: 'danger' } }}" cancel-btn="取消" bind:confirm="confirmDelete" bind:cancel="confirmDelete" />
<t-dialog visible="{{showTextAndTitle}}" title="提示" content="{{content}}" confirm-btn="{{ confirmBtn }}" bind:confirm="closeDialog" />
<t-toast id="t-toast" />
<t-message id="t-message" />