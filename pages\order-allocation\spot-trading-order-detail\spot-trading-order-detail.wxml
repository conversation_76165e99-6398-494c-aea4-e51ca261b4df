<!--pages/order-allocation/spot-trading-order-detail/spot-trading-order-detail.wxml-->
<view class="container">
  
  <!-- 车辆信息输入区域 -->
  <view class="form-section">
    <view class="section-title">车辆信息</view>

    <!-- 车牌号选择 - 改为自定义带搜索的下拉框 -->
    <view class="car-selector">
      <view class="form-item">
        <view class="form-label">车牌号<text class="required">*</text></view>
        <view class="car-select-btn" bindtap="selectCar">
          <text class="{{carTitle === '请选择车牌号' ? 'placeholder' : 'selected'}}">{{carTitle}}</text>
          <text class="arrow">></text>
        </view>
      </view>

      <!-- 搜索框和下拉列表 -->
      <view class="car-dropdown {{carFlag ? 'show' : ''}}">
        <view class="search-box">
          <input class="search-input" placeholder="{{searchPlaceholder}}" value="{{searchKeyword}}" bindinput="onSearchCar" />
        </view>
        <view class="car-list">
          <view wx:for="{{filteredCarList}}" wx:key="value"
                class="car-item {{selectedCarItem && selectedCarItem.value === item.value ? 'selected' : ''}}"
                bindtap="selectCarItem"
                data-car="{{item}}">
            <view class="car-plate">{{item.label}}</view>
            <view class="driver-name">{{item.driverName}}</view>
          </view>
          <view wx:if="{{filteredCarList.length === 0}}" class="no-data">
            暂无匹配的车牌号
          </view>
        </view>
        <view class="dropdown-actions">
          <view class="btn-cancel" bindtap="cancelSelectCar">取消</view>
          <view class="btn-confirm" bindtap="confirmSelectCar">确认</view>
        </view>
      </view>

      <!-- 遮罩层，点击关闭下拉框 -->
      <view wx:if="{{carFlag}}" class="dropdown-mask" bindtap="cancelSelectCar"></view>
    </view>

    <view class="form-item">
      <view class="form-label">司机姓名<text class="required">*</text></view>
      <input
        class="form-input disabled"
        placeholder="请选择车牌号后自动填入"
        value="{{vehicleInfo.driverName}}"
        disabled
      />
    </view>
    <view class="form-item">
      <view class="form-label">司机电话</view>
      <input
        class="form-input disabled"
        placeholder="请选择车牌号后自动填入"
        value="{{vehicleInfo.driverPhone}}"
        disabled
      />
    </view>
  </view>

  <!-- 货物列表区域 -->
  <view class="form-section">
    <view class="section-title">
      <text>捆包列表</text>
      <view wx:if="{{!isWholeOrder}}" class="select-all-btn" bindtap="toggleSelectAll">
        {{allSelected ? '全不选' : '全选'}}
      </view>
    </view>
    
    <view class="goods-list">
      <scroll-view class="order-scroll {{dataList.length>0 ? '' : 'dataList-empty' }}" scroll-y="true">
        <view wx:for="{{dataList}}" wx:key="index" wx:for-item="item">
          <view class='order-list'>
            <view wx:if="{{!isWholeOrder}}" class="list-name" bindtap="cheackList" data-index="{{index}}">
              <view class="list-name-checked">
                <image src="/assets/image/icon_UncheckBox.png" class='cheacked-img' wx:if="{{!item.flag}}"></image>
                <image src="/assets/image/icon_checkBox.png" class='cheacked-img' wx:if="{{item.flag}}"></image>
              </view>
              <view class="list-name-number">{{item.ladingBillId}}</view>
            </view>
            <view wx:else class="list-name">
              <view class="list-name-number">{{item.ladingBillId}}</view>
            </view>
            <view class="list-name-start">捆包号：{{item.packId}}</view>
            <view class="list-name-start">销售订单子项号：{{item.orderNum}}</view>
            <view class="list-name-start">钢厂资源号：{{item.factoryOrderNum}}</view>
            <view class="list-name-start">母卷号：{{item.m_packId}}</view>
            <view class="list-name-start">客户零件号：{{item.custPartId}}</view>
            <view class="list-name-start">客户零件号名称：{{item.custPartName}}</view>
            <view class="list-name-start">库位名称：{{item.locationId}}</view>
            <view class="list-name-cloum">
              <view class="list-name-cloum-one">品种：{{item.prodTypeId}}</view>
              <view class="list-name-cloum-one">规格：{{item.specsDesc}}</view>
              <view class="list-name-cloum-one">牌号：{{item.shopsign}}</view>
              <view class="list-name-cloum-one">净重：{{item.netWeight}}</view>
              <view class="list-name-cloum-one">毛重：{{item.grossWeight}}</view>
              <view class="list-name-cloum-one">件数：{{item.pieceNum}}</view>
            </view>
          </view>
        </view>
        
        <view wx:if="{{!dataList.length>0}}">
          <view class="my-order-text">暂无货物数据</view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <view wx:if="{{!isWholeOrder}}" class="selected-count">
      已选择：{{selectedList.length}} 件货物
    </view>
    <view wx:else class="selected-count">
      整单配单：{{dataList.length}} 件货物
    </view>
    <button class="submit-btn" bindtap="submitOrder">提交现货交易配单</button>
  </view>

  <t-toast id="t-toast" />
</view> 