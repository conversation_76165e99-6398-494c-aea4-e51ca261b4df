// pages/order-allocation/submit-order/submit-order.js
import { Toast } from 'tdesign-miniprogram';
// import Toast from 'tdesign-miniprogram/toast/index';
const $api = require('../../../api/request')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {},
    dataList: [],
    carTitle: '请选择车牌号',
    carFlag: false,
    carNumber: "",
    carList: [],
    carNumberList: [],
    searchKeyword: '',
    searchPlaceholder: '',
    filteredCarList: [],
    selectedCarItem: null,
    name: "",
    tel: '',
    idCard: '',
    arriveTime: '请选择时间',
    datetimeVisible: false,
    arrivedatetime: '',
    selectCarInfo: [],
    pageNum: 1,
    businessType: '请选择业务类型',
    businessTypeFlag: false,
    businessNumber: '',
    businessNumberList: [{ label: "", value: ' ' }, { label: "废料提货", value: 20 }],
    btnText: '新增',
    entryFlag: 0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let that = this;
    let timestamp = Date.parse(new Date());
    let date = new Date(timestamp);
    //获取当前小时
    let H = date.getHours();
    let time = new Date().toJSON().substring(0, 10) + ' ' + (H < 10 ? '0' + H : H);
    this.setData({
      arriveTime: time,
      arrivedatetime: time,
      userInfo: wx.getStorageSync('userInfo'),
      pageNum: options.pageNum
    })
    const eventChannel = this.getOpenerEventChannel()
    // 监听acceptDataFromOpenerPage事件，获取上一页面通过eventChannel传送到当前页面的数据
    eventChannel.on('acceptDataFromLoadingOrderList', function (data) {
      console.log(data)
      that.setData({
        dataList: data.data
      })
    })
    //获取车牌信息
    this.getCarInfo()
  },
  // 查询车辆信息
  getCarInfo() {
    const user = wx.getStorageSync('userInfo');
    const { reservationIdentity } = user;
    let userInfoList = wx.getStorageSync('userInfoList').filter(u => u.reservationIdentity == reservationIdentity);
    const customerIds = userInfoList.map(m => m.customerId);

    var data = {
      "segNo": this.data.userInfo.segNo,
      "customerId": customerIds,
      "tel": this.data.userInfo.tel,
    }
    $api.request('S_LI_RL_0076', '', data).then((res) => {
      console.log(res.vehicleList)
      let arr = []
      if (res.vehicleList.length > 0) {
        arr = res.vehicleList.map(item => {
          return { 
            label: item.vehicleNo, 
            value: item.vehicleNo,
            driverName: item.driverName || '未知司机',
            tel: item.tel,
            driverIdentity: item.driverIdentity,
            displayText: `${item.vehicleNo} (${item.driverName || '未知司机'})`
          };
        })
      }
      this.setData({
        carNumberList: arr,
        carList: res.vehicleList,
        filteredCarList: arr,
      });

      const editData = wx.getStorageSync('editData');
      if (editData) {
        this.carChange({ detail: { value: [editData.vehicleNo] } });
        this.setData({
          btnText: '修改',
        })
      }


    }).catch((err) => {
      console.log(err)
    })
  },
  // 选车牌
  selectCar() {
    // 如果当前已经有选中的车牌号，找到对应的车牌项作为默认选中
    let defaultSelectedItem = null;
    if (this.data.carNumber && this.data.name) {
      defaultSelectedItem = this.data.carNumberList.find(item => 
        item.value === this.data.carNumber && item.driverName === this.data.name
      );
    }
    
    this.setData({
      carFlag: true,
      searchKeyword: '',
      searchPlaceholder: '输入车牌号或司机姓名进行筛选',
      filteredCarList: this.data.carNumberList,
      selectedCarItem: defaultSelectedItem || null
    })
  },
  // 新增：搜索车牌号
  onSearchCar(e) {
    const keyword = e.detail.value; // 保持原始大小写
    const filteredList = this.data.carNumberList.filter(item => {
      // 不区分大小写匹配车牌号或司机姓名，但保持显示原始大小写
      return item.label.toLowerCase().includes(keyword.toLowerCase()) ||
             item.driverName.toLowerCase().includes(keyword.toLowerCase());
    });
    
    this.setData({
      searchKeyword: keyword, // 保持用户输入的原始大小写
      filteredCarList: filteredList
    });
  },
  // 新增：选择车牌项
  selectCarItem(e) {
    const carItem = e.currentTarget.dataset.car;
    // 如果点击的是已选中的项，则取消选中
    const isCurrentlySelected = this.data.selectedCarItem && 
                               this.data.selectedCarItem.value === carItem.value &&
                               this.data.selectedCarItem.driverName === carItem.driverName;
    
    this.setData({
      selectedCarItem: isCurrentlySelected ? null : carItem
    });
  },
  // 新增：确认选择车牌
  confirmSelectCar() {
    if (!this.data.selectedCarItem) {
      this.showToast('请选择车牌号');
      return;
    }
    
    // 找出选中的那条司机数据
    let selectCarInfo = this.data.carList.filter(item => {
      return item.vehicleNo === this.data.selectedCarItem.value &&
             item.driverName === this.data.selectedCarItem.driverName
    });
    
    this.setData({
      carNumber: this.data.selectedCarItem.value,
      carTitle: this.data.selectedCarItem.displayText,
      selectCarInfo: selectCarInfo,
      name: this.data.selectedCarItem.driverName,
      tel: this.data.selectedCarItem.tel,
      idCard: this.data.selectedCarItem.driverIdentity,
      carFlag: false,
      searchPlaceholder: ''
    });
  },
  // 新增：取消选择车牌
  cancelSelectCar() {
    this.setData({
      carFlag: false,
      searchKeyword: '',
      searchPlaceholder: '',
      selectedCarItem: null
    });
  },
  carChange(e) {
    //找出选中的那条司机数据
    let selectCarInfo = this.data.carList.filter(item => {
      return item.vehicleNo === e.detail.value[0]
    })
    // 构建显示文本
    const displayText = `${e.detail.value[0]} (${selectCarInfo[0].driverName || '未知司机'})`;
    
    this.setData({
      carNumber: e.detail.value[0],
      carTitle: displayText,
      selectCarInfo: selectCarInfo,
      name: selectCarInfo[0].driverName,
      tel: selectCarInfo[0].tel,
      idCard: selectCarInfo[0].driverIdentity,
    });
  },
  // 姓名
  onInputName(e) {
    this.setData({
      name: e.detail.value,
    })
  },
  // 手机号
  onInputTel(e) {
    this.setData({
      tel: e.detail.value,
    })
  },
  // 身份证号
  onInputId(e) {
    this.setData({
      idCard: e.detail.value,
    })
  },
  // 业务类型
  selectBusinessType() {
    this.setData({
      businessTypeFlag: true
    })
  },
  businessChange(e) {
    if (e.detail.label[0] == '空') {
      this.setData({
        businessNumber: '',
        businessType: '请选择业务类型'
      })
    } else {
      this.setData({
        businessNumber: e.detail.value[0],
        businessType: e.detail.label[0]
      })
    }
  },
  // 选时间
  selectTime() {
    this.setData({
      datetimeVisible: true,
    })
  },
  onConfirm(e) {
    this.setData({
      arriveTime: e.detail.value,
      arrivedatetime: e.detail.value,
      datetimeVisible: false,
    })
  },
  hidePicker() {
    this.setData({
      datetimeVisible: false,
    })
  },
  // 新增配单
  insert() {
    const editData = wx.getStorageSync('editData');
    let serviceId = 'S_LI_RL_0077';
    let editObj = '';
    if (editData) {
      serviceId = 'S_LI_RL_0156';
      editObj = {
        segNo: editData.segNo,
        allocateVehicleNo: editData.allocateVehicleNo,
        recCreator: this.data.userInfo.tel,
        recCreatorName: this.data.userInfo.administrator,
      };
    }

    if (this.cheackForm()) {
      let data = {
        ...editObj,
        "result": this.data.dataList,
        "result2": [
          {
            ...this.data.selectCarInfo[0],
            "time": this.data.arrivedatetime,
            // "businessType":this.data.businessNumber,
            "customerId": this.data.userInfo.customerId,
            "customerName": this.data.userInfo.customerName,
            "tel": this.data.userInfo.tel,
            "administrator": this.data.userInfo.administrator
          }
        ],
        entryFlag: '0',
      }
      $api.request(serviceId, '', data).then((res) => {
        if (res.__sys__.status === 1) {
          wx.removeStorageSync('editData');
          this.showToast('配单成功');
          setTimeout(() => {
            wx.navigateBack({
              delta: this.data.pageNum - 0,
              success: function (e) {
                var page = getCurrentPages().pop();
                if (page == undefined || page == null) return;
                page.onLoad(); // 刷新数据
              }
            })
          }, 1000);
        } else {
          this.showToast(res.__sys__.msg)
        }
      }).catch((err) => {
        console.log(err)
      })
    }

  },

    /**
   * 是否补单单选按钮事件
   * @param {*} e 
   */
  onBringChange(e) {
    this.setData({
      entryFlag: e.detail.value
    });
  },

  // 判断数据是否填写完成
  cheackForm() {
    let flag = false
    if (this.data.carNumber) {
      flag = true
      return flag;
    } else {
      this.showToast('请选择车牌号')
      return flag
    }
  },

  showToast(e) {
    Toast({
      context: this,
      selector: '#t-toast',
      message: e,
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})