import { Toast } from 'tdesign-miniprogram';
const $api = require('../../../api/request')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {},
    // 车辆相关
    carList: [],
    selectedCar: '请选择车辆',
    selectedCarValue: '',
    carVisible: false,
    
    // 装卸点相关
    loadingPointList: [],
    selectedLoadingPoint: '请选择装卸点',
    selectedLoadingPointValue: [],
    loadingPointVisible: false,
    selectedPointNames: [], // 存储选中的装卸点名称
    
    // 提交按钮状态
    submitLoading: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const userInfo = wx.getStorageSync('userInfo');
    this.setData({
      userInfo
    });
    
    // 获取车辆列表
    this.getCarList();
    // 获取装卸点列表
    this.getLoadingPointList();
  },

  /**
   * 获取车辆列表
   */
  getCarList() {
    const { userInfo } = this.data;
    const { segNo, driverName, tel } = userInfo;
    
    $api.request('S_LI_RL_0191', '', {
      tel,
      segNo,
      driverName
    }).then((res) => {
      console.log('车辆列表:', res);
      if (res.result && res.result.length > 0) {
        const carOptions = res.result.map(car => ({
          label: car,
          value: car,
          // ...car
        }));
        this.setData({
          carList: carOptions
        });
      } else {
        this.setData({
          carList: []
        });
        Toast({
          context: this,
          selector: '#t-toast',
          message: "暂无车辆数据",
        });
      }
    }).catch((err) => {
      console.log('获取车辆列表失败:', err);
      Toast({
        context: this,
        selector: '#t-toast',
        message: "获取车辆列表失败",
      });
    });
  },

  /**
   * 获取装卸点列表
   */
  getLoadingPointList() {
    const { userInfo } = this.data;
    
    $api.request('S_LI_RL_0158', '', {
      segNo: userInfo.segNo,
    }).then((res) => {
      console.log('装卸点列表:', res);
      if (res.list && res.list.length > 0) {
        const pointOptions = res.list.map(point => ({
          label: point.handPointName,
          value: point.handPointId,
          ...point
        }));
        this.setData({
          loadingPointList: pointOptions
        });
      } else {
        this.setData({
          loadingPointList: []
        });
        Toast({
          context: this,
          selector: '#t-toast',
          message: "暂无装卸点数据",
        });
      }
    }).catch((err) => {
      console.log('获取装卸点列表失败:', err);
      Toast({
        context: this,
        selector: '#t-toast',
        message: "获取装卸点列表失败",
      });
    });
  },

  /**
   * 打开车辆选择器
   */
  selectCar() {
    if (this.data.carList.length === 0) {
      Toast({
        context: this,
        selector: '#t-toast',
        message: "暂无可选车辆",
      });
      return;
    }
    this.setData({
      carVisible: true
    });
  },

  /**
   * 车辆选择确认
   */
  onCarChange(e) {
    const { value, label } = e.detail;
    this.setData({
      selectedCar: label[0],
      selectedCarValue: value[0]
    });
  },

  /**
   * 打开装卸点选择器
   */
  selectLoadingPoint() {
    if (this.data.loadingPointList.length === 0) {
      Toast({
        context: this,
        selector: '#t-toast',
        message: "暂无可选装卸点",
      });
      return;
    }
    this.setData({
      loadingPointVisible: true
    });
  },

  /**
   * 装卸点多选变更
   */
  onLoadingPointChange(e) {
    const selectedValues = e.detail.value;
    const { loadingPointList } = this.data;
    
    // 获取选中的装卸点信息
    const selectedPoints = loadingPointList.filter(point => 
      selectedValues.includes(point.value)
    );
    
    const selectedNames = selectedPoints.map(point => point.label);
    const displayText = selectedNames.length > 0 
      ? `已选择 ${selectedNames.length} 个装卸点` 
      : '请选择装卸点';
    
    this.setData({
      selectedLoadingPointValue: selectedValues,
      selectedPointNames: selectedNames.join('、'),
      selectedLoadingPoint: displayText
    });
  },

  /**
   * 提交表单
   */
  handleSubmit() {
    const { selectedCarValue, selectedLoadingPointValue, userInfo, loadingPointList } = this.data;
    
    // 验证表单
    if (!selectedCarValue) {
      Toast({
        context: this,
        selector: '#t-toast',
        message: "请选择车辆",
      });
      return;
    }
    
    if (!selectedLoadingPointValue || selectedLoadingPointValue.length === 0) {
      Toast({
        context: this,
        selector: '#t-toast',
        message: "请选择装卸点",
      });
      return;
    }

    this.setData({
      submitLoading: true
    });

    // 构造装卸点数据，支持多选，与预约页面保持一致
    const selectedPoints = loadingPointList.filter(point => 
      selectedLoadingPointValue.includes(point.value)
    );
    const handPoint = selectedPoints;

    const app = getApp();
    wx.showLoading({
      title: '新增中',
    });

    // 构造提交数据，与预约页面的 addLoadPoint 接口保持一致
    const data = {
      handPoint: handPoint,
      segNo: userInfo.segNo,
      vehicleNo: selectedCarValue,
      driverName: userInfo.administrator ?? userInfo.driverName,
      driverTel: userInfo.tel,
      serviceId: 'S_LI_RL_0159',
    };

    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data,
      success: (res) => {
        wx.hideLoading();
        this.setData({
          submitLoading: false
        });
        
        const result = res.data;
        if (result.__sys__?.status == -1) {
          Toast({
            context: this,
            selector: '#t-toast',
            message: result.__sys__.msg,
          });
          return;
        }

        Toast({
          context: this,
          selector: '#t-toast',
          message: "新增成功",
        });
        
        // 清空表单
        this.resetForm();
      },
      fail: () => {
        wx.hideLoading();
        this.setData({
          submitLoading: false
        });
        
        Toast({
          context: this,
          selector: '#t-toast',
          message: "网络异常，请稍后重试",
        });
      }
    });
  },

  /**
   * 确认装卸点选择
   */
  confirmLoadingPoints() {
    this.setData({
      loadingPointVisible: false
    });
  },

  /**
   * 取消装卸点选择
   */
  cancelLoadingPoints() {
    this.setData({
      loadingPointVisible: false
    });
  },

  /**
   * 重置表单
   */
  resetForm() {
    this.setData({
      selectedCar: '请选择车辆',
      selectedCarValue: '',
      selectedLoadingPoint: '请选择装卸点',
      selectedLoadingPointValue: [],
      selectedPointNames: [],
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
}) 