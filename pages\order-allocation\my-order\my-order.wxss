/* pages/order-allocation/my-order/my-order.wxss */
/* page{
    width: 100%;
    height: 100%;
} */

/* 搜索区域 */
.search-container {
  padding: 20rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e9ecef;
}

/* 时间筛选区域 */
.time-filter {
  padding: 20rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.time-filter-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.time-label {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  min-width: 140rpx;
}

.time-picker {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
  padding: 16rpx 20rpx;
  margin-left: 20rpx;
  transition: all 0.3s ease;
}

.time-picker:active {
  border-color: #1890ff;
  background-color: #f6f8ff;
}

.time-selected {
  color: #333333;
  font-size: 28rpx;
}

.time-placeholder {
  color: #999999;
  font-size: 28rpx;
}

.time-icon {
  font-size: 32rpx;
  color: #666666;
}

.time-clear {
  align-self: flex-end;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  color: #1890ff;
  background-color: #ffffff;
  border: 2rpx solid #1890ff;
  border-radius: 20rpx;
  text-align: center;
  transition: all 0.3s ease;
  min-width: 100rpx;
}

.time-clear:active {
  background-color: #1890ff;
  color: #ffffff;
}

.container{
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    box-sizing: border-box;
}
.my-list{
    width:calc(100% - 64rpx);
    padding:20rpx 30rpx;
    background-color: #ffffff;
    margin: 0 auto 20rpx;
    border-radius: 20rpx;
    box-sizing: border-box;
    font-size: 16px;
    line-height: 1.5;
    position: relative;
}
.my-list-li{
    display: flex;
    margin-bottom:5px;
}
.my-list-li-name{
    min-width:80px;
    color:#666666;
}
.my-list-li-value{
    color:#333333;
}
.my-list-li-button{
    width: 100%;
    margin-top: 24rpx;
    padding-top: 20rpx;
    border-top: 1rpx solid #f0f2f5;
    display: flex;
    flex-wrap: nowrap;
    gap: 8rpx;
    justify-content: flex-end;
    align-items: center;
    overflow: hidden;
}

.start-queuing{
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    border-radius: 28rpx;
    min-width: 100rpx;
    max-width: 140rpx;
    height: 56rpx;
    color: #ffffff;
    font-size: 22rpx;
    font-weight: 500;
    text-align: center;
    line-height: 56rpx;
    box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    padding: 0 12rpx;
    box-sizing: border-box;
    flex: 1;
    white-space: nowrap;
}

.start-queuing::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.start-queuing:active::before {
    left: 100%;
}

.start-queuing:active {
    transform: scale(0.96);
    box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.4);
}

.canle-queuing{
    background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
    border-radius: 28rpx;
    min-width: 100rpx;
    max-width: 140rpx;
    height: 56rpx;
    color: #ffffff;
    font-size: 22rpx;
    font-weight: 500;
    text-align: center;
    line-height: 56rpx;
    box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    padding: 0 12rpx;
    box-sizing: border-box;
    flex: 1;
    white-space: nowrap;
}

.canle-queuing::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.canle-queuing:active::before {
    left: 100%;
}

.canle-queuing:active {
    transform: scale(0.96);
    box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.4);
}

/* 特殊情况：只有一个按钮时，居中显示 */
.my-list-li-button > view:only-child {
    margin: 0 auto;
    flex: none;
    max-width: 200rpx;
}

/* 为不同类型的按钮添加特殊样式 */
.edit-btn {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%) !important;
    box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3) !important;
}

.edit-btn:active {
    box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.4) !important;
}

.edit-btn::before {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent) !important;
}

/* 暂停按钮样式 */
.pause-btn {
    background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
    border-radius: 28rpx;
    min-width: 100rpx;
    max-width: 140rpx;
    height: 56rpx;
    color: #ffffff;
    font-size: 22rpx;
    font-weight: 500;
    text-align: center;
    line-height: 56rpx;
    box-shadow: 0 4rpx 12rpx rgba(250, 140, 22, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    padding: 0 12rpx;
    box-sizing: border-box;
    flex: 1;
    white-space: nowrap;
}

.pause-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.pause-btn:active::before {
    left: 100%;
}

.pause-btn:active {
    transform: scale(0.96);
    box-shadow: 0 2rpx 8rpx rgba(250, 140, 22, 0.4);
}

.dataList-empty{
    width: 100%;
    height: 100%;
    display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}
.my-order-img{
    width: 150px;
    height: 150px;
    display: block;
}
.my-order-text{
    text-align: center;
	font-size: 16px;
	color: rgb(51, 51, 51);
}

.block-center{
    color: var(--td-text-color-secondary);
   position: relative;
    width: 90vw;
    height: 200px;
    overflow: hidden;
  }
.block-title{
    text-align: center;
    line-height:2;
    font-size: 18px;
}
.t-popup--center {
    top:40% !important;
}
.bottom{
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 50px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}
.bottom-cancle,.bottom-confirm{
    flex: 1;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 32rpx;
}
.bottom-cancle {
    color: #666;
    border-right: 1rpx solid #eee;
}
.bottom-confirm {
    color: #0052d9;
}

/* 状态筛选器样式 */
.status-filter {
  padding: 16rpx 32rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
  display: flex;
  align-items: center;
}

.filter-label {
  font-size: 28rpx;
  color: #333333;
  margin-right: 20rpx;
  font-weight: 500;
}

.filter-buttons {
  display: flex;
  gap: 16rpx;
}

.filter-btn {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  border-radius: 20rpx;
  background-color: #ffffff;
  color: #666666;
  border: 2rpx solid #d9d9d9;
  text-align: center;
  transition: all 0.3s ease;
  font-weight: 400;
}

.filter-btn.active {
  background-color: #1890ff;
  color: #ffffff;
  border-color: #1890ff;
  font-weight: 500;
}

.filter-btn:active {
  transform: scale(0.96);
}

/* 时间选择器弹窗样式 */
.time-picker-popup {
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
}

.time-picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #fafafa;
}

.time-picker-cancel {
  color: #666666;
  font-size: 28rpx;
  padding: 8rpx 16rpx;
}

.time-picker-title {
  color: #333333;
  font-size: 32rpx;
  font-weight: 500;
}

.time-picker-confirm {
  color: #1890ff;
  font-size: 28rpx;
  font-weight: 500;
  padding: 8rpx 16rpx;
}

.time-picker-content {
  padding: 40rpx 32rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333333;
  background-color: #ffffff;
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  margin: 20rpx;
}

/* 右上角启动状态标签样式 */
.start-status-tag {
    position: absolute;
    top: 20rpx;
    right: 30rpx;
    padding: 4rpx 8rpx;
    border-radius: 8rpx;
    font-size: 22rpx;
    font-weight: 500;
    z-index: 10;
}

.start-status-tag.started {
    background-color: rgba(24, 144, 255, 0.15);
    color: #1890ff;
    border: 1rpx solid #1890ff;
}

.start-status-tag.not-started {
    background-color: rgba(255, 77, 79, 0.15);
    color: #ff4d4f;
    border: 1rpx solid #ff4d4f;
}

.start-status-tag.completed {
    background-color: rgba(82, 196, 26, 0.15);
    color: #52c41a;
    border: 1rpx solid #52c41a;
}