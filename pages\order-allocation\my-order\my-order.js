// pages/order-allocation/my-order/my-order.js
import {
  Toast
} from 'tdesign-miniprogram';
const $api = require('../../../api/request')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {},
    dataList: [],
    dataOne: {},
    visible: false,
    factoryArea: "请选择厂区",
    factoryVisible: false,
    factoryValue: '',
    customerCodeList: [],
    unloadingPoint: "请选择卸货点",
    unloadingPointValue: '',
    unloadingPointList: [],
    customerList: [], // 厂区数据 
    curCustomer: '', // 当前选择的厂区
    showTextAndTitle: false,
    confirmBtn: { content: '确定', variant: 'base' },
    content: '', // 提示信息
    statusFlags: ['20'], // 状态筛选数组，默认显示生效状态
    status20Active: true, // 生效状态是否激活
    status99Active: false, // 完成状态是否激活 // 汇总信息
    searchValue: '', // 搜索的提单号
    // 时间查询相关
    startDate: '', // 起始时间
    endDate: '', // 截止时间
    timePickerVisible: false, // 时间选择器显示状态
    timePickerTitle: '', // 时间选择器标题
    timePickerValue: '', // 时间选择器当前值
    currentTimeType: '', // 当前选择的时间类型：start 或 end
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      userInfo: wx.getStorageSync('userInfo'),
      status20Active: this.data.statusFlags.includes('20'),
      status99Active: this.data.statusFlags.includes('99')
    })
    this.getList()
  },

  /**
   * 跳转到详情
   */
  toInfo(e) {
    const data = this.data.dataList[e.currentTarget.dataset.index];
    wx.navigateTo({
      url: '/pages/order-allocation/my-order/my-order-info/my-order-info',
      events: {
        // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
        acceptDataFromOpenedPage: () => { },
      },
      success: (res) => {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('acceptDataFromOpenerPage', { data, })
      }
    });
  },

  getUnloadingPoint(e) {
    const {
      factoryArea,
      factoryBuilding
    } = this.data.curCustomer;
    const data = {
      "segNo": this.data.userInfo.segNo,
      factoryArea,
      factoryBuilding,
    }
    $api.request('S_LI_RL_0051', '', data).then((res) => {
      // console.log(res)
      const list = res.list.map(m => {
        return {
          label: m.handPointName,
          value: m.handPointId,
        };
      });
      this.setData({
        unloadingPointList: list
      })
    }).catch((err) => {
      console.log(err)
    })
  },

  // 切换状态筛选（多选）
  toggleStatus(e) {
    const status = e.currentTarget.dataset.status;
    let statusFlags = [...this.data.statusFlags];
    
    if (statusFlags.includes(status)) {
      // 如果已选中，则取消选中（但至少保留一个）
      if (statusFlags.length > 1) {
        statusFlags = statusFlags.filter(flag => flag !== status);
      }
    } else {
      // 如果未选中，则添加到选中列表
      statusFlags.push(status);
    }
    
    this.setData({
      statusFlags: statusFlags,
      status20Active: statusFlags.includes('20'),
      status99Active: statusFlags.includes('99'),
      dataList: [] // 清空当前数据
    });
    // 重新加载数据（保持搜索条件）
    this.getList();
  },

  //选择厂区
  selectFactory() {
    this.setData({
      factoryVisible: true
    })
  },

  onPickerChange(e) {
    const {
      value,
      label
    } = e.detail;
    const valueList = value[0].split('-');
    const factoryArea = valueList[0];
    const factoryBuilding = valueList[1];

    const curCustomer = this.data.customerList.find(c => c.factoryArea == factoryArea && c.factoryBuilding == factoryBuilding);
    if (curCustomer && this.data.factoryValue != value[0]) {
      this.setData({
        factoryArea: label[0], // 显示
        factoryValue: value,
        curCustomer,
        unloadingPoint: '请选择卸货点',
        unloadingPointValue: ''
      });
      this.getUnloadingPoint(e.detail.value[0])
    }
  },
  // 选者卸货点
  selectPoint(e) {
    if (!this.data.factoryValue) {
      Toast({
        context: this,
        selector: '#t-toast',
        message: "请先选择厂区",
      });
      return
    }
    this.setData({
      unloadingPointVisible: true
    })
  },

  onPointChange(e) {
    this.setData({
      unloadingPoint: e.detail.label[0],
      unloadingPointValue: e.detail.value[0]
    })
    //调用启动排队事件
  },

  /**
   * 清空数据
   */
  handleCancle() {
    this.setData({
      visible: false,
      dataOne: {},
      factoryArea: '请选择厂区',
      factoryValue: '',
      unloadingPoint: '请选择卸货点',
      unloadingPointValue: '',
      unloadingPointList: []
    })
  },

  /**
   * 启动排队弹出框点击事件
   */
  handleConfirm() {
    if (this.data.unloadingPointValue && this.data.factoryValue) {
      // 调用启动排队事件
      this.getAddressT()
    } else {
      Toast({
        context: this,
        selector: '#t-toast',
        message: "厂区和卸货点必填",
      });
    }
  },

  getList() {
    const { userInfo } = this.data;
    const user = wx.getStorageSync('userInfo');
    const { reservationIdentity } = user;
    let userInfoList = wx.getStorageSync('userInfoList').filter(u => u.reservationIdentity == reservationIdentity);
    const customerIds = userInfoList.map(m => m.customerId);
    
    // 时间格式转换函数
    const formatDateTime = (dateStr, isEndDate = false) => {
      if (!dateStr) return '';
      // 将 YYYY-MM-DD 格式转换为 YYYYMMDDHHMMSS 格式
      const formattedDate = dateStr.replace(/-/g, '');
      return isEndDate ? formattedDate + '235959' : formattedDate + '000000';
    };
    
    let data = {
      "segNo": userInfo.segNo,
      "tel": userInfo.tel,
      "driverName": userInfo.driverName,
      reservationIdentity: userInfo.reservationIdentity,
      statusFlags: this.data.statusFlags, // 添加状态筛选参数数组
      customerId: customerIds,
      ladingBillId: this.data.searchValue || '', // 添加提单号查询参数
      startDate: formatDateTime(this.data.startDate, false), // 起始时间查询参数，格式：YYYYMMDD000000
      endDate: formatDateTime(this.data.endDate, true), // 截止时间查询参数，格式：YYYYMMDD235959
    }

    $api.request('S_LI_RL_0056', '', data).then((res) => {
      res.result.forEach((item, i) => {
        if (item.allocType == '10') {
          item.allocType = '装货配单'
        } else if (item.allocType == '20') {
          item.allocType = '卸货配单'
        } else if (item.allocType == '30') {
          item.allocType = '废次材装货配单'
        }
        item['recCreateTimeStr'] = item.recCreateTime.replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1/$2/$3 $4:$5:$6');
      })
      this.setData({
        dataList: res.result
      })
    }).catch((err) => {
      console.log(err)
    })
  },

  /**
   * 搜索框输入
   */
  onSearchInput(e) {
    this.setData({
      searchValue: e.detail.value
    });
  },

  /**
   * 搜索
   */
  onSearch() {
    this.setData({
      dataList: [] // 清空当前数据
    });
    this.getList();
  },

  /**
   * 清空搜索
   */
  onClearSearch() {
    this.setData({
      searchValue: ''
    });
    this.getList();
  },

  /**
   * 选择起始时间
   */
  selectStartTime() {
    const currentDate = this.data.startDate || new Date().toISOString().split('T')[0];
    this.setData({
      timePickerVisible: true,
      timePickerTitle: '选择起始时间',
      timePickerValue: currentDate,
      currentTimeType: 'start'
    });
  },

  /**
   * 选择截止时间
   */
  selectEndTime() {
    const currentDate = this.data.endDate || new Date().toISOString().split('T')[0];
    this.setData({
      timePickerVisible: true,
      timePickerTitle: '选择截止时间',
      timePickerValue: currentDate,
      currentTimeType: 'end'
    });
  },

  /**
   * 时间选择器日期改变事件
   */
  onTimePickerDateChange(e) {
    this.setData({
      timePickerValue: e.detail.value
    });
  },

  /**
   * 时间选择器确认事件
   */
  onTimePickerConfirm() {
    const selectedDate = this.data.timePickerValue;
    const { currentTimeType } = this.data;
    
    if (currentTimeType === 'start') {
      this.setData({
        startDate: selectedDate,
        timePickerVisible: false
      });
    } else if (currentTimeType === 'end') {
      this.setData({
        endDate: selectedDate,
        timePickerVisible: false
      });
    }
    
    // 如果起始时间晚于截止时间，需要调整
    if (this.data.startDate && this.data.endDate && this.data.startDate > this.data.endDate) {
      if (currentTimeType === 'start') {
        this.setData({ endDate: '' });
        Toast({
          context: this,
          selector: '#t-toast',
          message: "起始时间不能晚于截止时间",
        });
      } else {
        this.setData({ startDate: '' });
        Toast({
          context: this,
          selector: '#t-toast',
          message: "截止时间不能早于起始时间",
        });
      }
    } else {
      // 自动刷新数据
      this.getList();
    }
  },

  /**
   * 时间选择器取消事件
   */
  onTimePickerCancel() {
    this.setData({
      timePickerVisible: false
    });
  },

  /**
   * 时间选择器弹窗显示状态改变
   */
  onTimePickerVisibleChange(e) {
    this.setData({
      timePickerVisible: e.detail.visible
    });
  },

  /**
   * 清空时间筛选条件
   */
  clearTimeFilter() {
    this.setData({
      startDate: '',
      endDate: ''
    });
    this.getList();
  },

  /**
   * 开始启动叫号
   * @param {*} location 位置信息
   */
  startQueuing(location) {
    let that = this;
    var data = {
      "result": [{
        ...this.data.dataOne,
        "factoryArea": this.data.curCustomer.factoryArea,
        "targetHandPointId": this.data.unloadingPointValue,
        "latitude": location.latitude,
        "longitude": location.longitude,
        "horizontalAccuracy": location.horizontalAccuracy
      }],
      driverTel: this.data.userInfo.tel,
      isNotShowToast: true,
    }
    $api.request('S_LI_RL_0067', '', data).then((res) => {
      Toast({
        context: this,
        selector: '#t-toast',
        message: res.__sys__.msg,
      });
      that.handleCancle();
      that.getList();
    }).catch((err) => {
      that.setData({
        showTextAndTitle: true,
        content: err.data.__sys__.msg,
      });
      that.handleCancle();
    })
  },

  // 启动排队
  getAddress(e) {
    const info = e.currentTarget.dataset['item'];
    this.setData({
      dataOne: info
    });
    this.getAddressT();
  },

  // 取消叫号
  canclePai(e) {
    const info = e.currentTarget.dataset['item'];
    $api.request('S_LI_RL_0151', '', info).then((res) => {
      Toast({
        context: this,
        selector: '#t-toast',
        message: res.__sys__.msg,
      });
      this.handleCancle();
      this.getList();
    }).catch((err) => {
      this.handleCancle();
    });
  },

  /**
   * 暂停配单
   * @param {*} e 
   */
  pauseOrder(e) {
    const info = e.currentTarget.dataset['item'];
    const data = {
      segNo: this.data.userInfo.segNo,
      vehicleNo: info.vehicleNo,
      allocateVehicleNo: info.allocateVehicleNo,
      carTraceNo: info.carTraceNo
    };
    
    $api.request('S_LI_RL_0192', '', data).then((res) => {
      Toast({
        context: this,
        selector: '#t-toast',
        message: res.__sys__.msg,
      });
      this.getList();
    }).catch((err) => {
      Toast({
        context: this,
        selector: '#t-toast',
        message: err.data?.__sys__?.msg || '暂停失败',
      });
    });
  },

  /**
   * 取消配单
   * @param {*} e 
   */
  cancleOrder(e) {
    const info = e.currentTarget.dataset['item'];
    const user = wx.getStorageSync('userInfo');
    const data = {
      ...info,
      packIdList: [],
      recRevisor: user.tel,
      recRevisorName: user.administrator ?? user.driverName,

    };
    $api.request('S_LI_RL_0154', '', data).then((res) => {
      Toast({
        context: this,
        selector: '#t-toast',
        message: res.__sys__.msg,
      });
      this.handleCancle();
      this.getList();
    }).catch((err) => {
      this.handleCancle();
    });
  },

  editOrder(e) {
    let info = e.currentTarget.dataset['item'];
    // 同时放到缓存中, 以防在提交配单页面返回上一层,丢失数据
    wx.setStorageSync('editData', info);
    wx.navigateTo({
      url: '/pages/order-allocation/loading-order/loading-order?isEdit=true',
      success: function (res) {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('acceptDataFromLoadingOrder', { data: info })
      }
    });
  },

  /**
   * 检查并申请定位权限
   */
  checkLocationPermission() {
    return new Promise((resolve, reject) => {
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.userLocation'] === false) {
            // 用户曾经拒绝过，需要引导用户手动开启
            wx.showModal({
              title: '需要位置权限',
              content: '为了准确获取您的位置信息，请在设置中开启位置权限',
              showCancel: true,
              cancelText: '取消',
              confirmText: '去设置',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  wx.openSetting({
                    success: (settingRes) => {
                      if (settingRes.authSetting['scope.userLocation']) {
                        resolve();
                      } else {
                        reject('用户未开启位置权限');
                      }
                    },
                    fail: () => {
                      reject('打开设置失败');
                    }
                  });
                } else {
                  reject('用户取消授权');
                }
              }
            });
          } else if (res.authSetting['scope.userLocation'] === undefined) {
            // 用户未授权过，直接调用授权
            resolve();
          } else {
            // 用户已授权
            resolve();
          }
        },
        fail: () => {
          reject('获取授权设置失败');
        }
      });
    });
  },

  /**
   * 启动排队
   */
  getAddressT() {
    let that = this;
    
    // 先检查权限
    this.checkLocationPermission().then(() => {
      wx.showLoading({
        title: '正在定位中',
        mask: true
      });
      wx.getLocation({
        type: 'wgs84', //wgs84 返回 gps 坐标，gcj02 返回国测局坐标
        altitude: false, //传入 true 会返回高度信息，由于获取高度需要较高精确度，会减慢接
        isHighAccuracy: true, //开启高精度定位，提高定位精度
        highAccuracyExpireTime: 10000, //高精度定位超时时间(ms)，延长到10秒提高定位准确性
        success(res) {
          //隐藏提示框
          wx.hideLoading();
          let location = {};
          location.horizontalAccuracy = res.horizontalAccuracy;
          location.latitude = parseFloat(res.latitude);
          location.longitude = parseFloat(res.longitude);
          that.startQueuing(location);
        },
        fail: (res) => {
          wx.hideLoading();
          
          let content = "";
          // 检查是否是权限问题
          if (res.errMsg && (res.errMsg.includes('auth deny') || res.errMsg.includes('permission'))) {
            content = "定位权限未开启，请按以下步骤操作：\n1. 点击右上角菜单\n2. 选择\"设置\"\n3. 开启\"位置信息\"权限\n4. 返回重新尝试";
          } else if (res.errMsg && res.errMsg.includes('location fail')) {
            content = "定位服务不可用，请检查：\n1. 手机定位服务是否开启\n2. 网络连接是否正常\n3. 是否在室外开阔区域";
          } else {
            content = "定位失败：" + res.errMsg + "\n请检查GPS权限和网络连接";
          }
          
          that.setData({
            showTextAndTitle: true,
            content: content,
          });
        }
      });
    }).catch((error) => {
      this.setData({
        showTextAndTitle: true,
        content: error || '获取定位权限失败',
      });
    });
  },

  /**
 * 关闭错误提示框
 */
  closeDialog() {
    this.setData({
      showTextAndTitle: false,
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})