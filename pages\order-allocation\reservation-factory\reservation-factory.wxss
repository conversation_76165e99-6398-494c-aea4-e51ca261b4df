/* pages/order-allocation/reservation-factory/reservation-factory.wxss */
.container {
    padding: 10px;
    background-color: #f5f5f5;
  }
  .block {
    padding:5px 15px 0 0;
    color: var(--td-text-color-secondary);
    display: flex;
  }
  .block--right {
    width: auto;
    height: 100vh;
  }
  .block-close{
      position: absolute;
      left: -25px;
      top: 0;
      z-index: 99999;
  }
  .reservation-all{
    background-color: #ffffff;
  }
  .reservation-special{
      margin:32rpx 32rpx 0 32rpx;
  }
  .reservation-img{
    display: flex;
  }
  .reservation-img image{
    width: 75px;
    height: 100px;
    display: block;
  }
  .reservation-img-title{
     padding:0 32rpx 16rpx 32rpx;
     font-size: 14px;
     color: #fa6604;
  }
  .reservation-buttom{
      width: 90%;
      background-color: #1953E6;
      color: #ffffff;
      text-align: center;
      margin: 30px auto;
      height: 40px;
      line-height: 40px;
      border-radius:10px;
  }
  .t-cell__title{
      min-width: 75px !important;
  }
  .t-upload__wrapper{
    width: 75px !important;
    height: 100px !important;
  }