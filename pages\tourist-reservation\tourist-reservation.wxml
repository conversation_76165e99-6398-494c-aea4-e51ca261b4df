<!--pages/insert-reservation/insert-reservation.wxml-->
<!-- 在页面的 WXML 文件中实现表单布局 -->
<view class="container">
  <t-input t-class-note="app-t-class" label="姓名" placeholder="请输入姓名" data-key="name" value="{{name}}" bindchange="onInputChang" align="right" />
  <t-input label="手机号" data-key="tel" t-class-input="app-t-class" placeholder="请输入手机号" align="right" value="{{tel}}" bindchange="onInputChang" />
  <t-input label="身份证号" data-key="driverIdentity" t-class-input="app-t-class" placeholder="请输入身份证号" align="right" value="{{driverIdentity}}" bindchange="onInputChang" />

  <t-input label="车牌号" disabled align="right" t-class-input="app-t-class" value="请在下方输入车牌号"></t-input>
  <car-plate-input activeIndex="{{activeIndex}}" codeArray="{{carArray}}" data-key="car-number-div" bind:inputcomplete="onInputComplete"></car-plate-input>

  <t-cell class="mb-16" title="业务类型" t-class-note="app-t-class" arrow hover note="{{type}}" bind:click="onTypePicker" />
  <t-picker visible="{{typeVisible}}" value="{{typeValue}}" data-key="type" title="选择业务类型" cancelBtn="取消" confirmBtn="确认" usingCustomNavbar bindchange="onPickerChange">
    <t-picker-item options="{{typeList}}" />
  </t-picker>

  <t-cell class="mb-16" title="预约起始日期" t-class-note="app-t-class" arrow hover note="{{dateText || ''}}" bind:click="showPicker" t-class="panel-item" />

  <t-cell class="mb-16" title="预约截止日期" t-class-note="app-t-class" arrow hover note="{{deadlineText || ''}}" bind:click="showDeadPicker" t-class="panel-item" />

  <t-date-time-picker title="选择日期和时间" visible="{{dateVisible}}" mode="minute" value="{{date}}" format="YYYYMMDDHHmm" start="{{start}}" bindchange="onColumnChange" bindcancel="hidePicker" />


  <t-date-time-picker title="选择预约截止日期" visible="{{deadVisible}}" mode="minute" value="{{deadDate}}" start="{{deadStart}}" end="{{deadEnd}}" format="YYYYMMDDHHmm" bindchange="onDeadChange" bindcancel="hidePicker" />

  <t-cell class="mb-16" title="拜访单位" t-class-note="app-t-class" arrow hover note="{{visitUnit}}" bind:click="onVisitUnitPicker" />
  <t-picker visible="{{visitUnitVisible}}" value="{{visitUnitValue}}" data-key="visitUnit" title="选择拜访单位" cancelBtn="取消" confirmBtn="确认" usingCustomNavbar bindchange="onPickerChange">
    <t-picker-item options="{{visitUnitList}}" />
  </t-picker>

  <t-cell title="是否有自带货">
    <view slot="note">
      <t-radio-group default-value="0" borderless t-class="box" bind:change="onBringChange">
        <view style="margin-right: 22rpx;display: inline-block;">
          <t-radio block="{{false}}" label="是" value="1" />
        </view>
        <t-radio block="{{false}}" label="否" value="0" />
      </t-radio-group>
    </view>
  </t-cell>

  <view wx:if="{{isBringGood == 1}}">
    <t-textarea label="自带货描述" t-class-label="is-bring-textarea" placeholder="请输自带货描述" disableDefaultPadding="{{true}}" autosize="{{autosize}}" bind:change="onBringTextChange" />
  </view>

  <view class="btn-group">
    <t-button theme="primary" block bind:tap="submit">提交</t-button>
  </view>

  <t-dialog visible="{{showMsg}}" title="通知" content="{{content}}" confirm-btn="{{ confirmMsgBtn }}" bind:confirm="closeMsgDialog" />

  <!-- 确认信息弹框 -->
  <t-dialog visible="{{showTextAndTitle}}" title="{{title}}" confirm-btn="{{ confirmBtn }}" bind:confirm="closeDialog">
    <scroll-view slot="content" type="list" scroll-y class="long-content" id="scrollH" bindscroll="handleScroll">
      <rich-text class="content-container" id="dialogScroll" nodes="{{content}}"></rich-text>
    </scroll-view>
  </t-dialog>

  <t-message id="t-message" />

</view>