<!--pages/resident-car/resident-car.wxml-->
<view class="container">
  <!-- 搜索区域 -->
  <view class="search-container">
    <t-search model:value="{{searchValue}}" placeholder="请输入车牌号" bind:submit="onSearch" bind:clear="onClearSearch" bind:change="onSearchInput" action-text="搜索" left-icon="search" />
  </view>

  <!-- 客户选择器 -->
  <view class="filter-row" wx:if="{{customerList.length > 1}}">
    <view class="customer-selector">
      <t-dropdown-menu style="background-color: transparent;position: initial;">
        <t-dropdown-item options="{{customer.options}}" value="{{customer.value}}" bindchange="onCustomerChange" />
      </t-dropdown-menu>
    </view>
  </view>

  <!-- 新增按钮 -->
  <view class="add-button-container">
    <t-button theme="primary" size="large" bind:tap="showAddDialog" block>
      新增常驻车
    </t-button>
  </view>

  <!-- 列表区域 -->
  <view class="list-container">
    <view wx:if="{{list.length === 0 && !loading}}" class="empty-container">
      <t-empty icon="car" description="暂无常驻车数据" />
    </view>

    <view wx:else>
      <view class="car-item" wx:for="{{list}}" wx:key="id">
        <view class="car-info">
          <view class="car-number">{{item.vehicleNo}}</view>
          <!-- <view class="car-details">
            <view class="detail-item" wx:if="{{item.customerName}}">
              <text class="label">客户名称：</text>
              <text class="value">{{item.customerName}}</text>
            </view>
            <view class="detail-item">
              <text class="label">司机姓名：</text>
              <text class="value">{{item.driverName}}</text>
            </view>
            <view class="detail-item">
              <text class="label">手机号：</text>
              <text class="value">{{item.tel}}</text>
            </view>
            <view class="detail-item" wx:if="{{item.remark}}">
              <text class="label">备注：</text>
              <text class="value">{{item.remark}}</text>
            </view>
            <view class="detail-item" wx:if="{{item.createTime}}">
              <text class="label">创建时间：</text>
              <text class="value">{{item.createTime}}</text>
            </view>
          </view> -->
        </view>
        <view class="car-actions">
          <t-button theme="danger" size="small" bind:tap="showDeleteDialog" data-item="{{item}}">
            删除
          </t-button>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{loading}}" class="loading-container">
      <t-loading theme="spinner" size="40rpx" text="加载中..." />
    </view>

    <view wx:if="{{!hasMore && list.length > 0}}" class="no-more">
      没有更多数据了
    </view>
  </view>

  <!-- 新增对话框 -->
  <t-dialog visible="{{showAddDialog}}" title="新增常驻车" content="非新能源车牌，请关闭软键盘后再点确定" confirm-btn="确定" cancel-btn="取消" bind:confirm="confirmAdd" bind:cancel="hideAddDialog" t-class="t-dialog-class" style="width: 710rpx;">
    <car-plate-input activeIndex="{{activeIndex}}" codeArray="{{codeArray}}" showProvincePicker="{{showProvincePicker}}" bottom="-350rpx" data-key="car-number-div" bind:inputcomplete="onInputComplete" slot="content" style="text-align: left;margin-top: 32rpx;border-radius: 8rpx;box-sizing: border-box;"></car-plate-input>
  </t-dialog>

  <!-- 删除确认对话框 -->
  <t-dialog visible="{{showDeleteDialog}}" title="确认删除" content="确定要删除车牌号为 {{deleteItem.vehicleNo}} 的常驻车吗？" confirm-btn="删除" cancel-btn="取消" bind:confirm="confirmDelete" bind:cancel="hideDeleteDialog" />

  <!-- 提示对话框 -->
  <t-dialog visible="{{showTextAndTitle}}" title="提示" content="{{dialogContent}}" confirm-btn="{{confirmBtn}}" bind:confirm="closeDialog" />

  <!-- Toast -->
  <t-toast id="t-toast" />
</view>