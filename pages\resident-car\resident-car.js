// pages/resident-car/resident-car.js
import Toast from 'tdesign-miniprogram/toast/index';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    list: [], // 常驻车列表
    searchValue: '', // 搜索的车牌号
    loading: false,
    userInfo: {},
    showAddDialog: false, // 显示新增对话框
    showDeleteDialog: false, // 显示删除确认对话框
    deleteItem: null, // 要删除的项目
    // 新增表单数据
    addForm: {
      vehicleNo: '', // 车牌号
    },
    showTextAndTitle: false,
    dialogContent: '',
    confirmBtn: { content: '确定', variant: 'base' },
    // 车牌号选择器相关
    activeIndex: 0,
    codeArray: ['', '', '', '', '', '', '', '新能源'],
    showProvincePicker: true,
    dialogCarNum: '',
    // 客户选择相关
    customer: {
      value: '',
      options: []
    },
    customerList: [], // 客户列表
    customerIds: [], // 客户ID数组
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const userInfo = wx.getStorageSync('userInfo');
    this.setData({
      userInfo
    });

    // 初始化客户选择选项
    this.initCustomerOptions();
    this.loadResidentCarList(true);
  },

  // 初始化客户选择选项
  initCustomerOptions() {
    let userInfoList = wx.getStorageSync('userInfoList');
    const user = wx.getStorageSync('userInfo');
    userInfoList = userInfoList.filter(u => u.reservationIdentity == user.reservationIdentity);

    this.setData({
      customerList: userInfoList,
    });

    const list = userInfoList.map(m => {
      return {
        label: `${m.customerId}-${m.customerName}`,
        value: m.customerId,
      };
    });

    // 获取所有客户ID
    const customerIds = userInfoList.map(u => u.customerId);
    this.setData({
      customerIds
    });

    if (userInfoList.length == 1) {
      // 只有一个客户，自动选中
      const { customerId } = userInfoList[0];
      this.setData({
        'customer.options': list,
        'customer.value': customerId,
      });
      return;
    }

    // 多个客户，添加全部客户选项作为默认
    list.unshift({
      value: 'all',
      label: `全部客户`,
    });
    this.setData({
      'customer': {
        value: 'all',
        options: list,
      },
    });
  },

  // 客户下拉框选择
  onCustomerChange(e) {
    this.setData({
      'customer.value': e.detail.value,
    });
    this.loadResidentCarList(true);
  },

  /**
   * 加载常驻车列表
   */
  loadResidentCarList(reset = false) {
    if (this.data.loading) return;

    this.setData({
      loading: true
    });

    if (reset) {
      this.setData({
        list: []
      });
    }

    const app = getApp();
    const { segNo } = this.data.userInfo;

    // 根据选择的客户获取客户ID数组
    const customerId = this.data.customer.value;
    let customerIds;

    if (this.data.customerList.length == 1) {
      // 单客户情况
      customerIds = [this.data.customerList[0].customerId];
    } else {
      // 多客户情况
      if (customerId === 'all') {
        // 选择全部客户，获取所有客户ID
        customerIds = this.data.customerIds;
      } else {
        // 选择特定客户
        customerIds = [customerId];
      }
    }

    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        serviceId: 'S_LI_RL_0511', // 查询接口
        segNo,
        customerId: customerIds, // 添加客户ID查询支持
        vehicleNo: this.data.searchValue || '', // 支持根据车牌号查询
      },
      success: (res) => {
        this.setData({
          loading: false
        });

        if (res?.data?.__sys__?.status === -1) {
          this.setData({
            dialogContent: res.data.__sys__.msg || '查询失败',
            showTextAndTitle: true
          });
          return;
        }

        const result = res.data?.list || [];

        this.setData({
          list: result
        });
      },
      fail: () => {
        this.setData({
          loading: false,
          dialogContent: '网络异常，请稍后重试',
          showTextAndTitle: true
        });
      }
    });
  },

  /**
   * 搜索框输入
   */
  onSearchInput(e) {
    this.setData({
      searchValue: e.detail.value
    });
  },

  /**
   * 搜索
   */
  onSearch() {
    this.loadResidentCarList(true);
  },

  /**
   * 清空搜索
   */
  onClearSearch() {
    this.setData({
      searchValue: ''
    });
    this.loadResidentCarList(true);
  },

  /**
   * 显示新增对话框
   */
  showAddDialog() {
    this.setData({
      showAddDialog: true,
      codeArray: ['', '', '', '', '', '', '', '新能源'],
      activeIndex: 0,
      showProvincePicker: true,
      dialogCarNum: '',
      addForm: {
        vehicleNo: ''
      }
    });
  },

  /**
   * 隐藏新增对话框
   */
  hideAddDialog() {
    this.setData({
      showAddDialog: false
    });
  },

  /**
   * 新增表单输入
   */
  onAddFormInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    this.setData({
      [`addForm.${field}`]: value
    });
  },

  /**
   * 车牌号输入完成
   */
  onCarPlateComplete(event) {
    const { plate } = event.detail;
    this.setData({
      dialogCarNum: plate
    });
  },

  /** 子页面方法 */
  onInputComplete(event) {
    const { plate } = event.detail; // 获取子组件传递的车牌号
    console.log('车牌号:', plate); // 打印或处理车牌号

    // 更新父页面的 carNumber 数据
    this.setData({
      dialogCarNum: plate
    });
  },

  /**
   * 确认新增
   */
  confirmAdd() {
    const app = getApp();
    const isValidCarNumber = app.isCarNumb && app.isCarNumb(this.data.dialogCarNum);

    // 验证必填字段
    if (!this.data.dialogCarNum) {
      Toast({
        context: this,
        selector: '#t-toast',
        message: '请输入车牌号',
        theme: 'warning',
      });
      return;
    }

    if (app.isCarNumb && !isValidCarNumber) {
      Toast({
        context: this,
        selector: '#t-toast',
        message: '车牌号格式错误，请检查',
        theme: 'warning',
      });
      return;
    }

    wx.showLoading({
      title: '添加中...'
    });

    const { segNo } = this.data.userInfo;

    // 获取当前选择的客户ID，如果是单客户直接使用，如果是多客户且选择了具体客户则使用选择的
    let targetCustomerId = '';
    if (this.data.customerList.length == 1) {
      targetCustomerId = this.data.customerList[0].customerId;
    } else if (this.data.customer.value !== 'all') {
      targetCustomerId = this.data.customer.value;
    } else {
      // 如果选择的是全部客户，需要用户指定一个具体的客户
      Toast({
        context: this,
        selector: '#t-toast',
        message: '请先选择具体客户再新增',
        theme: 'warning',
      });
      return;
    }

    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        serviceId: 'S_LI_RL_0512', // 新增接口
        segNo,
        customerId: targetCustomerId,
        tel: this.data.userInfo.tel,
        administrator: this.data.userInfo.administrator || '',
        vehicleNo: this.data.dialogCarNum,
        remark: '',
      },
      success: (res) => {
        wx.hideLoading();

        if (res?.data?.__sys__?.status === -1) {
          this.setData({
            dialogContent: res.data.__sys__.msg || '添加失败',
            showTextAndTitle: true
          });
          return;
        }

        Toast({
          context: this,
          selector: '#t-toast',
          message: '添加成功',
          theme: 'success',
        });

        this.hideAddDialog();
        this.loadResidentCarList(true); // 刷新列表
      },
      fail: () => {
        wx.hideLoading();
        this.setData({
          dialogContent: '网络异常，请稍后重试',
          showTextAndTitle: true
        });
      }
    });
  },

  /**
   * 显示删除确认对话框
   */
  showDeleteDialog(e) {
    const { item } = e.currentTarget.dataset;
    this.setData({
      showDeleteDialog: true,
      deleteItem: item
    });
  },

  /**
   * 隐藏删除确认对话框
   */
  hideDeleteDialog() {
    this.setData({
      showDeleteDialog: false,
      deleteItem: null
    });
  },

  /**
   * 确认删除
   */
  confirmDelete() {
    if (!this.data.deleteItem) return;

    wx.showLoading({
      title: '删除中...'
    });

    const app = getApp();
    const { segNo } = this.data.userInfo;
    const { id, vehicleNo, customerId } = this.data.deleteItem;

    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        serviceId: 'S_LI_RL_0513', // 删除接口
        segNo,
        vehicleNo,
        customerId,
        tel: this.data.userInfo.tel,
        administrator: this.data.userInfo.administrator || '',
      },
      success: (res) => {
        wx.hideLoading();

        if (res?.data?.__sys__?.status === -1) {
          this.setData({
            dialogContent: res.data.__sys__.msg || '删除失败',
            showTextAndTitle: true
          });
          return;
        }

        Toast({
          context: this,
          selector: '#t-toast',
          message: '删除成功',
          theme: 'success',
        });

        this.hideDeleteDialog();
        this.loadResidentCarList(true); // 刷新列表
      },
      fail: () => {
        wx.hideLoading();
        this.setData({
          dialogContent: '网络异常，请稍后重试',
          showTextAndTitle: true
        });
      }
    });
  },

  /**
   * 关闭提示对话框
   */
  closeDialog() {
    this.setData({
      showTextAndTitle: false
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadResidentCarList(true);
    wx.stopPullDownRefresh();
  },

  /**
   * 触底加载更多 - 已禁用分页功能
   */
  onReachBottom() {
    // 分页功能已移除，此方法不再执行任何操作
  }
}) 