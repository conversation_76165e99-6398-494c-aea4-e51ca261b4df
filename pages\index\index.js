// pages/index/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    currentTab: 0, // 默认显示首页
    list: [
      { value: '0', icon: 'home', ariaLabel: '首页' },
      { value: '1', icon: 'user', ariaLabel: '我的' },
    ],
    screenHeight: 0,
    navigatorProps: {
      url: '/pages/driver/driver-list/driver-list',
    },
    isVisible: true,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let that = this;
    let clientHeight = wx.getWindowInfo().windowHeight;
    let query = wx.createSelectorQuery().in(this);
    query.select('#tabbar-box').boundingClientRect(rects => {
      that.setData({
        screenHeight: clientHeight - rects.height
      });
    }).exec();

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    const myComponent = this.selectComponent('#myComponent');
    if (myComponent) {
      myComponent.refreshData();  // 调用组件的方法
    }
    wx.hideHomeButton();
    const auditList = wx.getStorageSync('auditList');
    const user = wx.getStorageSync('userInfo');
    // 30是司机, 40是内部员工, 50: 游客
    if (['30', '40', '50'].includes(user.reservationIdentity)) {
      this.setData({
        isVisible: false,
      });
      return;
    }

    this.setData({
      isVisible: auditList && auditList.length > 0,
    });
  },

  /**
   * 点击标签栏事件
   * @param {*} e 
   */
  onChange(e) {
    this.setData({
      currentTab: e.detail.value
    });
    // 动态设置页面标题
    if (this.data.currentTab == 0) {
      wx.setNavigationBarTitle({
        title: '首页' // 首页标题
      });
      return;
    }
    if (this.data.currentTab == 1) {
      wx.setNavigationBarTitle({
        title: '我的' // 我的页面标题
      });
    }
  },

  clearNoticBar() {
    this.setData({
      isVisible: false,
    })
  },
})