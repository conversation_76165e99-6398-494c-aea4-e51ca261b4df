// pages/order-allocation/spot-trading-order-detail/spot-trading-order-detail.js
import Toast from 'tdesign-miniprogram/toast/index';
const $api = require('../../../api/request')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    mainObj: {},
    dataList: [],
    mainObjList: [],
    isEdit: false,
    editObj: '',
    selectedList: [], // 选中的货物列表
    isWholeOrder: false, // 是否整单配单
    allSelected: false, // 是否全选
    userInfo: {},
    // 车辆信息
    vehicleInfo: {
      plateNumber: '', // 车牌号
      driverName: '', // 司机姓名
      driverPhone: '', // 司机电话
      carTraceNo: '', // 车辆跟踪号
      idCard: '', // 身份证
    },
    // 车牌选择相关
    carTitle: '请选择车牌号',
    carFlag: false,
    carNumber: "",
    carList: [],
    carNumberList: [],
    searchKeyword: '',
    searchPlaceholder: '',
    filteredCarList: [],
    selectedCarItem: null,
    selectCarInfo: [],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      userInfo: wx.getStorageSync('userInfo')
    });

    const eventChannel = this.getOpenerEventChannel()
    // 监听acceptDataFromOpenerPage事件，获取上一页面通过eventChannel传送到当前页面的数据
    eventChannel.on('acceptDataFromSpotTradingOrder', (data) => {
      console.log(data);


      if (data.checkList && data.checkList.length > 0) {
        // 多选配单模式
        let packList = [];
        data.checkList.forEach(c => {
          if (c.packList && c.packList.length > 0) {
            // 为每个捆包添加主项信息，用于后续重新分组
            c.packList.forEach(pack => {
              pack.parentBillId = c.billId || c.ladingBillId; // 关联主项ID
              // 只保存主项的关键信息，避免循环引用
              pack.parentMainInfo = {
                billId: c.billId,
                ladingBillId: c.ladingBillId,
                customerName: c.customerName,
                ladingSpotName: c.ladingSpotName,
                destSpotName: c.destSpotName,
                totalWeightNum: c.totalWeightNum,
                sumNetWeight: c.sumNetWeight,
                ladingBillRemark: c.ladingBillRemark,
                recCreateTime: c.recCreateTime,
                billingMethod: c.billingMethod
              };
            });
            packList = [
              ...packList,
              ...c.packList,
            ];
          }
        });

        if (packList.length > 0) {
          packList.forEach((item, i) => {
            item.flag = false
          })
        }

        this.setData({
          mainObjList: data.checkList,
          dataList: packList,
          isWholeOrder: data.isWholeOrder || false
        });

        // 更新全选状态
        this.updateAllSelectedStatus();
        return;
      }

      if (data.data) {
        // 单项详情模式
        let packList = [];
        if (data.data.packList && data.data.packList.length > 0) {
          packList = data.data.packList;
          packList.forEach((item, i) => {
            item.flag = false;
            // 为单项模式的捆包也添加主项关联信息
            item.parentBillId = data.data.billId || data.data.ladingBillId;
            // 只保存主项的关键信息，避免循环引用
            item.parentMainInfo = {
              billId: data.data.billId,
              ladingBillId: data.data.ladingBillId,
              customerName: data.data.customerName,
              ladingSpotName: data.data.ladingSpotName,
              destSpotName: data.data.destSpotName,
              totalWeightNum: data.data.totalWeightNum,
              sumNetWeight: data.data.sumNetWeight,
              ladingBillRemark: data.data.ladingBillRemark,
              recCreateTime: data.data.recCreateTime,
              billingMethod: data.data.billingMethod
            };
          })
        }

        this.setData({
          mainObj: data.data,
          dataList: packList
        });

        // 更新全选状态
        this.updateAllSelectedStatus();
      }
    })
  },

  // 查询车辆信息 - 使用与"我的车辆"页面一致的接口
  getCarInfo() {
    const { userInfo } = this.data;
    const { segNo, driverName, tel } = userInfo;

    wx.showLoading({
      title: '加载车辆信息',
    });

    const app = getApp();
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        serviceId: 'S_LI_RL_0199',
        driverTel: tel,
        segNo,
        driverName
      },
      success: (res) => {
        wx.hideLoading();
        let arr = [];
        if (res?.data?.result?.length > 0) {
          const result = res.data.result;
          arr = result.map(vehicle => {
            return {
              label: vehicle.vehicleNo,
              value: vehicle.vehicleNo,
              driverName: vehicle.driverName,
              tel: vehicle.driverTel,
              carTraceNo: vehicle.carTraceNo, // 添加车辆跟踪号
              idCard: vehicle.idCard, // 添加身份证
              displayText: `${vehicle.vehicleNo} (${vehicle.driverName})`
            };
          });
        }
        this.setData({
          carNumberList: arr,
          carList: res?.data?.result || [],
          filteredCarList: arr,
        });
      },
      fail: () => {
        wx.hideLoading();
        Toast({
          context: this,
          selector: '#t-toast',
          message: '获取车辆信息失败',
        });
      }
    });
  },

  // 选车牌
  selectCar() {
    // 如果还没有加载车辆信息，先加载
    if (this.data.carNumberList.length === 0) {
      this.getCarInfo();
    }

    // 如果当前已经有选中的车牌号，找到对应的车牌项作为默认选中
    let defaultSelectedItem = null;
    if (this.data.carNumber && this.data.vehicleInfo.carTraceNo) {
      defaultSelectedItem = this.data.carNumberList.find(item =>
        item.value === this.data.carNumber && item.carTraceNo === this.data.vehicleInfo.carTraceNo
      );
    }

    this.setData({
      carFlag: true,
      searchKeyword: '',
      searchPlaceholder: '输入车牌号进行筛选',
      filteredCarList: this.data.carNumberList,
      selectedCarItem: defaultSelectedItem || null
    })
  },

  // 搜索车牌号
  onSearchCar(e) {
    const keyword = e.detail.value; // 保持原始大小写
    const filteredList = this.data.carNumberList.filter(item => {
      // 不区分大小写匹配车牌号
      return item.label.toLowerCase().includes(keyword.toLowerCase());
    });

    this.setData({
      searchKeyword: keyword, // 保持用户输入的原始大小写
      filteredCarList: filteredList
    });
  },

  // 选择车牌项
  selectCarItem(e) {
    const carItem = e.currentTarget.dataset.car;
    // 如果点击的是已选中的项，则取消选中
    const isCurrentlySelected = this.data.selectedCarItem &&
      this.data.selectedCarItem.value === carItem.value;

    this.setData({
      selectedCarItem: isCurrentlySelected ? null : carItem
    });
  },

  // 确认选择车牌
  confirmSelectCar() {
    if (!this.data.selectedCarItem) {
      Toast({
        context: this,
        selector: '#t-toast',
        message: '请选择车牌号',
      });
      return;
    }

    this.setData({
      carNumber: this.data.selectedCarItem.value,
      carTitle: this.data.selectedCarItem.displayText,
      'vehicleInfo.plateNumber': this.data.selectedCarItem.value,
      'vehicleInfo.driverName': this.data.selectedCarItem.driverName,
      'vehicleInfo.driverPhone': this.data.selectedCarItem.tel,
      'vehicleInfo.carTraceNo': this.data.selectedCarItem.carTraceNo, // 保存车辆跟踪号
      'vehicleInfo.idCard': this.data.selectedCarItem.idCard, // 保存身份证
      carFlag: false,
      searchPlaceholder: ''
    });
  },

  // 取消选择车牌
  cancelSelectCar() {
    this.setData({
      carFlag: false,
      searchKeyword: '',
      searchPlaceholder: '',
      selectedCarItem: null
    });
  },

  /**
   * 选择/取消选择货物
   */
  cheackList(e) {
    let index = e.currentTarget.dataset.index;
    let dataList = this.data.dataList;
    let selectedList = this.data.selectedList;

    dataList[index].flag = !dataList[index].flag;

    if (dataList[index].flag) {
      // 添加到选中列表
      if (selectedList.findIndex(item => item.packId === dataList[index].packId) === -1) {
        selectedList.push(dataList[index]);
      }
    } else {
      // 从选中列表移除
      selectedList = selectedList.filter(item => item.packId !== dataList[index].packId);
    }

    this.setData({
      dataList,
      selectedList
    });

    // 更新全选状态
    this.updateAllSelectedStatus();
  },

  /**
   * 重新组织数据：将选中的捆包按主项分组
   */
  reorganizeSelectedData(submitList) {
    const { mainObjList } = this.data;
    
    // 清理捆包数据的函数
    const cleanPackData = (pack) => {
      const cleanPack = { ...pack };
      delete cleanPack.parentMainInfo;
      delete cleanPack.parentMainObj;
      delete cleanPack.flag;
      delete cleanPack.parentBillId;
      return cleanPack;
    };

    if (!mainObjList || mainObjList.length === 0) {
      // 单项模式，返回清理后的数据
      const mainObj = this.data.mainObj;
      return [{
        billId: mainObj.billId,
        ladingBillId: mainObj.ladingBillId,
        customerName: mainObj.customerName,
        ladingSpotName: mainObj.ladingSpotName,
        destSpotName: mainObj.destSpotName,
        totalWeightNum: mainObj.totalWeightNum,
        sumNetWeight: mainObj.sumNetWeight,
        ladingBillRemark: mainObj.ladingBillRemark,
        recCreateTime: mainObj.recCreateTime,
        billingMethod: mainObj.billingMethod,
        packList: submitList.map(cleanPackData)
      }];
    }

    // 多项模式，按主项分组
    const groupedData = {};
    
    // 按 parentBillId 分组选中的捆包
    submitList.forEach(pack => {
      const parentId = pack.parentBillId || pack.ladingBillId;
      if (!groupedData[parentId]) {
        // 从 mainObjList 中找到对应的主项
        const mainObj = mainObjList.find(main => 
          (main.billId || main.ladingBillId) === parentId
        );
        
        if (mainObj) {
          groupedData[parentId] = {
            billId: mainObj.billId,
            ladingBillId: mainObj.ladingBillId,
            customerName: mainObj.customerName,
            ladingSpotName: mainObj.ladingSpotName,
            destSpotName: mainObj.destSpotName,
            totalWeightNum: mainObj.totalWeightNum,
            sumNetWeight: mainObj.sumNetWeight,
            ladingBillRemark: mainObj.ladingBillRemark,
            recCreateTime: mainObj.recCreateTime,
            billingMethod: mainObj.billingMethod,
            packList: []
          };
        } else if (pack.parentMainInfo) {
          groupedData[parentId] = {
            ...pack.parentMainInfo,
            packList: []
          };
        }
      }
      
      if (groupedData[parentId]) {
        groupedData[parentId].packList.push(cleanPackData(pack));
      }
    });

    // 转换为提交格式
    return Object.values(groupedData);
  },

  /**
   * 提交现货交易配单
   */
  submitOrder() {
    const { vehicleInfo, selectedList, dataList, userInfo, isWholeOrder } = this.data;

    // 验证必填项
    if (!vehicleInfo.plateNumber) {
      Toast({
        context: this,
        selector: '#t-toast',
        message: '请输入车牌号',
      });
      return;
    }

    if (!vehicleInfo.driverName) {
      Toast({
        context: this,
        selector: '#t-toast',
        message: '请输入司机姓名',
      });
      return;
    }

    // 确定要提交的货物列表
    let submitList = [];
    if (isWholeOrder) {
      // 整单配单，提交所有货物
      submitList = dataList;
    } else {
      // 部分配单，只提交选中的货物
      submitList = selectedList;
      if (submitList.length === 0) {
        Toast({
          context: this,
          selector: '#t-toast',
          message: '请选择要配单的货物',
        });
        return;
      }
    }

    // 重新组织数据：将选中的捆包按主项分组
    const organizedList = this.reorganizeSelectedData(submitList);

    // 构造提交数据
    const submitData = {
      "result2": [
        {
          vehicleNo: vehicleInfo.plateNumber,
          driverName: vehicleInfo.driverName, // 使用选择的司机姓名
          driverTel: vehicleInfo.driverPhone, // 使用选择的司机电话
          "time": this.data.arrivedatetime,
          "customerId": userInfo.customerId,
          "customerName": userInfo.customerName,
          tel: userInfo.tel,
          "administrator": userInfo.administrator,
          carTraceNo: vehicleInfo.carTraceNo, // 车辆跟踪号
          idCard: vehicleInfo.idCard, // 身份证
        }
      ],
      segNo: userInfo.segNo,
      tel: userInfo.tel,
      reservationIdentity: userInfo.reservationIdentity,
      // 车辆信息
      plateNumber: vehicleInfo.plateNumber,
      vehicleDriverName: vehicleInfo.driverName,
      vehicleDriverPhone: vehicleInfo.driverPhone,
      carTraceNo: vehicleInfo.carTraceNo, // 添加车辆跟踪号
      "list": organizedList, // 使用重新组织后的数据
    };

    console.log('提交数据结构:', {
      segNo: submitData.segNo,
      tel: submitData.tel,
      plateNumber: submitData.plateNumber,
      listCount: submitData.list ? submitData.list.length : 0,
      listSample: submitData.list && submitData.list[0] ? {
        billId: submitData.list[0].billId,
        packListCount: submitData.list[0].packList ? submitData.list[0].packList.length : 0
      } : null
    });

    wx.showLoading({
      title: '提交中...',
    });

    // 调用新增接口
    $api.request('S_LI_RL_0196','',submitData).then((res) => {
      wx.hideLoading();
      Toast({
        context: this,
        selector: '#t-toast',
        message: '现货交易配单提交成功',
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);

    }).catch((err) => {
      wx.hideLoading();
      console.log(err);
    });
  },

  /**
   * 更新全选状态
   */
  updateAllSelectedStatus() {
    const { dataList } = this.data;
    const allSelected = dataList.length > 0 && dataList.every(item => item.flag);
    this.setData({
      allSelected
    });
  },

  /**
   * 全选/全不选
   */
  toggleSelectAll() {
    let dataList = this.data.dataList;
    let selectedList = this.data.selectedList;

    // 判断当前是否全选
    const isAllSelected = dataList.every(item => item.flag);

    if (isAllSelected) {
      // 全不选
      dataList.forEach(item => {
        item.flag = false;
      });
      selectedList = [];
    } else {
      // 全选
      dataList.forEach(item => {
        item.flag = true;
      });
      selectedList = [...dataList];
    }

    this.setData({
      dataList,
      selectedList
    });

    // 更新全选状态
    this.updateAllSelectedStatus();
  }
}) 