import { baseUrl } from './config.js'
module.exports = {
  /*
   * url:请求的接口地址
   * methodType:请求方式
   * data: 要传递的参数
   */
  request: function (url, methodType, data) {
    let method = methodType ? methodType : 'POST';
    data = {
      ...data,
      serviceId: url,
    }
    wx.showLoading({
      title: "加载中"
    });
    return new Promise((resolve, reject) => {
      wx.request({
        url: baseUrl,
        method: method,
        data,
        header: {
          'content-type': 'application/json', // 默认值
          // 'x-api-key': token,
        },
        success: (res) => {
          wx.hideLoading();
          if (res.statusCode == 200 && res.data != null && res.data.__sys__) {
            if (res.data.__sys__.status == -1) {
              if (!data.isNotShowToast) {
                wx.showToast({
                  title: res.data.__sys__.msg,
                  icon: 'none'
                });
              }

              reject(res)
            } else {
              resolve(res.data)
            }
          } else {
            wx.showToast({
              title: res.data.msg,
              icon: 'none'
            })
            reject(res)
          }
        },
        fail: () => {
          wx.hideLoading();
          wx.showToast({
            title: '接口请求错误',
            icon: 'none'
          })
          reject('接口请求错误')
        },

      })
    })
  }
}