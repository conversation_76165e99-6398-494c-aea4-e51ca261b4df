// pages/my-reservation/my-reservation.js
import Message from 'tdesign-miniprogram/message/index';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    reservationList: [],
    showTextAndTitle: false,
    content: '',
    confirmBtn: { content: '确定', variant: 'base' },
    value: '',
    customer: {
      value: 'all',
      options: [{ value: 'all', label: '请选择所属公司' }],
    },
    // 分页相关参数
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    isLoading: false,
    customerIds: [], // 存储所有客户ID
    scrollTop: 0,
    isTourist: false,
    showLoadPoint: false,
    loadPointBtn: { content: '确定', variant: 'base' },
    loadPointList: [],
    selectedHandPointId: '',
    reservation: '',
    userInfo: '',
    demoCheckboxMax: [],
    isShowEdit: true,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      userInfo: wx.getStorageSync('userInfo'),
    })
    // 有值就是游客登录
    const isKey = options.isKey;
    this.setData({
      // 游客不能修改预约
      isShowEdit: this.data.userInfo.reservationIdentity != '50',
    });

    if (isKey == '1') {
      this.setData({
        isTourist: true,
        isShowEdit: false,
      });
      this.getReversionInfoList(true);
      return;
    }

    this.setData({
      isTourist: false,
    });

    this.fetchCustomerOptions();
  },

  /**
   * 取消预约
   * @param {*} e 
   */
  deleteItem(e) {
    const user = wx.getStorageSync('userInfo');
    const index = e.currentTarget.dataset.index;
    const reversion = this.data.reservationList[index];
    const app = getApp();
    const { segNo, reservationNumber, reservationTime, reservationDate } = reversion;
    wx.showLoading({
      title: '加载中',
    });
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        serviceId: 'S_LI_RL_0030',
        segNo,
        reservationNumber,
        reservationTime,
        reservationDate,
        driverTel: user.tel,
        reservationIdentity: user.reservationIdentity,
        recRevisor: user.tel,
        recRevisorName: user.administrator ?? user.driverName,
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          Message.error({
            context: this,
            offset: [90, 32],
            duration: 3000,
            content: '网络异常, 请稍后重试',
          });
          return;
        }

        const result = res.data;
        if (result.__sys__?.status != 1) {
          Message.error({
            context: this,
            offset: [90, 32],
            duration: 3000,
            content: result.__sys__.msg,
          });
          return;
        }

        Message.success({
          context: this,
          offset: [90, 32],
          duration: 3000,
          content: '删除成功',
        });

        this.getReversionInfoList(true);
      },
      fail: () => {
        wx.hideLoading();
        this.setData({
          showTextAndTitle: true,
          content: '网络异常, 请稍后重试',
        });
      }
    });

  },

  /**
   * 修改预约
   * 逻辑就是: 将数据带入到新增预约页面
   * @param {*} e 
   */
  editItem(e) {
    const index = e.currentTarget.dataset.index;
    const data = this.data.reservationList[index];
    // 重新跳转到游客预约
    if (this.data.isTourist) {
      wx.navigateTo({
        url: '/pages/tourist-reservation/tourist-reservation',
        events: {
          // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
          acceptDataFromOpenedPage: () => { },
        },
        success: (res) => {
          // 通过eventChannel向被打开页面传送数据
          res.eventChannel.emit('acceptDataFromOpenerPage', { data, })
        }
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/insert-reservation/insert-reservation',
      events: {
        // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
        acceptDataFromOpenedPage: () => { },
      },
      success: (res) => {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('acceptDataFromOpenerPage', { data, })
      }
    });
  },

  /**
   * 打开增加装卸点弹出框
   * @param {*} e 
   */
  openLoadPointDialog(e) {
    const index = e.currentTarget.dataset.index;
    const data = this.data.reservationList[index];
    const app = getApp();
    wx.showLoading({
      title: '查询中',
    })
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        serviceId: 'S_LI_RL_0158',
        segNo: data.segNo,
      },
      success: (res) => {
        wx.hideLoading();
        const result = res.data;
        if (result.__sys__?.status != 1) {
          Message.error({
            context: this,
            offset: [90, 32],
            duration: 3000,
            content: result.__sys__.msg,
          });
          return;
        }

        this.setData({
          loadPointList: result.list,
          showLoadPoint: true,
          loadPointName: '',
          loadPointId: '',
          reservation: data,
        });
      },
      fail: () => {
        wx.hideLoading();
        this.setData({
          showTextAndTitle: true,
          content: '网络异常, 请稍后重试',
        });
      }
    });

  },

  radioChange(event) {
    console.log('checkbox', event.detail.value);
    this.setData({
      demoCheckboxMax: event.detail.value
    })
  },

  /**
   * 新增装卸点
   */
  addLoadPoint() {
    const user = wx.getStorageSync('userInfo');
    const { reservation, selectedHandPointId, loadPointList, demoCheckboxMax } = this.data;
    if (demoCheckboxMax.length == 0) {
      Message.error({
        context: this,
        offset: [90, 32],
        duration: 3000,
        content: '请选择装卸点',
      });
      return;
    }

    const checkLoadList = loadPointList.filter(l => demoCheckboxMax.includes(l.handPointId));

    const app = getApp();
    wx.showLoading({
      title: '新增中',
    });
    // const handPoint = loadPointList.find(l => l.handPointId == selectedHandPointId);
    const { segNo, reservationNumber, vehicleNo } = reservation;
    const data = {
    handPoint: checkLoadList,
      segNo,
      reservationNumber,
      driverName: user.administrator ?? user.driverName,
      driverTel: user.tel,
      vehicleNo,
      serviceId: 'S_LI_RL_0159',
    };
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data,
      success: (res) => {
        wx.hideLoading();
        const result = res.data;
        if (result.__sys__?.status == -1) {
          Message.error({
            context: this,
            offset: [90, 32],
            duration: 3000,
            content: result.__sys__.msg,
          });
          return;
        }

        Message.success({
          context: this,
          offset: [90, 32],
          duration: 3000,
          content: '新增成功',
        });

        this.setData({
          loadPointList: [],
          showLoadPoint: false,
          reservation: '',
          selectedHandPointId: '',
        });
        this.getReversionInfoList(true);
      },
      fail: () => {
        wx.hideLoading();
        this.setData({
          showTextAndTitle: true,
          content: '网络异常, 请稍后重试',
        });
      }
    });
  },

  /**
 * 点击搜索按钮或者回车
 */
  actionHandle() {
    this.getReversionInfoList(true);
  },

  /**
 * 客户下拉框选择
 * @param {*} e 
 */
  onChange(e) {
    this.setData({
      'customer.value': e.detail.value,
    });
    this.getReversionInfoList(true);
  },

  toInfo(e) {
    console.log(e);
    const data = this.data.reservationList[e.currentTarget.dataset.index];
    console.log(data, e.currentTarget.dataset.index);
    wx.navigateTo({
      url: '/pages/my-reservation/my-reservation-info/my-reservation-info',
      events: {
        // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
        acceptDataFromOpenedPage: () => { },
      },
      success: (res) => {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('acceptDataFromOpenerPage', { data, })
      }
    });
  },

  closeDialog() {
    this.setData({
      showTextAndTitle: false,
    });
  },

  closeLoadPoint() {
    this.setData({
      showLoadPoint: false,
      loadPointList: [],
      selectedHandPointId: '',
    });
  },

  /**
   * 选择装卸点确认事件
   */
  confirmDialog() {

  },

  /**
 * 伪双向绑定搜索框
 * @param {*} e 
 */
  changeHandle(e) {
    const { value } = e.detail;
    this.setData({
      value,
    });
  },

  // 获取预约列表
  getReversionInfoList(resetPage = false) {
    if (this.data.isLoading) return;
    const newState = {};
    if (resetPage) {
      newState.currentPage = 1;
      newState.reservationList = [];
      newState.hasMore = true;
      this.setData({
        scrollTop: 0,
      })
    }

    this.setData({ ...newState, isLoading: true });
    wx.showLoading({ title: '加载中' });

    const user = wx.getStorageSync('userInfo');
    const app = getApp();
    const params = {
      serviceId: 'S_LI_RL_0029',
      segNo: user.segNo,
      customerId: this.data.customer.value == 'all'
        ? this.data.customerIds
        : [this.data.customer.value],
      driverTel: user.tel,
      identityType: user.identityType,
      pageNum: this.data.currentPage,
      pageSize: this.data.pageSize,
    };

    // 搜索条件处理
    const value = this.data.value;
    if (value) {
      params[isNaN(value) ? 'driverName' : 'driverTelNum'] = value;
    }

    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: params,
      success: (res) => {
        wx.hideLoading();
        const result = res.data;
        if (result.__sys__?.status === -1) {
          Message.error({ context: this, content: result.__sys__.msg });
          this.setData({ isLoading: false });
          return;
        }

        const currentList = result.list || [];
        this.setData({
          reservationList: resetPage ?
            this.processListData(currentList) :
            [...this.data.reservationList, ...this.processListData(currentList)],
          hasMore: currentList.length > 9,
          isLoading: false
        });
      },
      fail: () => {
        wx.hideLoading();
        this.setData({ isLoading: false });
        Message.error({ context: this, content: '请求失败' });
      }
    });
  },

  // 获取客户选项
  fetchCustomerOptions() {
    const user = wx.getStorageSync('userInfo');
    const app = getApp();
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        serviceId: 'S_LI_RL_0026',
        tel: user.tel,
        segNo: user.segNo,
        identityType: user.identityType,
        reservationIdentity: user.reservationIdentity,
      },
      success: (res) => {
        if (res?.data?.result) {
          const resultList = res.data.result.filter((item, index, self) =>
            index === self.findIndex(t => t.customerId === item.customerId && t.customerName === item.customerName)
          );
          const customerIds = resultList.map(r => r.customerId);
          const cOptions = resultList.map(r => ({
            value: r.customerId,
            label: r.customerName
          }));
          cOptions.unshift({ value: 'all', label: '请选择所属公司' });
          this.setData({
            'customer.options': cOptions,
            customerIds,
          });
          this.getReversionInfoList(true);
        }
      }
    });
  },

  loadMore() {
    if (this.data.hasMore && !this.data.isLoading) {
      this.setData(
        { currentPage: this.data.currentPage + 1 },
        () => this.getReversionInfoList()
      );
    }
  },

  // 处理列表数据
  processListData(list) {
    return list.map(item => ({
      ...item,
      statusClass: item.status === '20' ? 'primary' : 'success',
      statusName: item.status === '20' ? '生效' : '完成',
      dateStr: item.reservationDate.replace(/(\d{4})(\d{2})(\d{2})/, '$1/$2/$3'),
      dateJCStr: item.segNo == 'JC000000' ? item.reservationDate.replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1/$2/$3 $4:$5:$6') : '',
      typeName: this.getTypeName(item.typeOfHandling)
    }));
  },

  // 获取类型名称
  getTypeName(type) {
    const app = getApp();
    const typeItem = app.globalData.typeList.find(item => item.value === type);
    return typeItem ? typeItem.label : '其他物品运输';
  },

  // 滚动触底事件
  onReachBottom: function () {
    this.debounce(function () {
      if (this.data.hasMore && !this.data.isLoading) {
        this.setData(
          { currentPage: this.data.currentPage + 1 },
          () => this.getReversionInfoList()
        );
      }
    }.bind(this))();
  },

  // 新增防抖函数
  debounce(func, delay = 500) {
    let timer = null;
    return function (...args) {
      if (timer) clearTimeout(timer);
      timer = setTimeout(() => {
        func.apply(this, args);
      }, delay);
    };
  },

})