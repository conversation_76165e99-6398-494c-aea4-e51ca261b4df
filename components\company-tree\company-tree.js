// components/copy-tree.js
Component({
    /**
     * 组件的属性列表
     */
    properties: {
        objData:{ 
            type: Object,  
            value: {}   
        },
        step:{
            type: String,  
            value: ''     
        },
        styleFlag:{
            type: String,  
            value: ''     
        },
    },

    /**
     * 组件的初始数据
     */
    data: {
    },

    /**
     * 组件的方法列表
     */
    methods: {
        foldList(e){
            let id=e.currentTarget.dataset.fold
            this.triggerEvent('foldList', {id}, {})  //通知父组建事件
        },
        selectOne(e){
            console.log(e.currentTarget.dataset.item)
            let objOne={}
            objOne.visitUnitCode=e.currentTarget.dataset.item.visitUnitCode
            objOne.visitUnitName=e.currentTarget.dataset.item.visitUnitName
            this.triggerEvent('changeValue', {objOne}, {})  //通知父组建事件
        },
        getValue(event){
            // 父组建更新
            console.log(event)
            this.setData({
                styleFlag:event.detail.objOne.visitUnitCode
            })
            let objOne=event.detail.objOne
            this.triggerEvent('changeValue', {objOne}, {})  
            // 再通知一次父组建 
        },
        getId(event){
            console.log('拿到id')
            console.log(event.detail.id)
            let id=event.detail.id
            this.triggerEvent('foldList', {id}, {})  //通知父组建事件
        },
    }
})
