/* pages/waste-material/waste-material-point/waste-material-point.wxss */
.container {
  padding: 0 32rpx 32rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  padding: 48rpx 0 32rpx;
  text-align: center;
}

.title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  line-height: 40rpx;
}

.form-content {
  margin-top: 60rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.cell-item {
  --td-cell-title-font-size: 32rpx;
  --td-cell-note-font-size: 28rpx;
  --td-cell-vertical-padding: 24rpx;
}

.note-text {
  color: #999 !important;
}

.cell-item[data-selected="true"] .note-text {
  color: #0052d9 !important;
}

.submit-section {
  padding: 48rpx 32rpx 32rpx;
  background-color: #fff;
}

/* 选择器相关样式 */
.picker-container {
  background-color: #fff;
}

/* 必填字段标识 */
.cell-item[required]::before {
  content: "*";
  color: #e34d59;
  font-size: 28rpx;
  margin-right: 8rpx;
}

/* 提交按钮样式增强 */
.submit-section .t-button {
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
  .container {
    padding: 0 24rpx 24rpx;
  }
  
  .title {
    font-size: 36rpx;
  }
  
  .subtitle {
    font-size: 26rpx;
  }
}

/* 加载状态 */
.submit-section .t-button[loading] {
  opacity: 0.8;
}

/* 表单验证错误状态 */
.cell-item.error {
  --td-cell-title-color: #e34d59;
}

.cell-item.error .note-text {
  color: #e34d59 !important;
}

/* 空数据提示 */
.empty-tip {
  text-align: center;
  padding: 60rpx 32rpx;
  color: #999;
  font-size: 28rpx;
}

/* 多选对话框样式 */
.dialog-content {
  max-height: 400rpx;
  padding: 24rpx 0;
}

.dialog-content .t-checkbox {
  padding: 16rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.dialog-content .t-checkbox:last-child {
  border-bottom: none;
}

/* 已选择项目提示 */
.selected-points-tip {
  color: #0052d9;
  font-size: 24rpx;
  margin-top: 8rpx;
  padding: 0 32rpx;
} 