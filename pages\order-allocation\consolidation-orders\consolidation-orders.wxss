/* pages/order-allocation/consolidation-orders/consolidation-orders.wxss */
/* pages/order-allocation/loading-order/loading-order.wxss */
.container{
    width: 100%;
    height: 100%;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    box-sizing: border-box;  
}
.scroll-container{
    height: calc(100% - 100rpx);
    overflow: hidden;
    flex-grow:1;
}
.order-scroll{ 
    width: 100%;
    height: 100%;
    overflow: auto;
 }
 .dataList-empty{
    width: 100%;
    height: calc(100% - 100rpx);
    display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center; 
 }
 .my-order-img{
    width: 150px;
    height: 150px;
    display: block;
}
.my-order-text{
    text-align: center;
	font-size: 16px;
	color: rgb(51, 51, 51);
}
.order-list{
    width:calc(100% - 64rpx);
    padding:20rpx 30rpx;
    background-color: #ffffff;
    margin: 0 auto 20rpx;
    border-radius: 20rpx;
    box-sizing: border-box;
}
.list-name{
    display: flex;
    flex-direction: row;
    align-items: center;
    align-content: center;
    margin-bottom: 10rpx;
}
.list-name-checked image{
    width: 32rpx;
    height: 32rpx;
    display: flex;
    align-items: center;
}
.list-name-number{
    font-size: 32rpx;
    color: #333333;
    font-weight: 500;
    margin-left: 10rpx;
}
.list-name-start{
    font-size: 28rpx;
    color: #666666;
    line-height: 40rpx;
    margin-bottom: 10rpx;
}
.list-name-cloum{
    display:flex;
    flex-direction: row;
    flex-wrap: wrap;
}
.list-name-flex{
    flex:0 0 50%;
}
.bomTxt {
display: flex;
justify-content: center;
font-size: 12px;
color: rgb(126, 138, 155);
padding: 0rpx 0rpx 20rpx 0rpx;
}


.order-button{
    width: 100%;
    height: 100rpx;
    display: flex;
    align-items: center;
} 
.whole-order{
    width: 80%;
    height:64rpx;
    background-color:#1953E6;
    color: #ffffff;
    text-align: center;
    line-height: 64rpx;
    margin: 0 auto;
    font-size: 28rpx;
    border-radius: 8rpx;
}