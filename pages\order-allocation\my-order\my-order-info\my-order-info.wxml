<!--pages/order-allocation/loading-order-detail/loading-order-detail.wxml-->
<view class="container">
  <!-- 汇总信息 -->
  <view class="summary-info">
    <view class="summary-title">汇总信息</view>
    <view class="summary-content">
      <view class="summary-item">
        <text class="summary-label">总捆包数：</text>
        <text class="summary-value">{{summaryInfo.totalPackCount}}</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">总提单数：</text>
        <text class="summary-value">{{summaryInfo.totalVoucherCount}}</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">总重量：</text>
        <text class="summary-value">{{summaryInfo.totalWeight}}</text>
      </view>
    </view>
  </view>
  
  <view class="scroll-container">
    <view class="order-scroll {{list.length>0 ? '' : 'dataList-empty' }}">
      <view wx:for="{{list}}" wx:key="index" wx:for-item="item">
        <view class='order-list'>
          <view class="list-name" bindtap="cheackList" data-index="{{index}}">
            <view class="list-name-checked">
              <image src="/assets/image/icon_UncheckBox.png" class='cheacked-img' wx:if="{{!item.flag}}"></image>
              <image src="/assets/image/icon_checkBox.png" class='cheacked-img' wx:if="{{item.flag}}"></image>
            </view>
            <view class="list-name-number">{{item.allocVehicleSeq}}</view>
            <view class="billing-method-tag">
              {{item.billingMethod == '20' || item.billingMethod == '30' ? '形式' : '普通'}}
            </view>
          </view>
          <view class="list-name-start">捆包号：{{item.packId}}</view>
          <view class="list-name-start">提单号：{{item.voucherNum}}</view>
          <view class="list-name-start">配单号：{{item.allocateVehicleNo}}</view>
          <!-- <view class="list-name-start">销售订单号：{{item.purOrderNum}}</view> -->
          <!-- <view class="list-name-start">客户零件号：{{item.custPartId}}</view> -->
          <view class="list-name-start">客户零件名称：{{item.custPartName}}</view>
          <view class="list-name-start">库位名称：{{item.locationName}}</view>
          <view class="list-name-start">母卷号：{{item.mPackId}}</view>
          <!-- <view class="list-name-start">仓库代码：{{item.warehouseCode}}</view> -->
          <view class="list-name-start">仓库名称：{{item.warehouseName}}</view>
          <view class="list-name-start">规格：{{item.specsDesc}}</view>
          <view class="list-name-start">张数/重量：{{item.piceNum || 0}}/{{item.netWeight}}</view>
          <view class="list-name-start">是否自带货：{{item.outPackFlag == 1 ? '是' : '否'}}</view>
          <view class="list-name-start">提单备注：{{item.ladingBillRemark}}</view>
        </view>
        <view wx:if="{{!list.length>0}}">
          <image src="/assets/image/empty_data.png" class="my-order-img"></image>
          <view class="my-order-text">暂无数据</view>
        </view>
      </view>
    </view>
    <view class="order-button" id="order-button">
      <view class="whole-order" bindtap="cancelOrder" style="background-color: #d54941;">取消配单</view>
    </view>
  </view>



  <t-dialog visible="{{showMsg}}" title="通知" content="{{content}}" confirm-btn="{{ confirmMsgBtn }}" bind:confirm="closeMsgDialog" />

</view>