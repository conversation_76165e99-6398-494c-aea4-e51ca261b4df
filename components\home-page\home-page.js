// components/home-page.js
Component({

  /**
   * 组件的属性列表
   */
  properties: {

  },

  /**
   * 组件的初始数据
   */
  data: {
    user: '',
    isShowConsolidation: false,
    isDriver: false,
    isUser: false,
  },

  attached() {
    const user = wx.getStorageSync('userInfo');
    this.setData({
      user,
      isShowConsolidation: ['30'].includes(user.reservationIdentity),
      isDriver: ['30','50'].includes(user.reservationIdentity),
      isUser: user.reservationIdentity == '50',
    })
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 跳转到新增司机页面
     */
    insertDriver() {
      wx.navigateTo({
        url: '/pages/driver/driver-insert/driver-insert'
      });
    },

    /**
     * 跳转到维护司机页面
     */
    editDriver() {
      wx.navigateTo({
        url: '/pages/driver/driver-list/driver-list'
      });
    },
    /**
     * 跳转到配单页
     */
    toOrderAllocation() {
      wx.removeStorageSync('editData');
      wx.navigateTo({
        url: '/pages/order-allocation/loading-order/loading-order'
      });
    },
    toDischargeCargo() {
      wx.navigateTo({
        url: '/pages/order-allocation/discharge-cargo/discharge-cargo'
      });
    },
    /**
     * 跳转到现货交易配单页面
     */
    toSpotTradingOrder() {
      wx.removeStorageSync('editData');
      wx.navigateTo({
        url: '/pages/order-allocation/spot-trading-order/spot-trading-order'
      });
    },
    toMyOrder() {
      wx.removeStorageSync('editData');
      wx.navigateTo({
        url: '/pages/order-allocation/my-order/my-order'
      });
    },
    toConsolidation(){
        wx.navigateTo({
            url: '/pages/order-allocation/consolidation-orders/consolidation-orders'
          });  
    },
    enterFactory() {
      wx.navigateTo({
        url: '/pages/order-allocation/reservation-factory/reservation-factory'
      });
    },
    enterReview(){
        wx.navigateTo({
            url: '/pages/order-allocation/my-review/my-review'
          });  
    },
    /**
     * 跳转到新增预约页面
     */
    toInsertReservation() {
      wx.navigateTo({
        url: '/pages/insert-reservation/insert-reservation'
      });
    },

    /**
     * 跳转到游客临时预约页面
     */
    toTouristReservation() {
      wx.navigateTo({
        url: '/pages/tourist-reservation/tourist-reservation'
      });
    },
    /**
     * 跳转到审核预约页面
     */
    toExamineReservation(){
        wx.navigateTo({
            url: '/pages/examine-reservation/examine-reservation'
          });
    },
    /**
     * 跳转到客户签收页面
     */
    toCustomerSign() {
      wx.navigateTo({
        url: '/pages/customer-sign/customer-sign-list/customer-sign-list'
      });
    },

    /**
     * 跳转到厂内物流
     */
    toInPlant() {
      wx.navigateTo({
        url: '/pages/in-plant/in-plant'
      });
    },

    /**
     * 跳转到提货委托
     */
    toDelivery() {
      wx.navigateTo({
        url: '/pages/delivery-commission/delivery-commission-list/delivery-commission-list'
      });
    },

    /**
     * 跳转到新增废次材装卸点
     */
    toWasteVehicle() {
      wx.navigateTo({
        url: '/pages/waste-material/waste-material-point/waste-material-point'
      });
    },
  }
})