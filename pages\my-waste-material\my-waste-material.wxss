/* 卡片主容器 */
.card-main {
    background-color: #ffffff;
    /* 卡片背景颜色 */
    margin: 10px 15px;
    /* 上下10px间距，左右15px间距 */
    padding: 15px;
    /* 内部填充 */
    border-radius: 8px;
    /* 卡片圆角 */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    /* 阴影效果 */
}

/* 卡片内部每一项的容器，使用flex布局 */
.card-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    /* 左右对齐 */
    padding: 6px 0;
    /* 每一项的上下内边距 */
    border-bottom: 1px solid #eaeaea;
    /* 每一项之间的分割线 */
    position: relative;
    gap: 16rpx;
}

/* 最后一项不需要分割线 */
.card-item:last-child {
    border-bottom: none;
    align-items: center;
}

/* 左边标签的样式 */
.label {
    font-size: 16px;
    /* 字体大小 */
    color: #333;
    /* 字体颜色 */
    flex-shrink: 0;
    min-width: 140rpx;
}

/* 右边值的样式 */
.value {
    font-size: 16px;
    /* 字体大小 */
    color: #666;
    /* 字体颜色 */
    text-align: right;
    /* 右对齐 */
    flex: 1;
    /* 占据剩余空间 */
}

/* 按钮容器 */
.button-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12rpx;
    margin-top: 16rpx;
}

/* 操作按钮样式 */
.action-button {
    flex: 1;
    height: 64rpx !important;
    font-size: 24rpx !important;
    border-radius: 8rpx !important;
}

.example-search {
    background-color: var(--bg-color-demo);
    padding: 16rpx 32rpx;
}

/* 无数据提示样式 */
.empty-container {
    padding: 100rpx 0;
}

/* 分页状态优化 */
.pagination-status {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20rpx;
    color: #666;
    font-size: 24rpx;
    text-align: center;
}

.pagination-status .divider {
    width: 1px;
    height: 24rpx;
    background: #ddd;
    margin: 0 20rpx;
}

/* 特殊样式 - 废料提货相关 */
.waste-material-info {
    color: #f56c6c;
    font-weight: 600;
}

.pickup-status {
    display: inline-block;
    margin-left: 10rpx;
}

.weight-info {
    color: #409eff;
    font-weight: 600;
}

/* 卡片头部样式 */
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eaeaea;
    gap: 16rpx;
}

.vehicle-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
}

.vehicle-text {
    font-weight: 600;
    color: #333;
    font-size: 32rpx;
    text-align: right;
}

/* 状态标签样式 */
.status-tag {
    margin-left: auto;
    flex-shrink: 0;
}

/* 装卸点容器样式 */
.loading-points-container {
    flex: 1;
    display: flex;
    justify-content: flex-end;
}

/* 装卸点标签样式 */
.loading-points {
    display: flex;
    flex-wrap: wrap;
    gap: 8rpx;
    justify-content: flex-end;
    align-items: flex-start;
    text-align: right;
    max-width: 100%;
}

.point-tag {
    background-color: #e8f4f8;
    color: #1976d2;
    padding: 6rpx 16rpx;
    border-radius: 20rpx;
    font-size: 20rpx;
    border: 1rpx solid #bbdefb;
    white-space: nowrap;
    line-height: 1.2;
    text-align: center;
    box-shadow: 0 1rpx 3rpx rgba(0,0,0,0.1);
    margin-bottom: 4rpx;
}

.no-points {
    color: #999;
    font-size: 28rpx;
}

.type-tag {
    margin-left: 10rpx;
} 

/* 状态筛选器样式 */
.status-filter {
  padding: 16rpx 32rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
  display: flex;
  align-items: center;
}

.filter-label {
  font-size: 28rpx;
  color: #333333;
  margin-right: 20rpx;
  font-weight: 500;
}

.filter-buttons {
  display: flex;
  gap: 16rpx;
}

.filter-btn {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  border-radius: 20rpx;
  background-color: #ffffff;
  color: #666666;
  border: 2rpx solid #d9d9d9;
  text-align: center;
  transition: all 0.3s ease;
  font-weight: 400;
}

.filter-btn.active {
  background-color: #1953E6;
  color: #ffffff;
  border-color: #1953E6;
  font-weight: 600;
  box-shadow: 0 2rpx 8rpx rgba(25, 83, 230, 0.3);
  transform: scale(1.02);
}

.filter-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 装卸点选择对话框样式 */
.dialog-content {
  max-height: 400rpx;
  padding: 24rpx 0;
}

.dialog-content .t-checkbox {
  padding: 16rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.dialog-content .t-checkbox:last-child {
  border-bottom: none;
}

.selected-points-tip {
  padding: 16rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
  font-size: 24rpx;
  color: #666;
}