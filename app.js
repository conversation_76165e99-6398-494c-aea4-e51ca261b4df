import gulpError from './utils/gulpError';
App({
  // 版本信息配置
  globalData: {
    version: 'V1.0.17',
    versionCode: '1.0.17',
    // 业务类型列表 - 全局共享数据
    typeList: [
      // { label: '钢材装货', value: '10' },
      // { label: '钢材卸货', value: '20' },
      // { label: '钢材卸货+装货', value: '30' },
      { label: '钢材装卸货', value: '10' },
      { label: '托盘运输', value: '40' },
      // { label: '资材卸货', value: '50' },
      {
        label: '废料提货',
        value: '60'
      },
      // { label: '欧冶提货', value: '70' }, // 重庆不需要
      {
        label: '其他物品运输',
        value: '80'
      },
    ]
  },

  onShow() {
    if (gulpError !== 'gulpErrorPlaceHolder') {
      wx.redirectTo({
        url: `/pages/gulp-error/index?gulpError=${gulpError}`,
      });
    }
  },

  onLaunch() {
    // 检查小程序更新
    this.checkForUpdate();
    
    // 在app.js或者页面的onLoad方法中
    const isLoggedIn = wx.getStorageSync('userLoggedIn');
    if (isLoggedIn) {
      // 用户已经登录，根据 lease 字段决定跳转页面
      const loginResponseData = wx.getStorageSync('loginResponseData');
      if (loginResponseData && loginResponseData.lease === "30") {
        // lease 为 "30" 时跳转到常驻车页面
        wx.redirectTo({
          url: '/pages/resident-car/resident-car',
        });
      } else {
        // 其他情况跳转到首页
        wx.redirectTo({
          url: '/pages/index/index',
        });
      }
    }
  },

  // 检查小程序更新
  checkForUpdate() {
    const updateManager = wx.getUpdateManager();
    
    updateManager.onCheckForUpdate((res) => {
      // 请求完新版本信息的回调
      console.log('检查更新结果:', res.hasUpdate);
    });

    updateManager.onUpdateReady(() => {
      wx.showModal({
        title: '更新提示',
        content: '新版本已经准备好，是否重启应用？',
        success: (res) => {
          if (res.confirm) {
            // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
            updateManager.applyUpdate();
          }
        }
      });
    });

    updateManager.onUpdateFailed(() => {
      // 新版本下载失败
      wx.showToast({
        title: '更新失败，请检查网络',
        icon: 'none'
      });
    });
  },

  amapKey: '9fca9c2a1ebace691cfb4f3d4cc417f0', // 高德开发平台Key
  // mesUrl: 'http://localhost:8090/mes/service',
  // mesUrl: 'http://ims-em-test.baointl.info/service',// 测试环境 
  mesUrl: 'https://zhihui.cqbaosteel.com:8888/service', // 外网测试
  // mesUrl: 'https://zhihui.cqbaosteel.com:8880/imom/service', // 外网正式
  segNo: 'JC000000', // 注册时调用
  isPhone: (tel) => /^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(tel),
  regIdCard: (id) => /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/.test(id),
  isCarNumb: (carNumber) => /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼港澳使领][A-Z][A-Z0-9]{5}$|^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼港澳使领][A-Z][A-Z0-9]{6}$/.test(carNumber),
  isChinese: (str) => /^[\u4E00-\u9FA5·]+$/.test(str),
  truncateDecimals: (numStr, decimalPlaces) => {
    if (!numStr) {
      return numStr;
    }
    let parts = numStr.split(".");  // 分割整数部分和小数部分
    if (parts.length === 1) return numStr; // 没有小数部分，直接返回
    return parts[0] + "." + parts[1].substring(0, decimalPlaces);
  },
  formatNumber: (num) => {
    // 处理非数值或无效值
    if (typeof num !== 'number' || isNaN(num)) return '0.000000';

    // 缩放并向下取整
    const scaled = num * 1e6;          // 放大到整数范围
    const floored = Math.floor(scaled); // 向下取整
    const result = floored / 1e6;       // 恢复为6位小数
    return result.toFixed(6); // 直接返回补零后的字符串
  },
  getUserName: (name) => {
    let rIdName = '';
    switch (name) {
      case '10':
        rIdName = '客户车辆管理员';
        break;
      case '20':
        rIdName = '承运商车辆管理员'
        break;
      case '30':
        rIdName = '司机'
        break;
      case '50':
        rIdName = '游客'
        break;
      case '70':
        rIdName = '委外单位管理员'
        break;
      default:
        rIdName = '内部员工';
        break;
    }
    return rIdName;
  },
  
  /**
   * 对角色列表进行排序，将司机身份排到第一位
   * @param {Array} roleList - 角色列表数组，每个元素包含 label 和 value 属性
   * @returns {Array} 排序后的角色列表，司机身份在第一位
   */
  sortRoleList: (roleList) => {
    return roleList.sort((a, b) => {
      if (a.value === '30') return -1; // 司机排第一
      if (b.value === '30') return 1;
      return 0; // 其他保持原顺序
    });
  }
});
