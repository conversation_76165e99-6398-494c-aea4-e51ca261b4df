<!--pages/home-login.wxml-->
<view class="container">

  <view class="col">
    <t-image src="{{imageSrc}}" mode="heightFix" width="72" height="72" shape="round" />
    <view class="slogan-container">
      <view class="company-name-container">
        <text class="company-name">重</text>
        <text class="company-name">宝</text>
        <text class="company-name">智</text>
        <text class="company-name">享</text>
      </view>
      <view class="slogan-line">
        <view class="slogan-text-container">
          <text class="slogan-text">智</text>
          <text class="slogan-text">启</text>
          <text class="slogan-text">新</text>
          <text class="slogan-text">程</text>
        </view>
        <view class="vertical-divider"></view>
        <view class="slogan-text-container">
          <text class="slogan-text">物</text>
          <text class="slogan-text">畅</text>
          <text class="slogan-text">其</text>
          <text class="slogan-text">流</text>
        </view>
      </view>
    </view>
  </view>



  <view class="main-btn">
    <view class="button-example wechat-login">
      <t-button theme="primary" size="large" block open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">手机号一键登录</t-button>
    </view>
    <view class="button-example">
      <t-button theme="light" block size="large" t-class="group-btn" bind:tap="onOtherPhoneLogin">其他手机号登录</t-button>
    </view>

    <!-- 添加说明文字 -->
    <view class="notice-text">
      <text>账号仅限重庆宝钢用户登录并进行登录账号鉴权。</text>
    </view>

    <view class="agreement">

      <t-radio-group value="{{isChecked}}" bind:change="radioChange" borderless t-class="box" data-key="isChecked" style="text-align: center;padding: 0;margin: 0;background-color: transparent;">
        <t-radio allow-uncheck value="1" style="padding: 0;margin: 0;background-color: transparent;" />
      </t-radio-group>

      <text>我已阅读并同意</text>
      <navigator url="/pages/agreement/userService/userService" class="link">用户服务协议</navigator>
      <text>、</text>
      <navigator url="/pages/agreement/privacyPolicy/privacyPolicy" class="link">隐私政策</navigator>
    </view>

  </view>

  <!-- 操作指引链接区域 -->
  <view class="guide-container">
    <view class="guide-item" bindtap="downloadAdminGuide">
      <text class="guide-text">操作手册</text>
    </view>
  </view>

  <!-- 版本号显示 -->
  <view class="version-container" bindtap="checkUpdate">
    <text class="version-text">版本: {{version}}</text>
  </view>

  <t-picker visible="{{segNoVisible}}" value="{{segNoValue}}" data-key="segNo" title="选择账套" cancelBtn="取消" confirmBtn="确认" usingCustomNavbar bindchange="onPickerChange" bindcancel="onPickerCancel">
    <t-picker-item options="{{segNos}}" />
  </t-picker>

  <t-picker visible="{{cityVisible}}" value="{{cityValue}}" auto-close="{{false}}" cancel-btn="取消" title="选择角色" confirmBtn="确认" usingCustomNavbar bind:confirm="onCityChange" bindcancel="onCityCancel">
    <t-picker-item options="{{citys}}"></t-picker-item>
  </t-picker>

  <!-- 包含"不存在"错误时显示双按钮弹框 -->
  <t-dialog 
    wx:if="{{showTemporaryReservation}}"
    visible="{{showTextAndTitle}}" 
    title="提示" 
    content="{{dialogContent}}" 
    confirm-btn="{{ confirmBtn }}" 
    cancel-btn="临时预约"
    bind:confirm="closeDialog" 
    bind:cancel="onTemporaryReservation" />

  <!-- 其他错误时显示单按钮弹框 -->
  <t-dialog 
    wx:else
    visible="{{showTextAndTitle}}" 
    title="提示" 
    content="{{dialogContent}}" 
    confirm-btn="{{ confirmBtn }}" 
    bind:confirm="closeDialog" />

</view>