/* pages/insert-reservation/insert-reservation.wxss */
/* 在 WXSS 文件中添加样式 */
.container {
  padding: 20px;
  background-color: #f5f5f5;
}

.time-section {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.block {
  color: var(--td-text-color-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.block--bottom {
  width: 100vw;
  height: 240px;
}

.wrapper {
  margin-bottom: 32rpx;
}

.wrapper:last-child {
  margin-bottom: 0;
}

.aaa {
  display: flex;
  flex-direction: column;
  /* 使子元素垂直排列 */
}

.example-search {
  order: 1;
  /* 搜索框在上方 */
  width: 100%;
  position: relative;
  /* 改为相对定位 */
  z-index: 2;
}

.example-picker {
  order: 2;
  /* 选择器在下方 */
  width: 100%;
  position: relative;
  z-index: 1;
  margin-top: 0;
  /* 移除原有 margin */
}

.dialog-search {
  margin-top: 1em !important;
}

.btn-group {
  margin-top: 12px;
}

.label-suffix {
  --td-tag-small-height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12rpx;
}

.long-content {
  height: 576rpx;
  margin-top: 16rpx;
  font-size: 32rpx;
  color: #888;
}

.long-content .content-container {
  white-space: pre-line;
}

.app-title {
  min-width: 6em !important;
}

.is-bring-textarea {
  font-size: var(--td-cell-title-font-size, var(--td-font-size-m, 32rpx)) !important;
}