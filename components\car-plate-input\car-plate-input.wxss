.plate-display {
  display: flex;
  justify-content: center;
  padding: 20rpx;
}

.code-box {
  width: 60rpx;
  height: 80rpx;
  border: 2rpx solid #ccc;
  margin: 10rpx;
  text-align: center;
  line-height: 80rpx;
  font-size: 32rpx;
  background-color: #f0f0f0;
}

.code-box.active {
  background-color: #4e9dff;
  color: #fff;
}

.custom-keyboard {
  background: #f0f0f0;
  padding: 20rpx;
  position: fixed;
  /* 使用 fixed 定位 */
  bottom: 0;
  /* 固定在页面底部 */
  left: -0.5em;
  width: 100%;
  /* 确保宽度占满全屏 */
  z-index: 99999;
  margin-bottom:1em;
  /* 确保键盘在页面最上层 */
}

.key {
  background: #fff;
  margin: 8rpx;
  padding: 20rpx;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.province-box {
  text-align: center;
  padding: 10rpx;
  background-color: #ddd;
  font-size: 28rpx;
}

.province-keyboard {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 20rpx;
}

.province-keyboard .key {
  width: 50rpx;
  height: 50rpx;
  text-align: center;
  line-height: 50rpx;
  background-color: #fff;
  margin: 5rpx;
  border-radius: 10rpx;
}

.code-boxes {
  display: flex;
}

/* 新增关闭按钮样式 */
/* 省份键盘样式 */
.province-keyboard {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 20rpx;
}

.province-keyboard .key {
  width: 60rpx;
  height: 60rpx;
  text-align: center;
  line-height: 60rpx;
  background-color: #fff;
  margin: 5rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

/* 关闭按钮样式 */
.close-btn {
  text-align: right;
  padding: 20rpx 40rpx;
  color: #4e9dff;
  font-size: 28rpx;
}

/* 调整键盘布局 */
.key-row {
  display: flex;
  justify-content: center;
  margin: 8rpx 0;
}

.key {
  width: 60rpx;
  height: 60rpx;
  margin: 0 4rpx;
  padding: 0;
  flex-shrink: 0;
  text-align: center;
  line-height: 60rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.last-row .key:nth-child(1),
.last-row .key:nth-child(2),
.last-row .key:nth-child(3) {
  width: 80rpx;
  /* 特殊字符加宽 */
}

.last-row .key:nth-child(1),
.last-row .key:nth-child(2),
.last-row .key:nth-child(3),
.last-row .key:nth-child(4) {
  width: 80rpx;
  /* 特殊字符加宽 */
}

/* 回退按钮样式 */
.backspace-key {
  width: 100rpx !important;
  /* 适当加宽回退按钮 */
  background-color: #ff4d4d !important;
  /* 使用醒目的颜色 */
  color: #fff !important;
}

.disabled {
  opacity: 0.5;
  /* 显示半透明 */
  pointer-events: none;
  /* 禁止点击事件 */
  /* 其他想要的禁用效果，比如文字颜色、背景色等 */
  color: #999;
  background-color: #f0f0f0;
}