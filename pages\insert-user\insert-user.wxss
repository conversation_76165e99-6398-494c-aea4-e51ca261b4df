/* pages/insert-user/insert-user.wxss */
.container {
  padding: 20px;
  background-color: #f5f5f5;
}

.btn-group {
  margin-top: 16px;
}

.aaa {
  display: flex;
  flex-direction: column;
  /* 使子元素垂直排列 */
}

.example-search {
  order: 1;
  /* 搜索框在上方 */
  width: 100%;
  position: relative;
  /* 改为相对定位 */
  z-index: 2;
}

.example-picker {
  order: 2;
  /* 选择器在下方 */
  width: 100%;
  position: relative;
  z-index: 1;
  margin-top: 0;
  /* 移除原有 margin */
}

.app-title {
  min-width: 6em !important;
}
.company-class {
  text-align: right;
}