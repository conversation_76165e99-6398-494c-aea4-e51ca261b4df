// pages/order-allocation/my-order/my-order-info/my-order-info.js
const $api = require('../../../../api/request');
import {
  Toast
} from 'tdesign-miniprogram';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    item: '',
    showMsg: false,
    content: '',
    confirmMsgBtn: { content: '知道了', variant: 'base' },
    list: [],
    summaryInfo: {
      totalPackCount: 0,
      totalVoucherCount: 0,
      totalWeight: 0
    }, // 汇总信息
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const eventChannel = this.getOpenerEventChannel();
    if (!eventChannel) {
      return;
    }
    eventChannel.emit('acceptDataFromOpenedPage', { data: '' });
    eventChannel.on('acceptDataFromOpenerPage', (data) => {
      const resultData = data.data;
      this.getchildrenList(resultData);
      this.setData({
        item: resultData,
      });
    });
  },

  getchildrenList(data) {
    wx.showLoading({
      title: '加载中',
    })
    const app = getApp();

    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        allocateVehicleNo: data.allocateVehicleNo,
        serviceId: 'S_LI_RL_0150',
      },
      success: (res) => {
        wx.hideLoading();
        const result = res.data;
        if (result.__sys__?.status == -1) {
          this.setData({
            showMsg: true,
            content: result.__sys__.msg,
          });
          return;
        }

        console.log(result);
        this.setData({
          list: result.list.map(r => {
            return {
              ...r,
              netWeight: app.truncateDecimals(r.netWeight, 3),
            }
          }),
          summaryInfo: result.summaryInfo || {
            totalPackCount: 0,
            totalVoucherCount: 0,
            totalWeight: 0
          }
        })
      },
      fail: () => {
        wx.hideLoading();
        this.setData({
          showMsg: true,
          content: '网络异常, 请稍后重试',
        });
      }
    });
  },

  cheackList(e) {
    let index = e.currentTarget.dataset.index;
    let dataList = this.data.list;
    dataList[index].flag = !dataList[index].flag
    this.setData({
      list: dataList
    })
  },

  /**
   * 取消配单
   */
  cancelOrder() {
    let detailsList = this.data.list.filter(item => {
      return item.flag == true
    });

    if (detailsList.length == 0) {
      wx.showToast({
        title: '请先勾选数据',
        icon: 'none',
        duration: 1000
      });
      return;
    }

    const user = wx.getStorageSync('userInfo');
    const data = {
      ...this.data.item,
      packIdList: detailsList.map(m => m.packId),
      recRevisor: user.tel,
      recRevisorName: user.administrator ?? user.driverName,
    };
    $api.request('S_LI_RL_0154', '', data).then((res) => {
      Toast({
        context: this,
        selector: '#t-toast',
        message: '取消成功',
      });
      this.getchildrenList(this.data.item);
    });
  },

  closeMsgDialog() {
    this.setData({
      showMsg: false,
    })
  },
})