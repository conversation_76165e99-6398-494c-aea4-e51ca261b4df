/* 全局容器样式 */
.container {
  background-color: #f2f3f5;
  padding: 20rpx;
}

/* 部分标题样式 */
.section {
  margin-top: 20rpx;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  color: #000; /* 字体为黑色 */
  margin-bottom: 10rpx;
  margin-left: 30rpx;
  margin-top: 30rpx;
}

.line {
  width: 5rpx;
  height: 20rpx;
  background-color: #1c86ee; /* 蓝色竖线 */
  margin-right: 12rpx;
}

/* Grid 样式 */
t-grid {
  background-color: #ffffff;
  padding: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

t-grid-item {
  text-align: center;
  /* color: #333; */
}
