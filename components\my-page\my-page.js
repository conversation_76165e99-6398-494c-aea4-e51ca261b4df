// components/my-page/my-page.js
Component({

  /**
   * 组件的属性列表
   */
  properties: {

  },

  /**
   * 组件的初始数据
   */
  data: {
    firstName: "",
    name: "",
    phoneNum: "",
    userInfo: '',
  },

  // 组件挂载时触发
  attached() {
    this.refreshData();
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 跳转到编辑个人信息
     */
    onEditTap() {
      wx.navigateTo({
        url: '/pages/user-info/user-info'
      });
    },

    /**
   * 跳转到我的车辆页面
   */
    toMyCar() {
      wx.navigateTo({
        url: '/pages/my-car/my-car'
      });
    },

    /**
     * 跳转到我的预约页面
     */
    toMyReservation() {
      wx.navigateTo({
        url: '/pages/my-reservation/my-reservation'
      });
    },

    /**
 * 跳转到我的角色页面
 */
    toMyRole() {
      wx.navigateTo({
        url: '/pages/my-role/my-role'
      });
    },

    /**
     * 跳转到废料提货页面
     */
    toInPlant() {
      wx.navigateTo({
        url: '/pages/my-waste-material/my-waste-material'
      });
    },

    refreshData() {
      let fName = '';
      const user = wx.getStorageSync('userInfo');
      this.setData({
        userInfo: user,
      })
      if (['20','40'].includes(user.identityType)) {
        fName = user.driverName.substr(0, 1);
        this.setData({
          firstName: fName,
          name: user.driverName,
          phoneNum: user.tel,
        });
        return;
      }
      // 内部员工
      if (user.reservationIdentity == '40') {
        fName = user.userName.substr(0, 1);
        this.setData({
          firstName: fName,
          name: user.userName,
          phoneNum: user.tel,
        });
        return;
      }
      fName = user.administrator.substr(0, 1);
      this.setData({
        firstName: fName,
        name: user.administrator,
        phoneNum: user.tel,
      });
    },
  }
})