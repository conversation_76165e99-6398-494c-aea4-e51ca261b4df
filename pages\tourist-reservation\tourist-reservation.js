// pages/tourist-reservation/tourist-reservation.js
import Message from 'tdesign-miniprogram/message/index';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    activeIndex: -1, // 子组件是否高亮
    carNumber: '',
    name: '',
    tel: '',
    driverIdentity: '',
    type: '请选择业务类型',
    typeValue: '',
    typeVisible: false,
    typeList: [], // 将从全局数据中获取
    showMsg: false,
    content: '',
    confirmMsgBtn: { content: '知道了', variant: 'base' },
    // 确认信息弹框相关数据
    showTextAndTitle: false,
    title: '提示',
    confirmBtn: {
      content: '确定',
      variant: 'base'
    },
    titleList: [],
    clientHeight: 0, // 用于存储可视区域高度
    isToBottom: false, // 是否滚动到底部
    isCountdown: false, // 倒计时是否完成
    dateText: '请选择预约起始日期',
    dateVisible: false,
    date: '',
    deadVisible: false,
    deadlineText: '请选择预约截止日期',
    deadDate: '',
    deadStart: '',
    deadEnd: '',
    start: '',
    isEdit: false,
    carArray: ['', '', '', '', '', '', '', '新能源'],
    reservationNumber: '', // 预约单号 用来修改
    visitUnit: '重庆宝钢',
    visitUnitVisible: false,
    visitUnitValue: '',
  },

  onLoad() {
    const app = getApp();
    // 从全局数据获取业务类型列表
    this.setData({
      typeList: app.globalData.typeList
    });

    const now = new Date();
    const formatted = [
      now.getFullYear(),
      (now.getMonth() + 1).toString().padStart(2, '0'),
      now.getDate().toString().padStart(2, '0'),
      now.getHours().toString().padStart(2, '0'),
      now.getMinutes().toString().padStart(2, '0')
    ].join('');

    const newDate = new Date(now.getTime() + 12 * 60 * 60 * 1000);
    const endDate = new Date(now.getTime() + 24 * 60 * 60 * 1000);
    
    // 获取用户信息并自动填充
    const userInfo = wx.getStorageSync('userInfo');
    let autoFillData = {};
    if (userInfo && userInfo.reservationIdentity === '50') {
      // 游客身份，自动填充用户信息
      autoFillData = {
        name: userInfo.driverName || userInfo.administrator || '',
        tel: userInfo.tel || '',
        driverIdentity: userInfo.driverIdentity || '',
      };
    }
    
    this.setData({
      isEdit: false,
      deadlineText: this.format(newDate),
      deadDate: this.format(newDate),
      deadStart: formatted,
      deadEnd: this.format(endDate),
      date: formatted,
      dateText: formatted,
      dateVisible: false,
      visitUnitList: [
        { label: '重庆宝钢', value: '重庆宝钢' },
        { label: '杭州宝伟', value: '杭州宝伟' },
      ],
      isBringGood: 0,
      ...autoFillData,
    });

    const eventChannel = this.getOpenerEventChannel();
    if (!eventChannel) {
      return;
    }
    eventChannel.emit('acceptDataFromOpenedPage', { data: '' });
    eventChannel.on('acceptDataFromOpenerPage', (data) => {
      const { reservationNumber, reservationTime, reservationDate, driverName, driverTel, driverIdentity, vehicleNo, typeName } = data.data;
      const vehicleNoList = vehicleNo.split('');
      if (vehicleNoList.length != 8) {
        vehicleNoList.push('新能源');
      }
      this.setData({
        isEdit: true,
        name: driverName,
        tel: driverTel,
        driverIdentity,
        type: [typeName],
        typeValue: [this.data.typeList.find(t => t.label == typeName).value],
        carArray: vehicleNoList,
        dateText: reservationDate,
        deadlineText: reservationTime,
        date: reservationDate,
        deadDate: reservationTime,
        carNumber: vehicleNo,
        reservationNumber,
      });
    });

  },

  showDeadPicker() {
    // 如果不选择起始时间，那边就不能选择截止时间
    if (!this.data.dateText || this.data.dateText.includes('请选择')) {
      this.setData({
        showMsg: true,
        content: '请先选择预约起始日期',
      });
      return;
    }

    this.setData({
      deadVisible: true,

    });
  },

  /**
 * 打开选择日期
 */
  showPicker() {
    if (!this.data.typeValue) {
      this.setData({
        showMsg: true,
        content: '请先选择业务类型',
      });
      return;
    }

    const now = new Date();
    this.setData({
      start: this.format(now),
      dateVisible: true,
    });

  },

  /**
 * 确定截止时期
 * @param {*} e 
 */
  onDeadChange(e) {
    const { value } = e.detail;
    this.setData({
      deadDate: value,
      deadlineText: value,
      deadVisible: false,
    });
  },

  /**
 * 是否自带货单选按钮事件
 * @param {*} e 
 */
  onBringChange(e) {
    this.setData({
      isBringGood: e.detail.value
    });
  },

  onColumnChange(e) {
    const { value } = e.detail;
    const year = parseInt(value.substring(0, 4));
    const month = parseInt(value.substring(4, 6)) - 1; // 月份从0开始
    const day = parseInt(value.substring(6, 8));
    const hours = parseInt(value.substring(8, 10));
    const minutes = parseInt(value.substring(10, 12));

    // 创建日期对象并加12小时
    const originalDate = new Date(year, month, day, hours, minutes);
    const newDate = new Date(originalDate.getTime() + 12 * 60 * 60 * 1000);
    const endDate = new Date(originalDate.getTime() + 24 * 60 * 60 * 1000);
    this.setData({
      deadlineText: this.format(newDate),
      deadDate: this.format(newDate),
      deadStart: value,
      deadEnd: this.format(endDate),
      date: value,
      dateText: value,
      dateVisible: false,
    });

  },

  /**
 * 关闭选择日期
 */
  hidePicker() {
    this.setData({
      dateVisible: false,
      deadVisible: false,
    });
  },

  closeMsgDialog() {
    this.setData({
      showMsg: false,
    })
  },

  /**
 * 打开业务类型选择框
 */
  onTypePicker() {
    this.setData({
      typeVisible: true,
    });
  },

  onVisitUnitPicker() {
    this.setData({
      visitUnitVisible: true,
    });
  },

  /**
 * 选择框确认事件
 * @param {*} e 
 */
  onPickerChange(e) {
    const { value, label } = e.detail;
    const { key } = e.currentTarget.dataset;
    this.setData({
      [`${key}Visible`]: false,
      [`${key}Value`]: value,
      [`${key}`]: label.join(' '),
    });
  },

  /**
   * 预约提交
   */
  submit() {
    let { reservationNumber, isEdit, name, carNumber, type, dateText, tel, driverIdentity, deadlineText, typeValue, visitUnit, isBringGood, bringTextarea } = this.data;
    const app = getApp();
    if (!name) {
      this.setData({
        showMsg: true,
        content: '请填写姓名',
      });
      return;
    }

    if (!tel || !app.isPhone(tel)) {
      this.setData({
        showMsg: true,
        content: '请填写正确的手机号',
      });
      return;
    }

    if (!driverIdentity || !app.regIdCard(driverIdentity)) {
      this.setData({
        showMsg: true,
        content: '请填写正确的身份证号',
      });
      return;
    }

    if (!carNumber) {
      this.setData({
        showMsg: true,
        content: '请填写车牌号或者关闭车牌号软键盘',
      });
      return;
    }

    if (type.includes('请选择')) {
      this.setData({
        showMsg: true,
        content: '请选择业务类型',
      });
      return;
    }

    if (!dateText || dateText.includes('请选择')) {
      this.setData({
        showMsg: true,
        content: '请选择预约日期',
      });
      return;
    }

    if (!deadlineText || deadlineText.includes('请选择')) {
      this.setData({
        showMsg: true,
        content: '请选择预约截止日期',
      });
      return;
    }

    if (isBringGood == 1 && !bringTextarea) {
      this.setData({
        showMsg: true,
        content: '请填写自带货描述',
      });
      return;
    }

    // 调用确认信息弹框
    this.openTitle(app);
  },

  /**
   * 确认新增(调用新增预约接口)
   */
  insert() {
    const isEdit = this.data.isEdit;
    wx.showLoading({
      title: isEdit ? '修改中' : '新增中',
    });
    const app = getApp();
    const { reservationNumber, name, tel, driverIdentity, carNumber, typeValue, dateText, deadlineText, visitUnit, isBringGood, bringTextarea } = this.data;

    const result = {
      segNo: app.segNo,
      unitCode: app.segNo,
      recCreator: tel,
      recCreatorName: name,
      driverName: name,
      driverTel: tel,
      driverIdentity,
      vehicleNo: carNumber,
      typeOfHandling: typeValue.join(''),
      reservationDate: dateText,
      reservationTime: deadlineText,
      // reservationIdentity: '30',
      reservationNumber,
      visitUnit,
      isSelfProduced: isBringGood,
      selfProducedDesc: bringTextarea,
    };

    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        result,
        serviceId: isEdit ? 'S_LI_RL_0100' : 'S_LI_RL_0015',
      },
      success: (res) => {
        wx.hideLoading();
        const result = res.data;
        if (result.__sys__?.status == -1) {
          this.setData({
            showMsg: true,
            content: result.__sys__.msg,
          });
          return;
        }

        // 新增成功, 清空数据
        this.setData({
          name: '请填写司机',
          tel: '请填写手机号',
          driverIdentity: '请填写身份证号',
          carNumber: '',
          carNumberValue: '',
          type: '请选择业务类型',
          typeValue: '',
          dateText: '请选择预约日期',
          deadEnd: '',
          deadDate: '',
          deadlineText: '请选择预约截止日期',
          isBringGood: 0,
          bringTextarea: '',
          visitUnit: '重庆宝钢',
          visitUnitVisible: false,
          visitUnitValue: '',
        });
        wx.showToast({
          title: '预约成功',
          icon: 'success',
        });
        
        // 检查用户是否已经登录，如果已经登录就不修改userInfo缓存
        const currentUserInfo = wx.getStorageSync('userInfo');
        const isLoggedIn = wx.getStorageSync('userLoggedIn');
        
        if (!isLoggedIn || !currentUserInfo || currentUserInfo.reservationIdentity !== '50') {
          // 只有在未登录或不是游客身份时才设置userInfo（用于游客注册流程）
          const data = {
            tel,
            identityType: '20',
            segNo: app.segNo,
            driverName: tel,
          };

          wx.setStorage({
            key: 'userInfo',
            data,
          });
        }

        setTimeout(() => {
          if (isEdit) {
            wx.navigateBack();
          } else {
            if (isLoggedIn && currentUserInfo && currentUserInfo.reservationIdentity === '50') {
              // 已登录的游客，返回到我的预约页面
              wx.navigateTo({
                url: `/pages/my-reservation/my-reservation`,
              });
            } else {
              // 未登录的游客注册流程，跳转到游客预约列表
              wx.redirectTo({
                url: `/pages/my-reservation/my-reservation?isKey=1`,
              });
            }
          }

        }, 500);
      },
      fail: () => {
        wx.hideLoading();
        this.setData({
          showMsg: true,
          content: '网络异常, 请稍后重试',
        });
      }
    });
  },


  /**
 * 自带货描述
 * @param {*} e 
 */
  onBringTextChange(e) {
    this.setData({
      bringTextarea: e.detail.value
    });
  },

  /**
 * input框伪双向绑定
 * @param {*} e 
 */
  onInputChang(e) {
    const { key } = e.currentTarget.dataset;
    const { value } = e.detail;
    this.setData({
      [key]: value,
    });
  },

  /** 子页面方法 */
  onInputComplete(event) {
    const { plate } = event.detail; // 获取子组件传递的车牌号
    console.log('车牌号:', plate); // 打印或处理车牌号

    // 更新父页面的 carNumber 数据
    this.setData({
      carNumber: plate
    });
  },

    /**
   * 新增之前先显示提示信息
   */
  openTitle(app) {
    wx.showLoading({
      title: '加载中',
    });
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        serviceId: 'S_LI_RL_0035',
        segNo: app.segNo,
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          this.setData({
            showMsg: true,
            content: '网络异常, 请稍后重试',
          });
          return;
        }

        const result = res.data;
        if (result.__sys__?.status == -1) {
          this.setData({
            showMsg: true,
            content: result.__sys__.msg,
          });
          return;
        }

        if (!result.list || result.list.length == 0) {
          // 没有提示信息直接新增
          this.insert();
          return;
        }

        const resultList = result.list;
        this.openInterval(resultList);
      },
      fail: () => {
        wx.hideLoading();
        this.setData({
          showMsg: true,
          content: '网络异常, 请稍后重试',
        });
      }
    });
  },

  /**
   * 关闭错误提示框
   */
  closeDialog() {
    this.setData({
      showTextAndTitle: false,
      isToBottom: false, // 重置是否滚动到底部
      isCountdown: false, // 重置倒计时是否完成
    });
    const { titleList } = this.data;
    if (!titleList || titleList.length == 0) {
      return;
    }
    // 先删除掉第一个元素
    titleList.shift();
    if (titleList.length == 0) {
      this.insert();
      return;
    }
    this.openInterval(titleList);
  },

  /**
   * 打开弹框定时
   */
  openInterval(resultList) {
    const r = resultList[0];

    let { bold, addRed, addUnderline, notificationText, countdownDuration } = r;
    // **步骤1：预处理，过滤掉空字符串或纯空格**
    const getValidSections = (str) => {
      return (str || "")
        .split(/[,，]/) // 中英文逗号分割
        .map(section => section.trim()) // 去除首尾空格
        .filter(section => section.length > 0); // 过滤空字符串
    };

    const boldSections = getValidSections(bold);
    const redSections = getValidSections(addRed);
    const underlineSections = getValidSections(addUnderline);

    // **步骤2：合并所有需要样式的文本（避免重复处理）**
    const styleMap = new Map(); // { text: Set<styles> }

    // 通用处理函数
    const addToStyleMap = (sections, styleType) => {
      sections.forEach(text => {
        if (!styleMap.has(text)) {
          styleMap.set(text, new Set());
        }
        styleMap.get(text).add(styleType);
      });
    };

    addToStyleMap(boldSections, "bold");
    addToStyleMap(redSections, "red");
    addToStyleMap(underlineSections, "underline");

    // **步骤3：按长度降序排序（避免短文本覆盖长文本）**
    // 步骤3：生成排序后的正则表达式
    const sortedSections = Array.from(styleMap.keys())
      .sort((a, b) => b.length - a.length) // 关键排序逻辑
      .map(text => ({
        text,
        regex: new RegExp(`(${text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, "g"),
        styles: styleMap.get(text)
      }));

    // **步骤4：单次替换，生成复合样式**
    let processedText = notificationText;
    sortedSections.forEach(({ text, regex, styles }) => {
      processedText = processedText.replace(regex, (match) => {
        const styleArr = [];
        if (styles.has("bold")) styleArr.push("font-weight: bold");
        if (styles.has("red")) styleArr.push("color: red");
        if (styles.has("underline")) styleArr.push("text-decoration: underline");
        return `<span style="${styleArr.join("; ")}">${match}</span>`;
      });
    });

    // **步骤5：最后处理换行**
    processedText = processedText.replace(/\n/g, "<br>");

    this.setData({
      titleList: resultList,
      showTextAndTitle: true,
      title: r.notificationTitle,
      content: processedText,
      confirmBtn: {
        content: `已知晓以上内容(${countdownDuration})`,
        variant: 'base',
        disabled: true,
      },
    });

    const query = wx.createSelectorQuery();
    // 等于0则没有滚动条, 不等于0有滚动条, 按钮控制放到滚动条事件中, 这里只是启用倒计时
    // 没有滚动条就相当于滚动到底部了, 由于是异步就写里面
    query.select('#dialogScroll').boundingClientRect((rect) => {
      // 299固定高度超出就有滚动条
      const isNoScrollBar = rect.height < 299;
      
      this.setData({
        clientHeight: rect.height, // 存储可视区域高度
        isToBottom: isNoScrollBar, // 无滚动条时直接到底部
      });

      countdownDuration--;

      const intervalId = setInterval(() => {
        this.setData({
          confirmBtn: {
            content: `已知晓以上内容(${countdownDuration})`,
            variant: 'base',
            disabled: true,
          },
        });

        // 倒计时完成
        if (countdownDuration == 0) {
          // 无滚动条或已滚动到底部
          if (isNoScrollBar || this.data.isToBottom) {
            this.setData({
              isCountdown: true,
              confirmBtn: {
                content: `已知晓以上内容`,
                variant: 'base',
                disabled: false,
              },
            });
          } else {
            // 有滚动条但未滚动到底部
            this.setData({
              isCountdown: true,
              confirmBtn: {
                content: `已知晓以上内容`,
                variant: 'base',
                disabled: true,
              },
            });
            Message.warning({
              context: this,
              offset: [90, 32],
              duration: 5000,
              content: '请下拉到底部',
            });
          }
          clearInterval(intervalId);
          return;
        }
        this.setData({
          isCountdown: false, // 倒计时没有完成
        });
        countdownDuration--;
      }, 1000);

    }).exec();
  },

  /**
   * 确认信息是否滚动到底部
   * @param {*} event 
   */
  handleScroll(event) {
    // 防抖处理，避免频繁执行
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
    }
    
    this.scrollTimer = setTimeout(() => {
      const scrollTop = event.detail.scrollTop; // 当前滚动位置
      const scrollHeight = event.detail.scrollHeight; // 总内容高度
      const clientHeight = this.data.clientHeight; // 使用已存储的可视区域高度
      const tolerance = 20; // 增加容差值为 20
      
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - tolerance;
      
      if (isAtBottom) {
        // 滚动到底部
        if (this.data.isCountdown) {
          this.setData({
            confirmBtn: {
              content: `已知晓以上内容`,
              variant: 'base',
              disabled: false,
            },
            isToBottom: true,
          });
        } else {
          this.setData({
            isToBottom: true,
          });
        }
      } else {
        // 未滚动到底部，重置状态
        if (this.data.isToBottom) {
          this.setData({
            isToBottom: false,
          });
          // 如果倒计时已完成但未滚动到底部，禁用按钮
          if (this.data.isCountdown) {
            this.setData({
              confirmBtn: {
                content: `已知晓以上内容`,
                variant: 'base',
                disabled: true,
              },
            });
          }
        }
      }
    }, 50); // 50ms 防抖

  },

  /**
   * 页面卸载时清理定时器
   */
  onUnload() {
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
      this.scrollTimer = null;
    }
  },

  /**
   * 返回YYYYMMDDHHdd
   * @param {时间对象} date 
   */
  format: (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}${month}${day}${hours}${minutes}`;
  },
})