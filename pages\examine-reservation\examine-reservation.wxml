<!--pages/examine-reservation/examine-reservation.wxml-->
<view class="con">
  <view class="example-search">
    <t-search value="{{oddNumbers}}" placeholder="请输入单号搜索" action="{{'搜索'}}" bind:action-click="actionHandle" bind:change="changeHandle" bind:submit="actionHandle" bind:clear="clearValue" />
  </view>
  <view class="container">
    <view class="container-main {{dataList.length>0 ? '' : 'dataList-empty' }}">
        <view class="my-list" wx:for="{{dataList}}" wx:key="index" wx:for-item="item">
            <view class="list-name" catchtap="cheackList" data-index="{{index}}">
                <view class="list-name-checked">
                    <image src="/assets/image/icon_UncheckBox.png" class='cheacked-img' wx:if="{{!item.flag}}"></image>
                    <image src="/assets/image/icon_checkBox.png" class='cheacked-img' wx:if="{{item.flag}}"></image>
                </view> 
                <view class="list-name-number">{{item.reservationNumber}}</view>
            </view>
            <!-- <view class="my-list-li">
                <view class="my-list-li-name">预约单号：</view>
                <view class="my-list-li-value">{{item.reservationNumber}}</view>
        </view> -->
        <view class="my-list-li">
                <view class="my-list-li-name">车牌号：</view>
                <view class="my-list-li-value">{{item.vehicleNo}}</view>
        </view>
        <view class="my-list-li">
                <view class="my-list-li-name">司机：</view>
                <view class="my-list-li-value">{{item.driverName}}</view>
        </view>
        <view class="my-list-li">
                <view class="my-list-li-name">手机号：</view>
                <view class="my-list-li-value">{{item.driverTel}}</view>
        </view>
        <view class="my-list-li">
                <view class="my-list-li-name">身份证：</view>
                <view class="my-list-li-value">{{item.driverIdentity}}</view>
        </view>
        <view class="my-list-li">
                <view class="my-list-li-name">预约时段：</view>
                <view class="my-list-li-value">{{item.reservationTime}}</view>
        </view>
        <view class="my-list-li">
                <view class="my-list-li-name">预约日期：</view>
                <view class="my-list-li-value">{{item.reservationDate}}</view>
        </view>
        <view class="my-list-li">
                <view class="my-list-li-name">承运商：</view>
                <view class="my-list-li-value">{{item.customerName}}</view>
        </view>
        <view class="my-list-li">
                <view class="my-list-li-name">业务类型：</view>
                <view class="my-list-li-value">{{item.handTypeName}}</view>
        </view>
        </view>
        <view wx:if="{{!dataList.length>0}}">
            <image src="/assets/image/empty_data.png" class="my-order-img"></image>
            <view class="my-order-text">暂无数据</view>
        </view>
    </view>
  </view>
  <view class="reservation-order" bindtap="cheackOrder">预约单审批</view>
</view>
