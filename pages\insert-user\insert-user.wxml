<view class="container">
  <t-cell class="mb-16" t-class-note="app-t-class" title="选择身份" arrow hover note="{{identity}}" bind:click="onShowIdentity" />
  <t-picker visible="{{identityVisible}}" value="{{identityValue}}" data-key="identity" title="选择身份" cancelBtn="取消" confirmBtn="确认" usingCustomNavbar bindchange="onPickerChange">
    <t-picker-item options="{{identityList}}" />
  </t-picker>

  <!-- 为司机选择委托运输 -->
  <view wx:if="{{identityValue[0] == '30'}}">
    <t-cell class="mb-16" t-class-note="app-t-class" title="选择委托方" arrow hover note="{{entrust}}" bind:click="onShowEntrust" />
    <t-picker visible="{{entrustVisible}}" value="{{entrustValue}}" data-key="entrust" title="选择身份" cancelBtn="取消" confirmBtn="确认" usingCustomNavbar bindchange="onPickerChange">
      <t-picker-item options="{{entrustList}}" />
    </t-picker>
  </view>

  <t-cell class="mb-16" t-class-note="app-t-class" title="所属公司" arrow hover note="{{belongCompanyValue}}" bind:click="onShowBelongCompany" />
  <t-cell class="mb-16" t-class-note="app-t-class company-class" t-class-title="app-title" title="公司名称" note="{{belongCompany}}" />
  <!-- 搜索所属公司-->
  <t-popup visible="{{belongCompanyQueryVisible}}" usingCustomNavbar placement="bottom" style="height: 580rpx;">
    <view class="aaa">
      <view class="example-search">
        <t-search placeholder="搜索公司" bind:change="belongCompanyQueryChange" bind:submit="belongSumbit" bind:clear="belongClear" value="{{belongCompanyQuery}}" />
      </view>
      <view class="example-picker">
        <t-picker auto-close="{{false}}" visible="{{belongCompanyVisible}}" value="{{belongCompanyValue}}" data-key="belongCompany" title="选择公司" cancelBtn="取消" confirmBtn="确认" usingCustomNavbar bindchange="onPickerChange" bindcancel="belongCompanyCanCel">
          <t-picker-item options="{{belongCompanyList}}" />
        </t-picker>
      </view>
    </view>
  </t-popup>

  <t-input label="姓名" placeholder="请输入姓名" align="right" data-key="name" value="{{name}}" bindchange="onInputChang"></t-input>

  <t-input label="手机号" placeholder="请输入手机号" align="right" data-key="tel" value="{{tel}}" bindchange="onInputChang"></t-input>

  <t-input label="身份证号" placeholder="请输入身份证号" align="right" data-key="idCard" value="{{idCard}}" bindchange="onInputChang"></t-input>

  <view wx:if="{{identityValue[0] == '30'}}">
    <car-plate-input activeIndex="{{activeIndex}}" codeArray="{{['', '', '', '', '', '', '', '新能源']}}" data-key="car-number-div" bind:inputcomplete="onInputComplete"></car-plate-input>
  </view>

  <t-input placeholder="输入验证码" value="{{code}}" type="number" data-key="code" bindchange="onInputChang" maxlength="6">
    <t-button slot="suffix" theme="primary" size="extra-small" bind:tap="sendCode" variant="outline"> 发送验证码 </t-button>
  </t-input>

  <view class="btn-group">
    <t-button theme="primary" block bind:tap="insert">注册</t-button>
  </view>

  <t-dialog visible="{{showTextAndTitle}}" title="提示" content="{{content}}" confirm-btn="{{ confirmBtn }}" bind:confirm="closeDialog" />

  <t-dialog visible="{{showMsg}}" title="通知" content="{{msgContent}}" confirm-btn="{{ confirmMsgBtn }}" bind:confirm="closeMsgDialog" />

  <t-toast id="t-toast" />
</view>