
.t-input {
  background-color: transparent !important;
}

.main {
  background: transparent;
  display: flex;
  flex-direction: column;
  /* 垂直排列子元素 */
  align-items: center;
  /* 水平居中对齐 */
  justify-content: center;
  /* 垂直居中对齐 */
  width: 100%;
  /* 宽度占满 */
  margin-top: 50%;
  box-sizing: border-box;
}

.main-input {
  width: 80%;
  /* 设置输入框宽度 */
  max-width: 400px;
  /* 限制最大宽度 */
}

.suffix--line {
  width: 1px;
  height: 24px;
  background-color: #ccc;
  margin-right: 8px;
  /* 与“选择账套”之间的间距 */
}

.verify {
  color: #666;
  /* 与输入框内的字体颜色保持一致 */
  font-size: 16px;
}

.button-example {
  width: 80%;
  /* 设置按钮宽度 */
  max-width: 400px;
  /* 限制最大宽度 */
  margin-top: 20px;
  /* 按钮与输入框之间的间距 */
}

.overlay-view {
  position: fixed;
  /* 固定定位 */
  top: 50%;
  /* 使view的顶部居中 */
  left: 50%;
  /* 使view的左边居中 */
  transform: translate(-50%, -50%);
  /* 通过位移调整到完全居中 */
  background-color: rgba(0, 0, 0, 0.6);
  /* 设置背景色为黑色并带有透明度 */
  border-radius: 12rpx;
  /* 设置圆角 */
  padding: 20rpx;
  /* 内边距，可以根据需求调整 */
  z-index: 999;
  /* 确保在最前 */
}