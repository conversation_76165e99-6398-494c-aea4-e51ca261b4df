// pages/my-waste-material/my-waste-material.js
import Message from 'tdesign-miniprogram/message/index';
import { Toast } from 'tdesign-miniprogram';
const $api = require('../../api/request');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    wasteMaterialList: [], // 废料提货车辆列表
    showTextAndTitle: false,
    content: '',
    confirmBtn: { content: '确定', variant: 'base' },
    value: '',
    userInfo: '',
    // 状态筛选相关
    statusFlags: ['20'], // 状态筛选数组，默认显示生效状态
    status1Active: true, // 生效状态是否激活
    status0Active: false, // 完成状态是否激活
    // 装卸点选择相关
    loadingPointList: [], // 装卸点列表
    loadingPointVisible: false, // 装卸点选择框显示状态
    selectedLoadingPointValue: [], // 选中的装卸点值
    selectedPointNames: [], // 选中的装卸点名称
    currentOperationIndex: -1, // 当前操作的车辆索引
    currentVehicle: null, // 当前操作的车辆数据
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      userInfo: wx.getStorageSync('userInfo'),
      status1Active: this.data.statusFlags.includes('20'),
      status0Active: this.data.statusFlags.includes('99')
    });

    // 初始化防抖搜索
    this.debounceSearch = this.debounce(() => {
      this.getWasteMaterialList();
    }, 500);

    // 获取废料提货车辆数据
    this.getWasteMaterialList();
  },



  /**
   * 修改装卸点
   * @param {*} e 
   */
  editItem(e) {
    const index = e.currentTarget.dataset.index;
    const vehicle = this.data.wasteMaterialList[index];
    
    // 获取该车辆当前的装卸点信息，用于预选
    const currentLoadingPoints = vehicle.handPointList || [];
    const currentValues = currentLoadingPoints.map(point => point.targetHandPointId);
    
    this.setData({
      currentOperationIndex: index,
      selectedLoadingPointValue: currentValues,
    });
    
    // 获取装卸点列表后显示对话框
    this.getLoadingPointList().then(() => {
      
      this.setData({
        loadingPointVisible: true
      });
      // 更新选中的装卸点名称显示
      this.updateSelectedPointNames();
    }).catch((err) => {
      // 获取装卸点列表失败时的处理
      this.setData({
        currentOperationIndex: -1,
        selectedLoadingPointValue: [],
        currentVehicle: null,
      });
    });
  },

  /**
   * 启动排队
   * @param {*} e 
   */
  startQueue(e) {
    const vehicle = e.currentTarget.dataset.item;
    
    this.setData({
      currentVehicle: vehicle,
    });
    
    // 启动排队功能
    this.getAddressT();
  },

  /**
   * 取消排队
   * @param {*} e 
   */
  cancelQueue(e) {
    const vehicle = e.currentTarget.dataset.item;
    
    $api.request('S_LI_RL_0151', '', vehicle).then((res) => {
      Toast({
        context: this,
        selector: '#t-toast',
        message: res.__sys__.msg,
      });
      this.getWasteMaterialList();
    }).catch((err) => {
      // 接口已处理错误提示，无需额外提示
    });
  },

  /**
   * 检查并申请定位权限
   */
  checkLocationPermission() {
    return new Promise((resolve, reject) => {
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.userLocation'] === false) {
            // 用户曾经拒绝过，需要引导用户手动开启
            wx.showModal({
              title: '需要位置权限',
              content: '为了准确获取您的位置信息，请在设置中开启位置权限，设置完成后请重新点击启动排队',
              showCancel: true,
              cancelText: '取消',
              confirmText: '去设置',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  wx.openSetting({
                    success: (settingRes) => {
                      if (settingRes.authSetting['scope.userLocation']) {
                      } else {
                        reject('用户未开启位置权限');
                      }
                    },
                    fail: () => {
                      reject('打开设置失败');
                    }
                  });
                } else {
                  reject('用户取消授权');
                }
              }
            });
          } else if (res.authSetting['scope.userLocation'] === undefined) {
            // 用户未授权过，直接调用授权
            resolve();
          } else {
            // 用户已授权
            resolve();
          }
        },
        fail: () => {
          reject('获取授权设置失败');
        }
      });
    });
  },

  /**
   * 启动排队 - 获取位置信息
   */
  getAddressT() {
    let that = this;
    
    // 先检查权限
    this.checkLocationPermission().then(() => {
      wx.showLoading({
        title: '正在定位中',
        mask: true
      });
      wx.getLocation({
        type: 'wgs84', //wgs84 返回 gps 坐标，gcj02 返回国测局坐标
        altitude: false, //传入 true 会返回高度信息，由于获取高度需要较高精确度，会减慢接
        isHighAccuracy: true, //开启高精度定位，提高定位精度
        highAccuracyExpireTime: 10000, //高精度定位超时时间(ms)，延长到10秒提高定位准确性
        success(res) {
          //隐藏提示框
          wx.hideLoading();
          let location = {};
          location.horizontalAccuracy = res.horizontalAccuracy;
          location.latitude = parseFloat(res.latitude);
          location.longitude = parseFloat(res.longitude);
          that.startQueuing(location);
        },
        fail: (res) => {
          wx.hideLoading();
          
          let content = "";
          // 检查是否是权限问题
          if (res.errMsg && (res.errMsg.includes('auth deny') || res.errMsg.includes('permission'))) {
            content = "定位权限未开启，请按以下步骤操作：\n1. 点击右上角菜单\n2. 选择\"设置\"\n3. 开启\"位置信息\"权限\n4. 返回重新尝试";
          } else if (res.errMsg && res.errMsg.includes('location fail')) {
            content = "定位服务不可用，请检查：\n1. 手机定位服务是否开启\n2. 网络连接是否正常\n3. 是否在室外开阔区域";
          } else {
            content = "定位失败：" + res.errMsg + "\n请检查GPS权限和网络连接";
          }
          
          Toast({
            context: that,
            selector: '#t-toast',
            message: content,
          });
          that.setData({
            currentVehicle: null,
          });
        }
      });
    }).catch((error) => {
      Toast({
        context: this,
        selector: '#t-toast',
        message: error || '获取定位权限失败',
      });
      this.setData({
        currentVehicle: null,
      });
    });
  },

  /**
   * 开始启动排队
   * @param {*} location 位置信息
   */
  startQueuing(location) {
    const vehicle = this.data.currentVehicle;
    
    var data = {
      "result": [{
        ...vehicle,
        "latitude": location.latitude,
        "longitude": location.longitude,
        "horizontalAccuracy": location.horizontalAccuracy
      }],
      driverTel: this.data.userInfo.tel,
      isNotShowToast: true,
    }
    
    $api.request('S_LI_RL_0067', '', data).then((res) => {
      Toast({
        context: this,
        selector: '#t-toast',
        message: res.__sys__.msg,
      });
      this.setData({
        currentVehicle: null,
      });
      this.getWasteMaterialList();
    }).catch((err) => {
      Toast({
        context: this,
        selector: '#t-toast',
        message: err.data?.__sys__?.msg || err.__sys__?.msg || '启动排队失败',
      });
      // 接口已处理错误提示，无需额外提示
      this.setData({
        currentVehicle: null,
      });
    });
  },

  /**
   * 搜索按钮点击
   */
  actionHandle() {
    this.getWasteMaterialList();
  },

  /**
   * 切换状态筛选（多选）
   * @param {*} e 
   */
  toggleStatus(e) {
    const status = e.currentTarget.dataset.status;
    let statusFlags = [...this.data.statusFlags];
    
    if (statusFlags.includes(status)) {
      // 如果已选中，则取消选中（但至少保留一个）
      if (statusFlags.length > 1) {
        statusFlags = statusFlags.filter(flag => flag !== status);
      }
    } else {
      // 如果未选中，则添加到选中列表
      statusFlags.push(status);
    }
    
    this.setData({
      statusFlags: statusFlags,
      status1Active: statusFlags.includes('20'),
      status0Active: statusFlags.includes('99'),
      wasteMaterialList: [] // 清空当前数据
    });
    
    // 重新加载数据（保持搜索条件）
    this.getWasteMaterialList();
  },



  /**
   * 跳转到详情页面
   * @param {*} e 
   */
  toInfo(e) {
    const index = e.currentTarget.dataset.index;
    const data = this.data.wasteMaterialList[index];
    
    // 跳转到装卸点详情页面
    wx.navigateTo({
      url: '/pages/waste-material-info/waste-material-info',
      events: {
        acceptDataFromOpenedPage: () => { },
      },
      success: (res) => {
        res.eventChannel.emit('acceptDataFromOpenerPage', { data, })
      }
    });
  },

  /**
   * 获取装卸点列表
   */
  getLoadingPointList() {
    const { userInfo } = this.data;
    
    return $api.request('S_LI_RL_0158', '', {
      segNo: userInfo.segNo,
    }).then((res) => {
      
      if (res.list && res.list.length > 0) {
        const pointOptions = res.list.map(point => ({
          label: point.handPointName,
          value: point.handPointId,
          ...point
        }));
        
        this.setData({
          loadingPointList: pointOptions
        });
        
      } else {
        this.setData({
          loadingPointList: []
        });
      }
    }).catch((err) => {
      Toast({
        context: this,
        selector: '#t-toast',
        message: "获取装卸点列表失败",
      });
      throw err; // 重新抛出错误，让调用者知道失败了
    });
  },

  /**
   * 装卸点多选变更
   */
  onLoadingPointChange(e) {
    const selectedValues = e.detail.value;
    this.setData({
      selectedLoadingPointValue: selectedValues,
    });
    
    // 更新选中的装卸点名称显示
    this.updateSelectedPointNames();
  },

  /**
   * 更新选中的装卸点名称显示
   */
  updateSelectedPointNames() {
    const { loadingPointList, selectedLoadingPointValue } = this.data;
    
    // 获取选中的装卸点信息
    const selectedPoints = loadingPointList.filter(point => 
      selectedLoadingPointValue.includes(point.value)
    );
    
    const selectedNames = selectedPoints.map(point => point.label);
    
    this.setData({
      selectedPointNames: selectedNames,
    });
  },

  /**
   * 确认装卸点选择
   */
  confirmLoadingPoints() {
    const { currentOperationIndex, selectedLoadingPointValue, wasteMaterialList } = this.data;
    
    if (currentOperationIndex === -1) {
      this.setData({
        loadingPointVisible: false
      });
      return;
    }

    const vehicle = wasteMaterialList[currentOperationIndex];
    
    // 修改装卸点
    this.modifyLoadingPoint(vehicle, selectedLoadingPointValue);
  },

  /**
   * 修改装卸点
   */
  modifyLoadingPoint(vehicle, selectedLoadingPointValue) {
    const user = wx.getStorageSync('userInfo');
    const { loadingPointList } = this.data;
    
    // 获取选中的装卸点详细信息
    const selectedPoints = loadingPointList.filter(point => 
      selectedLoadingPointValue.includes(point.value)
    );

    wx.showLoading({
      title: '修改中',
    });

    $api.request('S_LI_RL_01592', '', {
      vehicleNo: vehicle.vehicleNo,
      handPoint: selectedPoints,
      segNo: user.segNo,
      driverTel: user.tel,
      recRevisor: user.tel,
      driverName: user.driverName,
      reservationNumber: user.reservationNumber,
    }).then((res) => {
      wx.hideLoading();
      
      Toast({
        context: this,
        selector: '#t-toast',
        message: "修改成功",
      });
      
      this.setData({
        loadingPointVisible: false,
        currentOperationIndex: -1,
        selectedLoadingPointValue: [],
        selectedPointNames: [],
        currentVehicle: null,
      });
      
      // 刷新数据
      this.getWasteMaterialList();
    }).catch((err) => {
      wx.hideLoading();
      Toast({
        context: this,
        selector: '#t-toast',
        message: "修改失败，请稍后重试",
      });
    });
  },



  /**
   * 取消装卸点选择
   */
  cancelLoadingPoints() {
    this.setData({
      loadingPointVisible: false,
      currentOperationIndex: -1,
      selectedLoadingPointValue: [],
      selectedPointNames: [],
      currentVehicle: null,
    });
  },

  /**
   * 关闭对话框
   */
  closeDialog() {
    this.setData({
      showTextAndTitle: false,
      currentVehicle: null,
    });
  },

  /**
   * 搜索框输入改变
   * @param {*} e 
   */
  changeHandle(e) {
    const { value } = e.detail;
    this.setData({
      value,
    });
    // 实时搜索（防抖）
    this.debounceSearch();
  },



  /**
   * 清空搜索框
   */
  clearValue() {
    this.setData({
      value: '',
    });
    this.getWasteMaterialList();
  },

  /**
   * 获取废料提货车辆列表
   */
  getWasteMaterialList() {
    const user = wx.getStorageSync('userInfo');
    const { value } = this.data;

    wx.showLoading({
      title: '加载中',
    });

    $api.request('S_LI_RL_01591', '', {
      tel: user.tel,
      name: user.driverName,
      segNo: user.segNo,
      searchKeyword: value,
      statusFlags: this.data.statusFlags, // 添加状态筛选参数
    }).then((res) => {
      wx.hideLoading();
      
      if (res.list && res.list.length > 0) {
        const processedList = this.processListData(res.list);
        this.setData({
          wasteMaterialList: processedList,
        });
      } else {
        this.setData({
          wasteMaterialList: [],
        });
      }
    }).catch((err) => {
      wx.hideLoading();
      Message.error({
        context: this,
        offset: [90, 32],
        duration: 3000,
        content: '获取数据失败，请稍后重试',
      });
    });
  },



  /**
   * 处理列表数据
   * @param {*} list 
   */
  processListData(list) {
    return list.map(item => ({
      ...item,
      dateStr: this.formatDate(item.recCreateTime),
      // 确保状态字段存在，默认为生效状态
      status: item.status || '20',
      // 处理装卸点列表显示
      handPointsDisplay: this.getHandPointsDisplay(item.handPointList || []),
      // 保存装卸点列表用于操作
      loadingPoints: item.handPointList || [],
    }));
  },

  /**
   * 获取装卸点显示文本
   * @param {*} handPointList 
   */
  getHandPointsDisplay(handPointList) {
    if (!handPointList || handPointList.length === 0) {
      return '暂无';
    }
    
    // 显示所有装卸点名称，用"、"分隔
    return handPointList.map(point => point.handPointName).join('、');
  },

  /**
   * 格式化日期
   * @param {*} date 
   */
  formatDate(date) {
    if (!date) return '';
    
    // 处理格式：20250712141138 -> 2025-07-12 14:11:38
    const dateStr = String(date);
    if (dateStr.length === 14) {
      const year = dateStr.substring(0, 4);
      const month = dateStr.substring(4, 6);
      const day = dateStr.substring(6, 8);
      const hour = dateStr.substring(8, 10);
      const minute = dateStr.substring(10, 12);
      const second = dateStr.substring(12, 14);
      
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    }
    
    // 兼容其他格式
    const d = new Date(date);
    return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
  },



  /**
   * 防抖函数
   * @param {*} func 
   * @param {*} delay 
   */
  debounce(func, delay = 500) {
    let timeoutId;
    return (...args) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
  },

  /**
   * 页面显示
   */
  onShow() {
    // 如果从其他页面返回，刷新数据
    if (this.data.wasteMaterialList.length > 0) {
      this.getWasteMaterialList();
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.getWasteMaterialList();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 不需要分页，这里暂时不处理
  },
}); 