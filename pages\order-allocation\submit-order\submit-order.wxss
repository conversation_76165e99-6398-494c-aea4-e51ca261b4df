/* pages/order-allocation/submit-order/submit-order.wxss */
.container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
    box-sizing: border-box;
  }
  
  .form-group {
    margin-bottom: 16px;
  }
  
  .form-group t-input {
    margin-bottom: 16px;
  }
  
  .verification-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
  }
  
  .verification-group t-input {
    flex-grow: 1;
    margin-right: 8px;
  }
  
  .btn-group t-button {
    margin-top: 16px;
  }

/* 车牌号选择器样式 */
.car-selector {
  position: relative;
}

.car-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  overflow: hidden;
}

.car-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

.search-box {
  padding: 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.search-input {
  background: white !important;
  border: 2px solid #e3f2fd !important;
  border-radius: 25px !important;
  padding-left: 20px !important;
  height: 44px !important;
  font-size: 16px !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
}

.search-input:focus {
  border-color: #2196f3 !important;
  box-shadow: 0 4px 16px rgba(33, 150, 243, 0.2) !important;
}

.car-list {
  max-height: 280px;
  overflow-y: auto;
  background: white;
}

.car-item {
  padding: 18px 24px;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  font-size: 16px;
  color: #333;
}

.car-plate {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.driver-name {
  font-size: 14px;
  color: #666;
  font-weight: 400;
}

.car-item:hover {
  background-color: #f8f9fa;
}

.car-item:last-child {
  border-bottom: none;
}

.car-item.selected {
  background: linear-gradient(90deg, #e3f2fd 0%, #bbdefb 100%);
}

.car-item.selected .car-plate {
  color: #1976d2;
  font-weight: 700;
}

.car-item.selected .driver-name {
  color: #1976d2;
  font-weight: 500;
}

.car-item.selected::after {
  content: "✓";
  position: absolute;
  right: 24px;
  color: #1976d2;
  font-weight: bold;
  font-size: 18px;
}

.no-data {
  padding: 40px 24px;
  text-align: center;
  color: #999;
  font-size: 15px;
  background: white;
}

.dropdown-actions {
  display: flex;
  border-top: 1px solid #eee;
  background: #fafafa;
}

.btn-cancel,
.btn-confirm {
  flex: 1;
  padding: 18px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.btn-cancel {
  color: #666;
  border-right: 1px solid #eee;
}

.btn-cancel:hover {
  background-color: #f0f0f0;
  color: #333;
}

.btn-confirm {
  color: #2196f3;
  font-weight: 600;
}

.btn-confirm:hover {
  background: linear-gradient(90deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1976d2;
}
  
  