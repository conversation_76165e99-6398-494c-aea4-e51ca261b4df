/* pages/examine-reservation/examine-reservation.wxss */
.con {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.example-search {
  height: 82rpx;
  background-color: var(--bg-color-demo);
  padding: 16rpx 32rpx;
  flex-shrink: 0;
}

.container {
  height: calc(100vh - 182rpx);
  overflow: hidden;
}

.container-main {
  width: 100%;
  height: 100%;
  padding: 0 32rpx;
  box-sizing: border-box;
  overflow: auto;
}

.my-list {
  width: 100%;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  margin: 0 auto 20rpx;
  border-radius: 20rpx;
  box-sizing: border-box;
  font-size: 16px;
  line-height: 1.5;
}

/* .my-list-li{
    display: flex;
    margin-bottom:5px;
} */
/* 卡片内部每一项的容器，使用flex布局 */
.my-list-li {
  display: flex;
  justify-content: space-between;
  /* 左右对齐 */
  padding: 8px 0;
  /* 每一项的上下内边距 */
  border-bottom: 1px solid #eaeaea;
  /* 每一项之间的分割线 */
}

/* 最后一项不需要分割线 */
.my-list-li:last-child {
  border-bottom: none;
}

.list-name {
  display: flex;
  flex-direction: row;
  align-items: center;
  align-content: center;
  margin-bottom: 10rpx;
}

.list-name-checked image {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
}

.list-name-number {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
  margin-left: 10rpx;
}

/* 左边标签的样式 */
.my-list-li-name {
  font-size: 16px;
  /* 字体大小 */
  color: #333;
  /* 字体颜色 */
}

/* 右边值的样式 */
.my-list-li-value {
  font-size: 16px;
  /* 字体大小 */
  color: #666;
  /* 字体颜色 */
  text-align: right;
  /* 右对齐 */
  max-width: 70%;
  /* 右侧文本最长占比，避免溢出 */
}

/* .my-list-li-name {
  width: 160rpx;
  color: #666666;
  flex-shrink: 0;
  text-align: justify;
  text-align-last: justify;
}

.my-list-li-value {
  color: #333333;
  flex: 1;
} */

.dataList-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.my-order-img {
  width: 150px;
  height: 150px;
  display: block;
  margin-bottom: 20rpx;
}

.my-order-text {
  text-align: center;
  font-size: 16px;
  color: rgb(51, 51, 51);
}

.reservation-order {
  width: calc(100% - 64rpx);
  margin: 0 auto;
  height: 64rpx;
  background-color: #1953E6;
  color: #ffffff;
  text-align: center;
  line-height: 64rpx;
  margin: 18rpx auto;
  font-size: 28rpx;
  border-radius: 8rpx;
}