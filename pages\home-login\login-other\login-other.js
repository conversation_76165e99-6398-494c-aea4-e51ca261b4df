// pages/home-login/login-other/login-other.js
import Message from 'tdesign-miniprogram/message/index';
Page({
  /**
   * 页面的初始数据
   */
  data: {
    phoneError: '',
    phoneNumber: '', // 16639826830 18917352213 15194531930  13605510104 17771833409
    //正式：18875556067(肖美琴)  19123576007(单有伟)
    segNo: "选择账套",
    segNos: [],
    segNoVisible: false,
    segNoValue: '',
    showTextAndTitle: false,
    dialogContent: '',
    confirmBtn: { content: '确定', variant: 'base' },
    skylineRender: false,
    overlayVisible: false,
    cityVisible: false,
    cityValue: '',
    citys: [],
    userList: [],
    code: '',
    showTemporaryReservation: false, // 控制是否显示临时预约按钮
  },

  /**
 * 生命周期函数--监听页面加载
 */
  onLoad(options) {
    this.getSegNoList();
  },

  /**
 * 发送验证码
 */
  sendCode() {
    const isPhoneNumber = /^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(this.data.phoneNumber);
    if (!isPhoneNumber) {
      this.setData({
        dialogContent: '请输入正确的手机号',
        showTextAndTitle: true,
        showTemporaryReservation: false,
      });
      return;
    }

    const app = getApp();
    wx.showLoading({
      title: '发送中',
    });
    wx.request({
      url: app.mesUrl, // 接口地址
      method: 'POST', // 请求方法
      data: {
        serviceId: 'S_LI_RL_0017',
        driverTel: this.data.phoneNumber,
      },
      success: (res) => {
        wx.hideLoading();

        if (!res || !res.data || !res.data.content) {
          this.setData({
            dialogContent: `验证码发送失败！`,
            showTextAndTitle: true,
            showTemporaryReservation: false,
          });
          return;
        }

        Message.success({
          context: this,
          offset: [90, 32],
          duration: 3000,
          content: '验证码发送成功!',
        });
      },
      fail: () => {
        this.setData({
          overlayVisible: false
        });
        this.setData({
          dialogContent: `请求失败，请检查网络`,
          showTextAndTitle: true,
          showTemporaryReservation: false,
        });
      },
    });
  },

  /**
   * 登录验证
   */
  phoneLogin() {
    // const isPhoneNumber = /^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(this.data.phoneNumber);
    // if (!isPhoneNumber) {
    //   this.setData({
    //     dialogContent: '请输入正确的手机号',
    //     showTextAndTitle: true,
    //     showTemporaryReservation: false,
    //   });
    //   return;
    // }

    // if (!this.data.code) {
    //   this.setData({
    //     dialogContent: '请输入填写验证码',
    //     showTextAndTitle: true,
    //     showTemporaryReservation: false,
    //   });
    //   return;
    // }

    // if (!this.data.segNoValue) {
    //   this.setData({
    //     dialogContent: '请选择账套',
    //     showTextAndTitle: true,
    //     showTemporaryReservation: false,
    //   });
    //   return;
    // }

    // this.setData({
    //   overlayVisible: true
    // });

    const app = getApp();

    // wx.request({
    //   url: app.mesUrl, // 接口地址
    //   method: 'POST', // 请求方法
    //   data: {
    //     serviceId: 'S_LI_RL_0066', // 验证验证码的接口ID
    //     driverTel: this.data.phoneNumber,
    //     messageCode: this.data.code, // 用户输入的验证码
    //   },
    //   success: (res) => {
    //     this.setData({
    //       overlayVisible: false
    //     });

    //     if (res && res.data.__sys__ && res.data.__sys__.status == 1) {

          // 登录
          wx.request({
            url: app.mesUrl, // 接口地址
            method: 'POST', // 请求方法
            data: {
              phoneNum: this.data.phoneNumber, // 请求体内容
              segNo: this.data.segNoValue,
              serviceId: 'S_LI_RL_0024',
            },
            success: (res) => {
              this.setData({
                overlayVisible: false
              });

              if (res?.data?.__sys__?.status != -1) {
                const { data } = res;
                wx.setStorageSync('userLoggedIn', true);  // 记录用户已登录
                const resultList = data.list.map(m => {
                  m.eiMetadata = "";
                  return m;
                });

                // 存储完整的响应数据，包含 lease 字段
                wx.setStorage({
                  key: 'loginResponseData',
                  data: data
                });

                wx.setStorage({
                  key: 'auditList',
                  data: data.auditList
                });
                this.setData({
                  userList: resultList,
                });
                wx.setStorage({
                  key: "userInfoList",
                  data: resultList,
                });
                if (resultList.length == 1) {
                  let resultData = resultList[0];
                  wx.setStorage({
                    key: "userInfo",
                    data: resultData
                  });
                  
                  // 检查 lease 字段，如果是 "30" 则跳转到常驻车页面
                  if (data.lease === "30") {
                    wx.reLaunch({
                      url: '/pages/resident-car/resident-car',
                    });
                  } else {
                    wx.reLaunch({
                      url: '/pages/index/index',
                    });
                  }
                  return;
                }
                // 弹框让用户自行选择
                const sfList = resultList.map(r => {
                  let rIdName = app.getUserName(r.reservationIdentity);
                  return { label: rIdName, value: r.reservationIdentity };
                });
                const uniqueArray = sfList.filter((item, index, self) =>
                  index === self.findIndex((t) => t.label === item.label && t.value === item.value)
                );
                
                // 将司机身份排到第一位
                const sortedArray = app.sortRoleList(uniqueArray);
                
                this.setData({
                  cityVisible: true,
                  citys: sortedArray,
                });
                wx.setStorage({
                  key: "userInfoList",
                  data: resultList,
                });
                return;
              }

              // 处理返回的错误
              const errorMsg = res?.data?.__sys__?.msg || '';
              const showTemporaryBtn = errorMsg.includes('不存在');

              this.setData({
                dialogContent: errorMsg,
                showTextAndTitle: true,
                showTemporaryReservation: showTemporaryBtn,
              });
            },
            fail: () => {
              this.setData({
                overlayVisible: false
              });
              this.setData({
                dialogContent: `请求失败，请检查网络`,
                showTextAndTitle: true,
                showTemporaryReservation: false,
              });
            },
          });

    //       return;
    //     }

    //     Message.error({
    //       context: this,
    //       offset: [90, 32],
    //       duration: 5000,
    //       content: `验证码错误`,
    //     });
    //   },
    //   fail: () => {
    //     this.setData({
    //       overlayVisible: false
    //     });
    //     this.setData({
    //       dialogContent: `请求失败，请检查网络`,
    //       showTextAndTitle: true,
    //       showTemporaryReservation: false,
    //     });
    //   },
    // });
  },

  onPhoneInput(e) {
    const { value } = e.detail;
    this.setData({
      phoneNumber: value,
    })
  },

  onCodeInput(e) {
    const { value } = e.detail;
    this.setData({
      code: value,
    })
  },

  /**
   * 选择账套
   */
  selectSegNo() {
    this.setData({
      segNoVisible: true,
    });

  },

  onPickerChange(e) {
    const { value, label } = e.detail;
    this.setData({
      segNoVisible: false,
      segNoValue: value.join(''),
      segNo: label,
    });
  },

  getSegNoList() {
    wx.showLoading({
      title: '加载中',
    })
    const app = getApp();
    wx.request({
      url: app.mesUrl, // 接口地址
      method: 'POST', // 请求方法
      data: {
        serviceId: 'S_LI_RL_0023',
      },
      success: (res) => {
        wx.hideLoading();
        if (!res.data || !res.data.list || res.data.list.length == 0) {
          this.setData({
            dialogContent: `请维护账套`,
            showTextAndTitle: true,
            showTemporaryReservation: false,
          });
        }

        const dataList = res.data.list.filter(d => d.segName.includes('重庆'));
        const resultData = dataList.map(l => {
          return {
            label: l.segName,
            value: l.segNo,
          };
        });
        this.setData({
          // segNoVisible: true,
          segNos: resultData,
        });
        // 默认重庆宝钢
        const anHuiData = resultData.find(f => f.value == 'JC000000');
        this.onPickerChange({
          detail: {
            value: [anHuiData.value],
            label: anHuiData.label
          },
        })
      },
      fail: (e) => {
        wx.hideLoading();
        this.setData({
          dialogContent: `请求失败，请检查网络`,
          showTextAndTitle: true,
          showTemporaryReservation: false,
        });
      },
    });
  },

  closeDialog() {
    this.setData({
      showTextAndTitle: false,
      showTemporaryReservation: false,
    });
  },

  // 处理临时预约按钮点击
  onTemporaryReservation() {
    this.setData({
      showTextAndTitle: false,
      showTemporaryReservation: false,
    });
    // 跳转到注册页面
    this.registration();
  },

  /**
   * 用户注册
   */
  registration() {
    wx.navigateTo({
      url: '/pages/tourist-reservation/tourist-reservation',
    });
  },

  onCityCancel() {
    this.setData({ cityVisible: false });
  },

  onCityChange(e) {
    let { value } = e.detail;

    const userInfo = this.data.userList.find(u => u.reservationIdentity == value.join(''));
    if (!userInfo) {
      return;
    }

    wx.setStorage({
      key: "userInfo",
      data: userInfo
    });
    
    // 检查存储的响应数据中的 lease 字段
    const responseData = wx.getStorageSync('loginResponseData');
    if (responseData && responseData.lease === "30") {
      wx.reLaunch({
        url: '/pages/resident-car/resident-car',
      });
    } else {
      wx.reLaunch({
        url: '/pages/index/index',
      });
    }
  }
})