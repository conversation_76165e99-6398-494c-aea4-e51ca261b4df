<!--pages/order-allocation/submit-order/submit-order.wxml-->
<view class="container">
  <view class="form-group">
    <!-- 车牌号选择 - 改为自定义带搜索的下拉框 -->
    <view class="car-selector">
      <t-cell class="mb-16" title="车牌号" arrow hover note="{{carTitle}}" bind:click="selectCar" t-class-note="app-t-class" />
      
      <!-- 搜索框和下拉列表 -->
      <view class="car-dropdown {{carFlag ? 'show' : ''}}">
        <view class="search-box">
          <t-input placeholder="{{searchPlaceholder}}" value="{{searchKeyword}}" bindchange="onSearchCar" t-class-input="search-input" />
        </view>
        <view class="car-list">
          <view wx:for="{{filteredCarList}}" wx:key="value" 
                class="car-item {{selectedCarItem && selectedCarItem.value === item.value && selectedCarItem.driverName === item.driverName ? 'selected' : ''}}" 
                bind:tap="selectCarItem" 
                data-car="{{item}}">
            <view class="car-plate">{{item.label}}</view>
            <view class="driver-name">{{item.driverName}}</view>
          </view>
          <view wx:if="{{filteredCarList.length === 0}}" class="no-data">
            暂无匹配的车牌号或司机
          </view>
        </view>
        <view class="dropdown-actions">
          <view class="btn-cancel" bind:tap="cancelSelectCar">取消</view>
          <view class="btn-confirm" bind:tap="confirmSelectCar">确认</view>
        </view>
      </view>
      
      <!-- 遮罩层，点击关闭下拉框 -->
      <view wx:if="{{carFlag}}" class="dropdown-mask" bind:tap="cancelSelectCar"></view>
    </view>
    
    <t-input label="司机姓名" placeholder="请输入司机姓名" align="right" data-key="name" value="{{name}}" bindchange="onInputName" disabled t-class-input="app-t-class"></t-input>
    <t-input label="司机手机号" placeholder="请输入手机号" align="right" data-key="tel" value="{{tel}}" bindchange="onInputTel" disabled t-class-input="app-t-class"></t-input>
    <t-input label="身份证号" placeholder="请输入身份证号" align="right" data-key="idCard" value="{{idCard}}" bindchange="onInputId" disabled t-class-input="app-t-class"></t-input>
    <t-cell class="mb-16" title="预计到达时间" arrow hover note="{{arriveTime}}" bind:click="selectTime" t-class-note="app-t-class" />
    <t-date-time-picker title="选择日期和时间" visible="{{datetimeVisible}}" mode="hour" value="{{arrivedatetime}}" format="YYYY-MM-DD HH" bindchange="onConfirm" bindcancel="hidePicker" />

    <!-- <t-cell title="是否补单">
      <view slot="note">
        <t-radio-group default-value="0" borderless t-class="box" bind:change="onBringChange">
          <view style="margin-right: 22rpx;display: inline-block;">
            <t-radio block="{{false}}" label="是" value="10" />
          </view>
          <t-radio block="{{false}}" label="否" value="0" />
        </t-radio-group>
      </view>
    </t-cell> -->
  </view>

  <view class="btn-group">
    <t-button theme="primary" block bind:tap="insert">{{btnText}}</t-button>
  </view>

  <t-dialog visible="{{showTextAndTitle}}" title="提示" content="{{content}}" confirm-btn="{{ confirmBtn }}" bind:confirm="closeDialog" />

  <t-toast id="t-toast" />
</view>