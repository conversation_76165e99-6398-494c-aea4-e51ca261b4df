<!--pages/delivery-commission/delivery-commission-list/delivery-commission-list.wxml-->
<view>
  <view class="example-search">
    <t-search value="{{value}}" placeholder="提单号、转库单、终到站仓库" action="{{'搜索'}}" bind:action-click="actionHandle" bind:change="changeHandle" bind:submit="actionHandle" bind:clear="clearValue" />

    <!-- 将两个元素包裹在 flex 容器中 -->
    <view class="filter-container">
      <!-- 下拉菜单 (宽度自适应) -->
      <t-dropdown-menu class="dropdown-menu" style="background-color: transparent;position: initial;">
        <t-dropdown-item options="{{listType.options}}" value="{{listType.value}}" bindchange="onChange" />
      </t-dropdown-menu>

      <t-dropdown-menu wx:if="{{user.reservationIdentity != '40'}}" class="dropdown-menu" style="background-color: transparent;position: initial;">
        <t-dropdown-item options="{{customer.options}}" value="{{customer.value}}" bindchange="onCustomerChange" />
      </t-dropdown-menu>

      <!-- 筛选按钮 (靠右对齐) wx:if="{{user.reservationIdentity == '40'}}" -->
      <view class="filter-btn-wrapper">
        <t-button icon="filter" variant="text" bindtap="openFilter"></t-button>
      </view>
    </view>

    <!-- 抽屉组件 -->
    <t-drawer visible="{{filterVisible}}" placement="right" title="筛选条件" bind:close="closeFilter">
      <view class="filter-content">

        <t-cell class="mb-16" t-class-note="app-t-class" title="客户代码" arrow hover note="{{belongCompanyValue}}" bind:click="onShowBelongCompany" />
        <t-cell class="mb-16" t-class-note="app-t-class company-class" t-class-title="app-title" title="客户名称" note="{{belongCompany}}" />

        <t-cell class="mb-16" t-class-note="app-t-class" title="承运商代码" arrow hover note="{{carrierValue}}" bind:click="onShowCarrier" />
        <t-cell class="mb-16" t-class-note="app-t-class company-class" t-class-title="app-title" title="承运商名称" note="{{carrier}}" />

        <t-cell class="mb-16" t-class-note="app-t-class" title="提单客户代码" arrow hover note="{{billValue}}" bind:click="onShowBill" />
        <t-cell class="mb-16" t-class-note="app-t-class company-class" t-class-title="app-title" title="提单客户名称" note="{{bill}}" />

        <!-- 搜索所属公司-->
        <t-popup visible="{{belongCompanyQueryVisible}}" usingCustomNavbar placement="bottom" style="height: 580rpx;">
          <view class="aaa">
            <view class="example-search-popup">
              <t-search placeholder="搜索公司" bind:change="belongCompanyQueryChange" bind:submit="belongSumbit" bind:clear="belongClear" value="{{belongCompanyQuery}}" />
            </view>
            <view class="example-picker">
              <t-picker auto-close="{{false}}" visible="{{belongCompanyVisible}}" value="{{belongCompanyValue}}" data-key="belongCompany" title="选择公司" cancelBtn="取消" confirmBtn="确认" usingCustomNavbar bindchange="onPickerChange" bindcancel="belongCompanyCanCel">
                <t-picker-item options="{{belongCompanyList}}" />
              </t-picker>
            </view>
          </view>
        </t-popup>

      </view>

      <!-- 底部操作栏 -->
      <view slot="footer" class="filter-footer">
        <t-button theme="default" bindtap="resetFilter">重置</t-button>
        <t-button theme="primary" bindtap="confirmFilter">确认</t-button>
      </view>
    </t-drawer>

  </view>

  <scroll-view scroll-y style="height: calc(100vh - 380rpx);" bindscrolltolower="onReachBottom" scroll-top="{{scrollTop}}">
    <view wx:if="{{list.length > 0}}">
      <view class="card-main" data-index="{{index}}" wx:for="{{list}}" wx:key="index">
        <view wx:if="{{listType.value == '10'}}">
          <view class="card-item">
            <view class="label">提单号:</view>
            <view class="value">
              {{item.ladingBillId}}
              <t-tag class="margin-16" theme="primary">{{ item.deliveryType == '10' ? '自提' : '代运' }}</t-tag>
            </view>
          </view>
          <view class="card-item">
            <view class="label">总重量/总捆包数:</view>
            <view class="value">{{item.totalWeight}}/{{item.totalPackQtyNum}}</view>
          </view>
          <view class="card-item">
            <view class="label" style="min-width: 8em;">承运商/客户名称:</view>
            <view class="value">{{item.deliveryType == '10' ? item.userName : item.tproviderName}}</view>
          </view>
          <view class="card-item">
            <view class="label" style="min-width: 8em;">始发站仓库:</view>
            <view class="value">{{item.ladingSpotName}}</view>
          </view>
          <view class="card-item">
            <view class="label" style="min-width: 8em;">终到站仓库:</view>
            <view class="value">{{item.destSpotName}}</view>
          </view>
          <view class="card-item">
            <view class="label" style="min-width: 8em;">提单客户名称:</view>
            <view class="value">{{item.userName}}</view>
          </view>
          <view class="card-item">
            <view class="label" style="min-width: 8em;">提单创建人姓名:</view>
            <view class="value">{{item.recCreatorName}}</view>
          </view>
          <view class="card-item">
            <view class="label">司机姓名/电话:</view>
            <view class="value">{{item.driver}}/{{item.driverPhone}}</view>
          </view>
          <view class="card-item">
            <view class="label">车牌号:</view>
            <view class="value">{{item.vehicleNo}}</view>
          </view>
        </view>

        <!-- 转库单 -->
        <view wx:else>
          <view class="card-item">
            <view class="label">转库单号:</view>
            <view class="value">{{item.transBillId}}
              <t-tag class="margin-16" theme="primary">{{ item.deleiveryType == '10' ? '自提' : '代运' }}</t-tag>
            </view>
          </view>
          <view class="card-item">
            <view class="label">总重量/总捆包数:</view>
            <view class="value">{{item.sumNetWeight}}/{{item.sumNtotalQty}}</view>
          </view>
          <view class="card-item">
            <view class="label" style="min-width: 8em;">承运商/客户名称:</view>
            <view class="value">{{item.deleiveryType == '10' ? item.packSettleUserName : item.tproviderName}}</view>
          </view>
          <view class="card-item">
            <view class="label" style="min-width: 8em;">始发站仓库:</view>
            <view class="value">{{item.ladingSpotName}}</view>
          </view>
          <view class="card-item">
            <view class="label" style="min-width: 8em;">终到站仓库:</view>
            <view class="value">{{item.destSpotName}}</view>
          </view>
          <view class="card-item">
            <view class="label" style="min-width: 8em;">提单客户名称:</view>
            <view class="value">{{item.userName}}</view>
          </view>
          <view class="card-item">
            <view class="label" style="min-width: 8em;">提单创建人姓名:</view>
            <view class="value">{{item.recCreatorName}}</view>
          </view>
          <view class="card-item">
            <view class="label">司机姓名/电话:</view>
            <view class="value">{{item.driverName}}/{{item.driverPhone}}</view>
          </view>
          <view class="card-item">
            <view class="label">车牌号:</view>
            <view class="value">{{item.vehicleNo}}</view>
          </view>
        </view>

        <view class="icon-container">
          <t-button style="padding: 0;margin-right: 0;" size="large" theme="primary" variant="text" catchtap="editItem" data-index="{{index}}">修改</t-button>
        </view>
      </view>
    </view>

    <!-- 无数据提示 -->
    <view wx:else class="empty-container">
      <t-empty icon="file-image" description="暂无提货委托数据" />
    </view>

    <!-- 底部加载状态 -->
    <view class="pagination-status" wx:if="{{list.length > 0}}">
      <!-- <view class="divider"></view> -->
      <text wx:if="{{hasMore}}">加载更多...</text>
      <text wx:else>没有更多数据了</text>
    </view>
  </scroll-view>

  <t-dialog visible="{{showTextAndTitle}}" title="提示" content="{{content}}" confirm-btn="{{ confirmBtn }}" bind:confirm="closeDialog" />
</view>