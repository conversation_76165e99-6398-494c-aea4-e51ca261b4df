// pages/insert-reservation/insert-reservation.js
import Toast from 'tdesign-miniprogram/toast/index';
import Message from 'tdesign-miniprogram/message/index';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    customerCode: "请选择客户代码",
    customerCodeValue: "",
    customerCodeVisible: false,
    customerCodeList: [],
    customerName: "客户名称",
    // 筛选后的起始地
    addr: [],
    // 全部的起始地
    addrList: [],
    addrValue: '',
    addrVisible: false,
    startOrigin: '', // 运输起始地
    endOrigin: '',
    startOriginVisible: false,
    searchValue: '',
    name: '请选择司机',
    uuid: '',
    tel: '',
    driverIdentity: '',
    vehicleNoList: [],
    type: '请选择业务类型',
    driverList: [],
    typeVisible: false,
    typeValue: '',
    visitUnitList: [
      { label: '重庆宝钢', value: '重庆宝钢' },
      { label: '杭州宝伟', value: '杭州宝伟' },
    ],
    typeList: [], // 将从全局数据中获取
    siteType: '',
    dateVisible: false,
    date: '', // 日期选择器数据
    dateText: '请选择预约日期', // 预约日期显示数据
    // start: '2000-01-01 00:00:00',
    start: new Date().getTime(),
    end: '2300-01-01 00:00:00',
    periodVisible: false,
    periodList: [],
    period: '',
    periodValue: '',
    showTextAndTitle: false,
    content: '', // 提示信息
    title: '提示',
    confirmBtn: {
      content: '确定',
      variant: 'base'
    },
    currentUser: '',
    titleList: [],
    reservationIdentityName: '',
    clientHeight: 0, // 用于存储可视区域高度
    scrollH: 0,
    isToBottom: false, // 是否滚动到底部
    isCountdown: false, // 倒计时是否完成
    isDisableFormStart: false,
    isDisableFormEnd: false,
    searchText: '起始地',
    isEdit: false,
    reservationNumber: '',
    nameQueryVisible: false, // 检索司机
    nameQuery: '',
    confirmMsgBtn: {
      content: '知道了',
      variant: 'base'
    },
    showMsg: false,
    segNo: '',
    deadlineText: '请选择截止日期',
    deadDate: '',
    deadStart: '',
    deadEnd: '',
    deadVisible: '',
    isBringGood: 0,
    bringTextarea: '',
    visitUnit: '重庆宝钢',
    visitUnitVisible: false,
    visitUnitValue: '',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const app = getApp();
    const user = wx.getStorageSync('userInfo');
    this.setData({
      segNo: user.segNo,
      currentUser: user,
      typeList: app.globalData.typeList, // 从全局数据获取业务类型列表
    });
    const {
      reservationIdentity
    } = user;
    let userInfoList = wx.getStorageSync('userInfoList').filter(u => u.reservationIdentity == reservationIdentity && u.identityType == user.identityType);

    // 去重
    userInfoList = userInfoList.filter((item, index, self) =>
      index === self.findIndex((t) => t.customerId === item.customerId && t.customerName === item.customerName)
    );

    const list = userInfoList.map(m => {
      return {
        label: `${m.customerId}-${m.customerName}`,
        value: m.customerId,
      };
    });
    let rName = reservationIdentity == '10' ? '客户' : '承运商';
    switch (reservationIdentity) {
      case '10':
        rName = '客户';
        break;
      case '20':
        rName = '承运商';
        break;
      default:
        rName = '所属公司';
        break;
    }
    this.setData({
      customerCodeList: list,
      reservationIdentityName: rName,
      customerName: `${rName}名称`,
      customerCode: `请选择${rName}代码`,
    });
    if (userInfoList.length == 1) {
      const {
        customerId,
        customerName,
        segNo
      } = userInfoList[0];
      this.onPickerChange({
        detail: {
          label: [`${customerId}-${customerName}`],
          value: [customerId],
        },
        currentTarget: {
          dataset: {
            key: 'customerCode'
          },
        },
      });
      this.setData({
        segNo,
        currentUser: userInfoList[0],
      });
    }

    const eventChannel = this.getOpenerEventChannel();
    // 角色为司机直接默认
    if (user.reservationIdentity == '30' && !eventChannel) {
      this.onNamePicker();
    }

    if (!eventChannel) {
      return;
    }
    eventChannel.emit('acceptDataFromOpenedPage', {
      data: ''
    });
    eventChannel.on('acceptDataFromOpenerPage', (data) => {
      const resultData = data.data;
      // 客户代码选择
      this.onPickerChange({
        detail: {
          value: [resultData.customerId],
          label: [`${resultData.customerId}-${resultData.customerName}`],
        },
        currentTarget: {
          dataset: {
            key: 'customerCode'
          },
        }
      });
      this.getName(resultData.driverName, resultData.vehicleNo, resultData.driverTel);
      // 业务类型
      this.onPickerChange({
        detail: {
          value: [resultData.typeOfHandling],
          label: [resultData.typeName],
        },
        currentTarget: {
          dataset: {
            key: 'type'
          },
        }
      });

      this.setData({
        endOrigin: resultData.purposeOfTransport,
        startOrigin: resultData.startOfTransport,
        dateText: resultData.reservationDate,
        isEdit: true,
        reservationNumber: resultData.reservationNumber,
      });
      this.getPeriod(resultData.reservationTime);
      wx.setNavigationBarTitle({
        title: '修改预约'
      });
    });

  },

  onReady() {
    // 获取 scroll-view 的可视区域高度
    const query = wx.createSelectorQuery();
    query.select('#dialogScroll').boundingClientRect((rect) => {
      this.setData({
        clientHeight: rect.height // 存储可视区域高度
      });
    }).exec();
  },

  /**
   * 是否自带货单选按钮事件
   * @param {*} e 
   */
  onBringChange(e) {
    this.setData({
      isBringGood: e.detail.value
    });
  },

  /**
   * 自带货描述
   * @param {*} e 
   */
  onBringTextChange(e) {
    this.setData({
      bringTextarea: e.detail.value
    });
  },

  /**
   * 关闭错误提示框
   */
  closeDialog() {
    this.setData({
      showTextAndTitle: false,
      isToBottom: false, // 重置是否滚动到底部
      isCountdown: false, // 重置倒计时是否完成
    });
    const {
      titleList
    } = this.data;
    if (!titleList || titleList.length == 0) {
      return;
    }
    // 先删除掉第一个元素
    titleList.shift();
    if (titleList.length == 0) {
      this.insert();
      return;
    }
    this.openInterval(titleList);
  },

  /**
   * 显示客户代码下拉框
   */
  oncustomerCodePicker() {
    this.setData({
      customerCodeVisible: true
    });
  },

  /**
   * 选择框确认事件
   * @param {*} e 
   */
  onPickerChange(e) {
    const {
      value,
      label
    } = e.detail;
    const {
      key
    } = e.currentTarget.dataset;
    const arr = label[0].split('-');

    /** 如果是预约时段的确实, 需要判断是否可预约 */
    if (key == 'period') {
      const periodObj = this.data.periodList.find(f => value.includes(f.value));
      if (periodObj.tag == 0) {
        Message.warning({
          context: this,
          offset: [90, 32],
          duration: 5000,
          content: `可预约时段的车辆为0,  不可预约`,
        });
        return;
      }
    }

    /** 是业务类型, 那么将预约时段和预约日期、运输起始、目的地清空掉 */
    if (key == 'type') {
      this.setData({
        dateText: '请选择预约日期',
        period: '',
        periodValue: '',
        startOrigin: '',
        endOrigin: '',
      });
    }

    this.setData({
      [`${key}Visible`]: false,
      [`${key}Value`]: value,
      [`${key}`]: 'type'.includes(key) ? label.join(' ') : value.join(''),
    });

    if ('customerCode'.includes(key)) {
      const userInfoList = wx.getStorageSync('userInfoList');
      const user = wx.getStorageSync('userInfo');
      const currentUser = userInfoList.find(u => u.customerId == value.join('') && u.reservationIdentity == user.reservationIdentity);
      this.setData({
        customerName: arr[1],
        segNo: currentUser.segNo,
        currentUser,
        name: '请选择司机',
        nameValue: '',
        tel: '',
        driverIdentity: '',
        carNumber: '',
        carNumberValue: '',
      });
      return;
    }

    if ('name'.includes(key)) {
      const driver = this.data.driverList.find(d => d.driverIdentity == value);
      this.setData({
        name: label.join(''),
        tel: driver.tel,
        nameQuery: '',
        nameQueryVisible: false,
        driverIdentity: driver.driverIdentity,
        carNumber: driver.vehicleNo.length == 1 ? driver.vehicleNo[0] : '',
        vehicleNoList: driver.vehicleNo.map(v => {
          return {
            label: v,
            value: v,
          };
        }),
      });
      return;
    }

    const {
      typeValue
    } = this.data;
    const typeVIndex = typeValue[0];
    if (getApp().segNo.includes('KF')) {
      this.setData({
        isDisableFormStart: ['10', '60', '70'].includes(typeVIndex),
        isDisableFormEnd: ['20', '50'].includes(typeVIndex),
      });
    }


  },

  /**
   * 打开司机选择框
   */
  onNamePicker() {
    if (this.data.customerCode.includes('代码')) {
      Message.warning({
        context: this,
        offset: [90, 32],
        duration: 5000,
        content: `请先选择${this.data.reservationIdentityName}`,
      });
      return;
    }

    const {
      currentUser
    } = this.data;
    const app = getApp();
    wx.showLoading({
      title: '加载中',
    })
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        serviceId: 'S_LI_RL_0033',
        segNo: currentUser.segNo,
        tel: currentUser.tel,
        customerId: currentUser.customerId,
        reservationIdentity: currentUser.reservationIdentity,
        driverIdentity: currentUser.driverIdentity,
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          Message.warning({
            context: this,
            offset: [90, 32],
            duration: 5000,
            content: '网络异常, 请稍后重试',
          });
          return;
        }

        const result = res.data;
        if (result.__sys__?.status == -1) {
          Message.warning({
            context: this,
            offset: [90, 32],
            duration: 5000,
            content: result.__sys__.msg,
          });
          return;
        }

        const resultList = result.list.map(r => {
          return {
            label: r.driverName,
            value: r.driverIdentity,
          }
        });


        if (resultList.length == 1) {
          const {
            driverIdentity,
            driverName,
            tel,
            vehicleNo,
            uuid
          } = result.list[0];
          this.setData({
            name: driverName,
            driverIdentity,
            tel,
            nameValue: driverIdentity,
            uuid,
            carNumber: vehicleNo.length == 1 ? vehicleNo[0] : '',
            vehicleNoList: vehicleNo.map(v => {
              return {
                label: v,
                value: v,
              };
            }),
          });
          return;
        }
        this.setData({
          nameList: resultList,
          driverList: result.list,
          nameVisible: true,
          nameQueryVisible: true,
        });
      },
      fail: () => {
        wx.hideLoading();
        Message.warning({
          context: this,
          offset: [90, 32],
          duration: 5000,
          content: '网络异常, 请稍后重试',
        });
      }
    });

  },

  /**
   * 打开司机选择框
   */
  getName(name, carNumb, tel) {
    const {
      currentUser
    } = this.data;
    const app = getApp();
    wx.showLoading({
      title: '加载中',
    });
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        serviceId: 'S_LI_RL_0033',
        segNo: currentUser.segNo,
        tel: currentUser.tel,
        customerId: currentUser.customerId,
        reservationIdentity: currentUser.reservationIdentity,
        driverIdentity: currentUser.driverIdentity,
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          Message.warning({
            context: this,
            offset: [90, 32],
            duration: 5000,
            content: '网络异常, 请稍后重试',
          });
          return;
        }

        const result = res.data;
        if (result.__sys__?.status == -1) {
          Message.warning({
            context: this,
            offset: [90, 32],
            duration: 5000,
            content: result.__sys__.msg,
          });
          return;
        }

        let dataList = result.list.filter(r => r.driverName == name && r.tel == tel);
        let resultList = dataList.map(r => {
          return {
            label: r.driverName,
            value: r.driverIdentity,
          }
        });
        if (resultList.length == 1) {
          const {
            driverIdentity,
            driverName,
            tel,
            vehicleNo,
            uuid
          } = dataList[0];
          const carNumber = vehicleNo.length == 1 ? vehicleNo[0] : vehicleNo.find(v => v.includes(carNumb));
          this.setData({
            name: driverName,
            driverIdentity,
            tel,
            nameValue: [driverIdentity],
            uuid,
            carNumber,
            vehicleNoList: vehicleNo.map(v => {
              return {
                label: v,
                value: v,
              };
            }),
            carNumberValue: [carNumber],
          });
          return;
        }
      },
      fail: () => {
        wx.hideLoading();
        Message.warning({
          context: this,
          offset: [90, 32],
          duration: 5000,
          content: '网络异常, 请稍后重试',
        });
      }
    });

  },

  /**
   * 打开预约时段选择器(修改)
   */
  getPeriod(reservationTime) {
    const app = getApp();
    wx.showLoading({
      title: '加载中',
    });
    const {
      customerCode,
      customerName,
      startOrigin,
      endOrigin,
      segNo,
      dateText,
      typeValue
    } = this.data;
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        serviceId: 'S_LI_RL_0016',
        segNo,
        day: dateText,
        handType: typeValue.join(''),
        customerId: customerCode,
        customerName,
        startOrigin,
        endOrigin,
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          Message.warning({
            context: this,
            offset: [90, 32],
            duration: 5000,
            content: '网络异常, 请稍后重试',
          });
          return;
        }

        const result = res.data;
        if (result.__sys__?.status == -1) {
          Message.warning({
            context: this,
            offset: [90, 32],
            duration: 5000,
            content: result.__sys__.msg,
          });
          return;
        }

        if (!result.list) {
          return;
        }
        const dataList = result.list.filter(r => r.reservationTime == reservationTime);
        const resultList = result.list.map(r => {
          return {
            label: r.reservationTime,
            value: r.reservationTime,
            tag: r.num
          }
        });


        if (dataList.length == 1) {
          const {
            reservationTime
          } = dataList[0];
          this.setData({
            periodValue: [reservationTime],
            period: reservationTime,
            periodList: resultList,
          });
          return;
        }
      },
      fail: () => {
        wx.hideLoading();
        Message.warning({
          context: this,
          offset: [90, 32],
          duration: 5000,
          content: '网络异常, 请稍后重试',
        });
      }
    });
  },

  /**
   * 打开车牌号选择框
   */
  onCarNumberPicker() {
    this.setData({
      carNumberVisible: true,
    });
  },

  /**
   * 打开业务类型选择框
   */
  onTypePicker() {
    this.setData({
      typeVisible: true,
    });
  },

  /**
 * 打开业务类型选择框
 */
  onVisitUnitPicker() {
    this.setData({
      visitUnitVisible: true,
    });
  },

  /**
   * 搜索起始地
   * @param {*} e 
   */
  changeHandle(e) {
    const {
      value
    } = e.detail;
    const queryAddrs = this.data.addrList.filter(a => a.label.includes(value));
    this.setData({
      addr: value ? queryAddrs : this.data.addrList,
    })
  },

  /**
   * 搜索司机
   * @param {*} e 
   */
  nameQueryChange(e) {
    const {
      value
    } = e.detail;

    const nameList = this.data.driverList.filter(a => a.driverName.includes(value)).map(r => {
      return {
        label: r.driverName,
        value: r.driverIdentity,
      };
    });
    this.setData({
      nameList,
    })
  },

  /**
   * 显示起始地弹出框
   */
  onStartOriginClick(e) {
    const type = this.data.typeValue;
    // 如果业务类型为装货起始地不可填
    if (!type || type.length == 0) {
      Message.warning({
        context: this,
        offset: [90, 32],
        duration: 5000,
        content: '请先选择业务类型',
      });
      return;
    }

    const {
      currentUser,
      isDisableFormStart,
      isDisableFormEnd
    } = this.data;
    const siteType = e.currentTarget.dataset.param;
    const seachText = e.currentTarget.dataset.seachtext;

    this.setData({
      searchText: seachText,
    });

    if (isDisableFormStart && siteType == '10' && currentUser.segNo.includes('KF')) {
      Message.warning({
        context: this,
        offset: [90, 32],
        duration: 5000,
        content: '业务类型为装货, 起始地不可填',
      });
      return;
    }

    if (isDisableFormEnd && siteType == '20' && currentUser.segNo.includes('KF')) {
      Message.warning({
        context: this,
        offset: [90, 32],
        duration: 5000,
        content: '业务类型为卸货, 目的地不可填',
      });
      return;
    }

    const app = getApp();
    wx.showLoading({
      title: '加载中',
    })
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        serviceId: 'S_LI_RL_0034',
        segNo: currentUser.segNo,
        siteType,
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          Message.warning({
            context: this,
            offset: [90, 32],
            duration: 5000,
            content: '网络异常, 请稍后重试',
          });
          return;
        }

        const result = res.data;
        if (result.__sys__?.status == -1) {
          Message.warning({
            context: this,
            offset: [90, 32],
            duration: 5000,
            content: result.__sys__.msg,
          });
          return;
        }

        const resultList = result.siteName.map(r => {
          return {
            label: r,
            value: r,
          }
        });


        if (resultList.length == 1) {
          const keyName = siteType == '10' ? 'startOrigin' : 'endOrigin';
          this.setData({
            [`${keyName}`]: result.siteName[0],
            siteType,
          });
          return;
        }
        this.setData({
          addr: resultList,
          startOriginVisible: true,
          addrVisible: true,
          searchValue: '',
          addrList: resultList,
          siteType,
        });
      },
      fail: () => {
        wx.hideLoading();
        Message.warning({
          context: this,
          offset: [90, 32],
          duration: 5000,
          content: '网络异常, 请稍后重试',
        });
      }
    });
  },

  /**
   * 隐藏起始地弹出框
   */
  onStartVisibleChange() {
    this.setData({
      startOriginVisible: false,
      addrVisible: false,
    });
  },

  /**
   * 隐藏选择司机弹出框
   */
  onNameQueryChange() {
    this.setData({
      nameQueryVisible: false,
      nameVisible: false,
    });
  },

  /**
   * 取消选择地区事件
   */
  onPickerCancel() {
    this.setData({
      startOriginVisible: false,
      addrVisible: false,
    });
  },

  /**
   * 选中运输起始地
   * @param {*} e 
   */
  onAddrChange(e) {
    const {
      value,
      label
    } = e.detail;
    const keyName = this.data.siteType == '10' ? 'startOrigin' : 'endOrigin';
    this.setData({
      startOriginVisible: false,
      addrVisible: false,
      addrValue: value,
      [`${keyName}`]: label.join(''),
    });
  },

  /**
   * 打开选择日期
   */
  showPicker() {
    if (!this.data.typeValue) {
      Message.warning({
        context: this,
        offset: [90, 32],
        duration: 5000,
        content: '请先选择业务类型',
      });
      return;
    }

    // 重庆用预约起始时间
    if (this.data.segNo == 'JC000000') {
      const now = new Date();
      this.setData({
        start: this.format(now),
        end: '',
      });
    }

    this.setData({
      dateVisible: true,
    });
  },

  showDeadPicker() {
    // 如果不选择起始时间，那边就不能选择截止时间
    if (!this.data.dateText || this.data.dateText.includes('请选择')) {
      Message.warning({
        context: this,
        offset: [90, 32],
        duration: 5000,
        content: '请先选择预约起始日期',
      });
      return;
    }

    this.setData({
      deadVisible: true,

    });
  },

  /**
   * 关闭选择日期
   */
  hidePicker() {
    this.setData({
      dateVisible: false,
      deadVisible: false,
    });
  },

  /**
   * 确定日期
   * @param {*} e 
   */
  onColumnChange(e) {
    const {
      value
    } = e.detail;
    console.log(value);
    // 重庆用预约起始时间
    if (this.data.segNo == 'JC000000') {
      // 解析原始时间
      const year = parseInt(value.substring(0, 4));
      const month = parseInt(value.substring(4, 6)) - 1; // 月份从0开始
      const day = parseInt(value.substring(6, 8));
      const hours = parseInt(value.substring(8, 10));
      const minutes = parseInt(value.substring(10, 12));

      // 创建日期对象并加12小时
      const originalDate = new Date(year, month, day, hours, minutes);
      const newDate = new Date(originalDate.getTime() + 12 * 60 * 60 * 1000);
      const endDate = new Date(originalDate.getTime() + 24 * 60 * 60 * 1000);
      this.setData({
        deadlineText: this.format(newDate),
        deadDate: this.format(newDate),
        deadStart: value,
        deadEnd: this.format(endDate),
        date: value,
        dateText: value,
        dateVisible: false,
      });
      return;
    }

    this.setData({
      date: value,
      dateText: value,
      dateVisible: false,
    });
  },

  /**
   * 确定截止时期
   * @param {*} e 
   */
  onDeadChange(e) {
    const {
      value
    } = e.detail;
    this.setData({
      deadDate: value,
      deadlineText: value,
      deadVisible: false,
    });
  },

  /**
   * 打开预约时段选择器
   */
  onPeriodPicker() {
    if (this.data.dateText.includes('请选择')) {
      Message.warning({
        context: this,
        offset: [90, 32],
        duration: 5000,
        content: '请先选择预约日期',
      });
      return;
    }

    const app = getApp();
    wx.showLoading({
      title: '加载中',
    });
    const {
      customerCode,
      customerName,
      startOrigin,
      endOrigin,
      segNo,
      dateText,
      typeValue
    } = this.data;
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        serviceId: 'S_LI_RL_0016',
        segNo: segNo,
        day: dateText,
        handType: typeValue.join(''),
        customerId: customerCode,
        customerName,
        startOrigin,
        endOrigin,
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          Message.warning({
            context: this,
            offset: [90, 32],
            duration: 5000,
            content: '网络异常, 请稍后重试',
          });
          return;
        }

        const result = res.data;
        if (result.__sys__?.status == -1) {
          Message.warning({
            context: this,
            offset: [90, 32],
            duration: 5000,
            content: result.__sys__.msg,
          });
          return;
        }

        const resultList = result.list.map(r => {
          return {
            label: r.reservationTime,
            value: r.reservationTime,
            tag: r.num
          }
        });


        if (resultList.length == 1) {
          const {
            reservationTime
          } = result.list[0];
          this.setData({
            periodValue: reservationTime,
            period: reservationTime,
            periodList: resultList,
          });
          return;
        }
        this.setData({
          periodList: resultList,
          periodVisible: true,
        });
      },
      fail: () => {
        wx.hideLoading();
        Message.warning({
          context: this,
          offset: [90, 32],
          duration: 5000,
          content: '网络异常, 请稍后重试',
        });
      }
    });
  },

  /**
   * 新增之前先显示提示信息
   */
  openTitle(app) {
    wx.showLoading({
      title: '加载中',
    });
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        serviceId: 'S_LI_RL_0035',
        segNo: this.data.currentUser.segNo,
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {

          Message.warning({
            context: this,
            offset: [90, 32],
            duration: 5000,
            content: '网络异常, 请稍后重试',
          });
          return;
        }

        const result = res.data;
        if (result.__sys__?.status == -1) {
          Message.warning({
            context: this,
            offset: [90, 32],
            duration: 5000,
            content: result.__sys__.msg,
          });
          return;
        }

        if (!result.list || result.list.length == 0) {
          // 没有提示信息直接新增
          this.insert();
          return;
        }

        const resultList = result.list;
        this.openInterval(resultList);
      },
      fail: () => {
        wx.hideLoading();
        Message.warning({
          context: this,
          offset: [90, 32],
          duration: 5000,
          content: '网络异常, 请稍后重试',
        });
      }
    });
  },

  /**
   * 打开确认信息弹框
   */
  submit() {
    let {
      currentUser,
      name,
      carNumber,
      type,
      startOrigin,
      endOrigin,
      dateText,
      period,
      isDisableFormEnd,
      isDisableFormStart,
      typeValue,
      isBringGood,
      bringTextarea
    } = this.data;
    if (!currentUser) {
      Message.warning({
        context: this,
        offset: [90, 32],
        duration: 5000,
        content: '请选择客户/承运商代码',
      });
      return;
    }

    if (name.includes('请选择')) {
      Message.warning({
        context: this,
        offset: [90, 32],
        duration: 5000,
        content: '请选择司机',
      });
      return;
    }

    if (!carNumber) {
      Message.warning({
        context: this,
        offset: [90, 32],
        duration: 5000,
        content: '请选择车牌号',
      });
      return;
    }

    if (type.includes('请选择')) {
      Message.warning({
        context: this,
        offset: [90, 32],
        duration: 5000,
        content: '请选择业务类型',
      });
      return;
    }

    if (isDisableFormEnd && !startOrigin && currentUser.segNo.includes('KF')) {
      Message.warning({
        context: this,
        offset: [90, 32],
        duration: 5000,
        content: '卸货类型, 请选择运输起始地',
      });
      return;
    }

    if (isDisableFormStart && !endOrigin && currentUser.segNo.includes('KF')) {
      Message.warning({
        context: this,
        offset: [90, 32],
        duration: 5000,
        content: '装货类型, 请选择运输目的地',
      });
      return;
    }

    if (['30', '80'].includes(typeValue[0]) && (!endOrigin || !startOrigin) && currentUser.segNo.includes('KF')) {
      Message.warning({
        context: this,
        offset: [90, 32],
        duration: 5000,
        content: '装卸货或者其他物品运输类型, 请选择运输目的地和起始地',
      });
      return;
    }

    if (!dateText || dateText.includes('请选择')) {
      Message.warning({
        context: this,
        offset: [90, 32],
        duration: 5000,
        content: '请选择预约日期',
      });
      return;
    }

    if (isBringGood == 1 && !bringTextarea) {
      Message.warning({
        context: this,
        offset: [90, 32],
        duration: 5000,
        content: '请填写自带货描述',
      });
      return;
    }

    // if (!period) {
    //   Message.warning({
    //     context: this,
    //     offset: [90, 32],
    //     duration: 5000,
    //     content: '请选择预约时段',
    //   });
    //   return;
    // }

    const app = getApp();
    // 调用确认信息弹框
    this.openTitle(app);
  },

  /**
   * 确认新增(调用新增预约接口)
   */
  insert() {
    const isEdit = this.data.isEdit;
    wx.showLoading({
      title: isEdit ? '修改中' : '新增中',
    })
    const app = getApp();
    const {
      currentUser,
      customerCode,
      customerName,
      name,
      tel,
      driverIdentity,
      carNumber,
      typeValue,
      startOrigin,
      endOrigin,
      dateText,
      period,
      reservationNumber,
      isBringGood,
      visitUnit,
      bringTextarea
    } = this.data;
    let recCreator = currentUser.reservationIdentity == 30 ? currentUser.driverName : currentUser.administrator;
    const result = {
      segNo: currentUser.segNo,
      unitCode: currentUser.segNo,
      recCreator: currentUser.tel,
      recCreatorName: recCreator,
      customerId: customerCode,
      customerName,
      driverName: name,
      driverTel: tel,
      driverIdentity,
      vehicleNo: carNumber,
      typeOfHandling: typeValue.join(''),
      startOfTransport: startOrigin || '重庆宝钢',
      purposeOfTransport: endOrigin || '重庆宝钢',
      reservationDate: dateText,
      reservationTime: currentUser.segNo.includes('JC') ? this.data.deadlineText : period,
      reservationNumber,
      reservationIdentity: currentUser.reservationIdentity,
      visitUnit,
      isSelfProduced: isBringGood,
      selfProducedDesc: bringTextarea,
    };

    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        result,
        serviceId: isEdit ? 'S_LI_RL_0100' : 'S_LI_RL_0015',
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          wx.hideLoading();
          Message.warning({
            context: this,
            offset: [90, 32],
            duration: 5000,
            content: '网络异常, 请稍后重试',
          });
          return;
        }

        const result = res.data;
        if (result.__sys__?.status == -1) {
          wx.hideLoading();
          Message.warning({
            context: this,
            offset: [90, 32],
            duration: 5000,
            content: result.__sys__.msg,
          });
          return;
        }

        if (isEdit) {
          Toast({
            context: this,
            selector: '#t-toast',
            message: '修改成功',
            theme: 'success',
            direction: 'column',
          });

          // 返回主页面
          setTimeout(() => {
            wx.navigateBack({
              delta: 1, // 返回上一页
              success: function () {
                // 在成功返回时，可以通过页面栈来调用A页面的刷新方法
                const pages = getCurrentPages(); // 获取页面栈
                const prevPage = pages[pages.length - 1]; // 获取上一页A页面
                if (prevPage) {
                  prevPage.getReversionInfoList(); // 调用A页面的刷新方法
                }
              }
            });
          }, 1000);

          return;
        }

        // 新增成功, 清空数据
        this.setData({
          name: '请选择司机',
          nameValue: '',
          tel: '司机手机号',
          driverIdentity: '司机身份证号',
          carNumber: '',
          carNumberValue: '',
          type: '请选择业务类型',
          typeValue: '',
          startOrigin: '',
          endOrigin: '',
          dateText: '请选择预约日期',
          period: '',
          periodValue: '',
          deadEnd: '',
          deadDate: '',
          deadlineText: '请选择预约截止日期',
        });
        if (this.data.segNo === 'JC000000') {
          Message.success({
            context: this,
            offset: [90, 32],
            duration: 5000,
            content: '预约成功',
          });
        } else {
          this.setData({
            showMsg: true,
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        Message.warning({
          context: this,
          offset: [90, 32],
          duration: 5000,
          content: '网络异常, 请稍后重试',
        });
      }
    });
  },

  closeMsgDialog() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 打开弹框定时
   */
  openInterval(resultList) {
    const r = resultList[0];

    let {
      bold,
      addRed,
      addUnderline,
      notificationText,
      countdownDuration
    } = r;
    // **步骤1：预处理，过滤掉空字符串或纯空格**
    const getValidSections = (str) => {
      return (str || "")
        .split(/[,，]/) // 中英文逗号分割
        .map(section => section.trim()) // 去除首尾空格
        .filter(section => section.length > 0); // 过滤空字符串
    };

    const boldSections = getValidSections(bold);
    const redSections = getValidSections(addRed);
    const underlineSections = getValidSections(addUnderline);

    // **步骤2：合并所有需要样式的文本（避免重复处理）**
    const styleMap = new Map(); // { text: Set<styles> }

    // 通用处理函数
    const addToStyleMap = (sections, styleType) => {
      sections.forEach(text => {
        if (!styleMap.has(text)) {
          styleMap.set(text, new Set());
        }
        styleMap.get(text).add(styleType);
      });
    };

    addToStyleMap(boldSections, "bold");
    addToStyleMap(redSections, "red");
    addToStyleMap(underlineSections, "underline");

    // **步骤3：按长度降序排序（避免短文本覆盖长文本）**
    // 步骤3：生成排序后的正则表达式
    const sortedSections = Array.from(styleMap.keys())
      .sort((a, b) => b.length - a.length) // 关键排序逻辑
      .map(text => ({
        text,
        regex: new RegExp(`(${text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, "g"),
        styles: styleMap.get(text)
      }));

    // **步骤4：单次替换，生成复合样式**
    let processedText = notificationText;
    sortedSections.forEach(({
      text,
      regex,
      styles
    }) => {
      processedText = processedText.replace(regex, (match) => {
        const styleArr = [];
        if (styles.has("bold")) styleArr.push("font-weight: bold");
        if (styles.has("red")) styleArr.push("color: red");
        if (styles.has("underline")) styleArr.push("text-decoration: underline");
        return `<span style="${styleArr.join("; ")}">${match}</span>`;
      });
    });

    // **步骤5：最后处理换行**
    processedText = processedText.replace(/\n/g, "<br>");

    this.setData({
      titleList: resultList,
      showTextAndTitle: true,
      title: r.notificationTitle,
      content: processedText,
      confirmBtn: {
        content: `已知晓以上内容(${countdownDuration})`,
        variant: 'base',
        disabled: true,
      },
    });
    const query = wx.createSelectorQuery();
    // 等于0则没有滚动条, 不等于0有滚动条, 按钮控制放到滚动条事件中, 这里只是启用倒计时
    // 没有滚动条就相当于滚动到底部了, 由于是异步就写里面
    query.select('#dialogScroll').boundingClientRect((rect) => {
      // 299固定高度超出就有滚动条
      const isNoScrollBar = rect.height < 299;
      
      this.setData({
        clientHeight: rect.height, // 存储可视区域高度
        isToBottom: isNoScrollBar, // 无滚动条时直接到底部
      });

      countdownDuration--;

      const intervalId = setInterval(() => {
        this.setData({
          confirmBtn: {
            content: `已知晓以上内容(${countdownDuration})`,
            variant: 'base',
            disabled: true,
          },
        });

        // 倒计时完成
        if (countdownDuration == 0) {
          // 无滚动条或已滚动到底部
          if (isNoScrollBar || this.data.isToBottom) {
            this.setData({
              isCountdown: true,
              confirmBtn: {
                content: `已知晓以上内容`,
                variant: 'base',
                disabled: false,
              },
            });
          } else {
            // 有滚动条但未滚动到底部
            this.setData({
              isCountdown: true,
              confirmBtn: {
                content: `已知晓以上内容`,
                variant: 'base',
                disabled: true,
              },
            });
            Message.warning({
              context: this,
              offset: [90, 32],
              duration: 5000,
              content: '请下拉到底部',
            });
          }
          clearInterval(intervalId);
          return;
        }
        this.setData({
          isCountdown: false, // 倒计时没有完成
        });
        countdownDuration--;
      }, 1000);

    }).exec();

  },

  /**
   * 确认信息是否滚动到底部
   * @param {*} event 
   */
  handleScroll(event) {
    // 防抖处理，避免频繁执行
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
    }
    
    this.scrollTimer = setTimeout(() => {
      const scrollTop = event.detail.scrollTop; // 当前滚动位置
      const scrollHeight = event.detail.scrollHeight; // 总内容高度
      const clientHeight = this.data.clientHeight; // 使用已存储的可视区域高度
      const tolerance = 20; // 增加容差值为 20
      
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - tolerance;
      
      if (isAtBottom) {
        // 滚动到底部
        if (this.data.isCountdown) {
          this.setData({
            confirmBtn: {
              content: `已知晓以上内容`,
              variant: 'base',
              disabled: false,
            },
            isToBottom: true,
          });
        } else {
          this.setData({
            isToBottom: true,
          });
        }
      } else {
        // 未滚动到底部，重置状态
        if (this.data.isToBottom) {
          this.setData({
            isToBottom: false,
          });
          // 如果倒计时已完成但未滚动到底部，禁用按钮
          if (this.data.isCountdown) {
            this.setData({
              confirmBtn: {
                content: `已知晓以上内容`,
                variant: 'base',
                disabled: true,
              },
            });
          }
        }
      }
    }, 50); // 50ms 防抖

  },

  /**
   * input框伪双向绑定
   * @param {*} e 
   */
  onInputChang(e) {
    const {
      key
    } = e.currentTarget.dataset;
    const {
      value
    } = e.detail;
    this.setData({
      [key]: value,
    });
  },

  /**
   * 页面卸载时清理定时器
   */
  onUnload() {
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
      this.scrollTimer = null;
    }
  },

  /**
   * 返回YYYYMMDDHHdd
   * @param {时间对象} date 
   */
  format: (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}${month}${day}${hours}${minutes}`;
  },

})