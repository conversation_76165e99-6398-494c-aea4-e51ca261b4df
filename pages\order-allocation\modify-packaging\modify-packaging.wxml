<!--pages/order-allocation/modify-packaging/modify-packaging.wxml-->
<view class="container">
    <view  class="scroll-container">
        <view class="order-scroll {{dataList.length>0 ? '' : 'dataList-empty' }}">
            <view wx:for="{{dataList}}" wx:key="index" wx:for-item="item">
                <view class='order-list'>
                    <view class="list-name" bindtap="cheackOne" data-index="{{index}}">
                        <view class="list-name-checked">
                            <image src="/assets/image/icon_UncheckBox.png" class='cheacked-img' wx:if="{{!item.active}}"></image>
                            <image src="/assets/image/icon_checkBox.png" class='cheacked-img' wx:if="{{item.active}}"></image>
                        </view> 
					    <view class="list-name-number">扫描顺序：{{item.order}}</view>
				    </view>
                    <view class="list-name-start">捆包号：{{item.packId}}</view>
                    <view class="list-name-start">是否为自带货：{{item.outPackFlag==1 ? '是' : '否'}}</view>
                    <view class="list-name-start">板数：{{item.piceNum}}</view>
                    <image src="/assets/image/icon-edit.png" class="edit-infor" bindtap="editInfor" data-item="{{item}}" data-index="{{index}}"></image>
                </view>
            </view>
            <view wx:if="{{!dataList.length>0}}">
                <image src="/assets/image/empty_data.png" class="my-order-img"></image>
                <view class="my-order-text">暂无数据</view>
            </view>
        </view>
    </view> 
    <view class="order-button" id="order-button">
        <view class="whole-order" bindtap="cheackOrder">删除</view>
    </view>
    <!-- 修改弹窗 -->
    <t-overlay visible="{{overlayVisible}}" duration="{{500}}">
        <view class="overlay-con">
            <view class="overlay-main">
                <view class="overlay-title">捆包数据修改</view>
                <t-input label="扫描顺序" type="number" placeholder="请输入数字" align="right" value="{{order}}" data-name="order" bindchange="numberChange" /> 
                <t-input label="捆包号" value="{{packId}}" placeholder="请输入文字" align="right" disabled/> 
                <view class="overlay-one">
                    <view>是否为自带货</view>
                    <t-radio-group value="{{flag}}" bind:change="onChange" borderless t-class="box" data-name="flag"  style="text-align: center;">
                        <t-radio block="{{false}}" label="是" value="1" />
                        <t-radio block="{{false}}" label="否" value="0" />
                    </t-radio-group>
                </view>
                <view class="overlay-one">
                    <view>板材类型</view>
                    <t-radio-group value="{{scanType}}" bind:change="onChange" borderless t-class="box" data-name="scanType" style="text-align: center;">
                        <t-radio block="{{false}}" label="板" value="板" />
                        <t-radio block="{{false}}" label="卷" value="卷" />
                    </t-radio-group>
                </view>
                <t-input label="板数" placeholder="请输入板的数量" value="{{piceNum}}" align="right" disabled="{{scanType=='板'?false:true}}" data-name="piceNum" bindchange="numberChange"/>
                <view class="overlay-button">
                    <view class="overlay-button-cancle" bindtap="cancle">取消</view>
                    <view class="overlay-button-confirm" bindtap="confirm">确定</view>
                </view>
            </view>
        </view>
    </t-overlay>
    <!-- 确认删除提示信息 -->
    <t-dialog
    visible="{{showConfirm}}"
    title="提示"
    content="确认删除当前选中数据！"
    confirm-btn="确定"
    cancel-btn="取消"
    bind:confirm="confirmDialog"
    bind:cancel="cancelDialog"
    />
</view>


