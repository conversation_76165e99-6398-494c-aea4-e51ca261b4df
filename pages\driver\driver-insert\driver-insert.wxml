<view class="container" bind:tap="onPage" style="padding-bottom: {{keyboardHeight}}rpx;">
  <!-- 智能输入框 -->
  <view class="smart-input-section">
    <view class="smart-input-header">
      <text class="smart-input-title">智能识别</text>
      <text class="smart-input-subtitle">输入文本自动识别姓名、电话、身份证号、车牌号</text>
    </view>

    <!-- 示例文本 -->
    <view class="smart-input-example {{smartInputFocused ? 'expanded' : ''}}">
      <text class="example-label">示例格式：</text>
      <text class="example-text">张三 13812345678 110101199001011234 渝A12356</text>
      <view class="example-detail {{smartInputFocused ? 'show' : 'hide'}}">
        <text class="detail-item">• 支持空格、换行等分隔符</text>
        <text class="detail-item">• 支持带标签格式：姓名：张三 手机号：13812345678</text>
        <text class="detail-item">• 信息顺序可任意排列</text>
        <text class="detail-item">• 支持7位蓝牌和8位新能源车牌</text>
      </view>
    </view>

    <t-textarea
      placeholder="请输入包含司机信息的文本..."
      value="{{smartInputText}}"
      maxlength="500"
      autosize="{{true}}"
      data-key="smartInputText"
      bindchange="onSmartInputChange"
      bindfocus="onSmartInputFocus"
      bindblur="onSmartInputBlur"
      t-class="smart-textarea"
    ></t-textarea>
    <view class="smart-input-actions">
      <t-button size="small" theme="primary" bindtap="onSmartParse" disabled="{{!smartInputText}}">
        智能识别
      </t-button>
      <t-button size="small" theme="default" bindtap="onClearSmartInput">
        清空
      </t-button>
    </view>
  </view>

  <view class="form-group">
    <t-cell class="mb-16" title="{{reservationIdentityName}}代码" arrow hover note="{{customerCodeText}}" bind:click="onCustomerCodePicker" t-class-note="app-t-class" />
    <t-picker visible="{{customerCodeVisible}}" value="{{customerCodeValue}}" title="选择{{reservationIdentityName}}代码" cancelBtn="取消" confirmBtn="确认" usingCustomNavbar bindchange="onCustomerCodeChange">
      <t-picker-item options="{{customerCodeList}}" />
    </t-picker>

    <t-input label="{{reservationIdentityName}}名称" value="{{customerName}}" disabled align="right" t-class-input="app-t-class"></t-input>

    <t-input label="司机姓名" placeholder="请输入司机姓名" align="right" data-key="name" value="{{name}}" bindchange="onInputChang"></t-input>
    <t-input label="司机手机号" placeholder="请输入手机号" align="right" data-key="tel" value="{{tel}}" bindchange="onInputChang"></t-input>
    <t-input label="身份证号" placeholder="请输入身份证号" align="right" data-key="idCard" value="{{idCard}}" bindchange="onInputChang"></t-input>
    <t-input label="车牌号" disabled align="right" t-class-input="app-t-class" value="请在下方输入车牌号"></t-input>

    <car-plate-input activeIndex="{{activeIndex}}" codeArray="{{carPlateArray}}" data-key="car-number-div" bind:inputcomplete="onInputComplete" bind:keyboardshow="onKeyboardShow" bind:keyboardhide="onKeyboardHide"></car-plate-input>

    <t-input placeholder="输入验证码" value="{{code}}" type="number" data-key="code" bindchange="onInputChang" maxlength="6">
      <t-button slot="suffix" theme="primary" size="extra-small" bind:tap="sendCode" variant="outline"> 发送验证码 </t-button>
    </t-input>
  </view>

  <view class="btn-group">
    <t-button theme="primary" block bind:tap="insert">新增</t-button>
  </view>

  <t-dialog visible="{{showTextAndTitle}}" title="提示" content="{{content}}" confirm-btn="{{ confirmBtn }}" bind:confirm="closeDialog" />

  <t-toast id="t-toast" />
</view>