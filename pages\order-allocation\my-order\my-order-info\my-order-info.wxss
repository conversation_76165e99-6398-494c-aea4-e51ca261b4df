/* pages/order-allocation/loading-order-detail/loading-order-detail.wxss */
/* page{
    width: 100%;
    height: 100%;
} */
.container {
  width: 100%;
  height: 100%;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
}

.scroll-container {
  width: 100%;
  height: calc(100% - 300rpx);
  flex-grow: 1;
}

.order-scroll {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.dataList-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.order-list {
  width: calc(100% - 64rpx);
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  margin: 0 auto 20rpx;
  border-radius: 20rpx;
  box-sizing: border-box;
}

.list-name {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.list-name-checked image {
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
}

.list-name-number {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
  margin-left: 10rpx;
  flex: 1;
}

/* 提单类型标签样式 */
.billing-method-tag {
  font-size: 22rpx;
  color: #1953E6;
  background-color: rgba(25, 83, 230, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 1rpx solid #1953E6;
  margin-left: 20rpx;
}

.list-name-start {
  font-size: 28rpx;
  color: #666666;
  line-height: 40rpx;
  margin-bottom: 10rpx;
}

.list-name-cloum {
  width: 100%;
  font-size: 28rpx;
  color: #666666;
  line-height: 40rpx;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.list-name-cloum-one {
  flex: 0 0 50%;
  margin-bottom: 10rpx;
}

.bomTxt {
  display: flex;
  justify-content: center;
  font-size: 12px;
  color: rgb(126, 138, 155);
  padding: 0rpx 0rpx 20rpx 0rpx;
}

.order-button {
  width: 100%;
  height: 100rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.whole-order {
  width: 80%;
  height: 64rpx;
  background-color: #1953E6;
  color: #ffffff;
  text-align: center;
  line-height: 64rpx;
  margin: 0 auto;
  font-size: 28rpx;
  border-radius: 8rpx;
}

.my-order-img {
  width: 150px;
  height: 150px;
  display: block;
}

.my-order-text {
  text-align: center;
  font-size: 16px;
  color: rgb(51, 51, 51);
}

/* 汇总信息样式 */
.summary-info {
  width: calc(100% - 64rpx);
  margin: 20rpx auto;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
}

.summary-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
  text-align: center;
}

.summary-content {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: wrap;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-width: 100rpx;
}

.summary-label {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.summary-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #1953E6;
}