//逢三位转逗号,保留两位小数，支持负数
var threeNumForTwo = {
    money_three_for_two_thousands: function(num) {
        var integerBit='' //整数部分
        var decimalplaces='' //小数部分
        var result=''  //格式化后的整数部分
        var plusMinus='' //正负号
        // 判断正数还是负数
        var numStr = num.toString();
        if (num < 0) {
            numStr = numStr.slice(1); // 去掉负号
            plusMinus='-'
        }else{
            plusMinus=''
        }
        // 判断整数还是小数
        var decimalIndex = numStr.indexOf('.'); 
        if (decimalIndex === -1) {
          integerBit= numStr
        } else {
          integerBit = numStr.substring(0, decimalIndex);
          decimalplaces = numStr.substring(decimalIndex,numStr.length);
        }
        while (integerBit.length > 3) {
            result = ',' + integerBit.slice(-3) + result;
            integerBit = integerBit.slice(0, integerBit.length - 3);
        }
        if (integerBit) {
            result = integerBit + result;
        }
        return plusMinus+result+decimalplaces;
    }
}
   
  module.exports = {
    money_three_for_two_thousands: threeNumForTwo.money_three_for_two_thousands //暴露接口调用
  }