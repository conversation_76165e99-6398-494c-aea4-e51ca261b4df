<view class="container">
    <t-cell class="mb-16" t-class-note="app-t-class" title="拜访单位" arrow hover note="{{copyName}}" bind:click="chooseCopyName" />
    <t-popup
    visible="{{copyVisible}}"
    usingCustomNavbar
    bind:visible-change="onVisibleChange"
    placement="right"
    style="top:0"
    >
    <view class="block-close">
        <t-icon t-class="close-btn" name="close" size="50rpx" color="#fff" bind:tap="onClose" />
    </view>
    <view class="block block--right">
        <view wx:for="{{copyList}}" wx:key="index" wx:for-item="item">
            <company-tree objData="{{item}}" step="1" styleFlag="{{styleFlag}}" bind:changeValue="getValue" bind:foldList="getId"></company-tree>
        </view>
    </view>
    </t-popup>
    <t-input t-class-input="app-t-class" label="姓名" placeholder="请输入您的姓名" align="right" model:value="{{appointmentPerson}}"/>
    <t-cell title="性别" hover>
        <t-radio-group default-value="{{appointmentGender}}" borderless t-class="box" slot="note" bind:change="onChangeGender">
            <t-radio block="{{false}}" label="男" value="男" />
            <t-radio block="{{false}}" label="女" value="女" />
        </t-radio-group>
    </t-cell>
    <t-input t-class-input="app-t-class" label="手机号" placeholder="请输入您的手机号" align="right" model:value="{{appointmentPhone}}"/>
    <t-input t-class-input="app-t-class" label="身份证号" placeholder="请输入您的身份证号" align="right" model:value="{{appointmentId}}"/>
    <t-cell class="mb-16" title="预约时间" arrow hover note="{{appointmentStart}}" bind:click="selectTime" data-flag="start" t-class-note="app-t-class" />
    <t-date-time-picker
        title="选择日期和时间"
        visible="{{datetimeVisible}}"
        mode="minute"
        value="{{shartTime}}"
        format="YYYY-MM-DD HH:mm"
        bindchange="onConfirm"
        bindcancel="hidePicker"
    />
    <t-cell class="mb-16" title="截止时间" arrow hover note="{{appointmentEnd}}" bind:click="selectTime" data-flag="end" t-class-note="app-t-class" />
    <t-cell title="说明备注" hover arrow="false" />
        <t-textarea
    label=""
    placeholder="请填写备注，如无备注需求，填写‘无’"
    disableDefaultPadding="{{true}}"
    maxcharacter="500"
    indicator
    bind:line-change="onLineChange"
    model:value="{{appointmentRemrk}}"
    />
    <view style="padding-left: 32rpx;background-color: #ffffff;">
        <view style="border-bottom: 1rpx solid #cccccc59;"></view>
    </view>
    <t-cell title="人脸门禁" hover arrow="false" />
    <view class="reservation-all">
        <view class="reservation-img">
            <image src="/assets/image/photo_img.jpg" class="reservation-special"></image>
            <t-upload
                mediaType="{{['video','image']}}"
                files="{{originFiles}}"
                gridConfig="{{gridConfig}}"
                max="{{1}}"
                bind:success="handleSuccess"
                bind:remove="handleRemove"
                bind:click="handleClick"
            >
            </t-upload>
        </view> 
        <view class="reservation-img-title">*请严格按照拍照标准进行拍照，否则可能导致照片审核失败！</view>
        <view class="reservation-img-title">*照片中只能出现一个人脸信息</view>
    </view>
    <view class="reservation-buttom" bindtap="confirmAppointment">确认预约</view>
    <!-- 弹窗信息 -->
    <t-message id="t-message" />
    <t-toast id="t-toast" />
</view>
