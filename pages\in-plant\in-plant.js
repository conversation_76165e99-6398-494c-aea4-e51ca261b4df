// pages/in-plant/in-plant.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    value: '', // 搜索框
    list: [],
    inHouseCars: {
      value: 'all',
      options: [
        { value: 'all', label: '车辆是否在厂' },
        { value: '10', label: '10-未进厂' },
        { value: '20', label: '20-在厂' },
        { value: '30', label: '30-已出厂' },
        { value: '40', label: '40-本日已出厂' },
      ],
    },
    showTextAndTitle: false,
    content: '', // 提示信息
    confirmBtn: { content: '确定', variant: 'base' },
    filterVisible: false,
    filterParams: {
      keyword: '',
    },
    belongCompanyValue: '请选择客户',
    belongCompany: '',
    belongCompanyQueryVisible: false,
    belongCompanyQuery: '',
    belongCompanyList: [],
    belongCompanyVisible: false,
    carrier: '',
    carrierValue: '请选择承运商',
    // 分页相关参数
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    isLoading: false,
    scrollTop: 0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getInPlantList(true);
  },

  // 打开抽屉
  openFilter() {
    this.setData({ filterVisible: true })
  },

  // 关闭抽屉
  closeFilter() {
    this.setData({ filterVisible: false })
  },

  // 重置筛选
  resetFilter() {
    this.setData({
      filterParams: {
        keyword: '',
        status: '',
        startDate: '',
        endDate: ''
      },
      belongCompanyValue: '请选择客户',
      belongCompany: '',
      belongCompanyQuery: '',
      belongCompanyList: [],
      carrierValue: '请选择承运商',
      carrier: '',
    });
    this.getInPlantList(true);
  },

  // 输入框变化
  onKeywordChange(e) {
    this.setData({
      'filterParams.keyword': e.detail.value
    })
  },

  // 确认筛选
  confirmFilter() {
    if (!this.data.belongCompanyValue.includes('请选择') && !this.data.carrierValue.includes('请选择')) {
      this.setData({
        showTextAndTitle: true,
        content: '客户和承运商只能选其一',
      });
      return;
    }


    this.closeFilter()
    // 这里执行筛选逻辑
    this.getInPlantList(true);
  },

  /**
 * 隐藏选择司机弹出框
 */
  belongCompanyCanCel() {
    this.setData({
      belongCompanyVisible: false,
      belongCompanyQueryVisible: false,
    });
  },

  /**
 * 确定选择框值
 */
  onPickerChange(e) {
    const { value, label } = e.detail;
    const { key } = e.currentTarget.dataset;

    // 承运商
    if (this.data.reservationIdentity == '20') {
      this.setData({
        [`${key}Visible`]: false,
        carrierValue: value,
        carrier: label,
      });
    } else {
      this.setData({
        [`${key}Visible`]: false,
        [`${key}Value`]: value,
        [`${key}`]: label,
      });
    }


    this.belongCompanyCanCel();
  },

  /**
 * 显示客户选择框
 */
  onShowBelongCompany() {
    this.setData({
      reservationIdentity: '10',
    });
    this.getCustomerList();
  },

  /**
* 显示承运商选择框
*/
  onShowCarrier() {
    this.setData({
      reservationIdentity: '20',
    });
    this.getCustomerList();
  },

  /**
 * 根据公客户编码查询
 */
  belongCompanyQueryChange(e) {
    const { value } = e.detail;
    this.setData({
      belongCompanyQuery: value,
    })
  },

  belongSumbit() {
    this.getCustomerList();
  },

  belongClear() {
    this.setData({
      belongCompanyQuery: '',
    });
    this.getCustomerList();
  },

  /**
 * 伪双向绑定搜索框
 * @param {*} e 
 */
  changeHandle(e) {
    const { value } = e.detail;
    this.setData({
      value,
    });
  },

  /**
 * 车辆是否在厂下拉框选择
 * @param {*} e 
 */
  onChange(e) {
    this.setData({
      'inHouseCars.value': e.detail.value,
    });
    this.getInPlantList(true);
  },

  /**
 * 点击搜索按钮或者回车
 */
  actionHandle() {
    this.getInPlantList(true);
  },

  /**
 * 转到详情
 * @param {*} e 
 */
  toInfo(e) {
    // 下标
    const { key } = e.currentTarget.dataset;
    let data = this.data.list[key];
    wx.navigateTo({
      url: '/pages/in-plant/in-plant-info/in-plant-info',
      events: {
        // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
        acceptDataFromOpenedPage: () => { },
      },
      success: (res) => {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('acceptDataFromOpenerPage', { data, })
      }
    });
  },

  /**
   * 清空搜索框
   */
  clearValue() {
    this.setData({
      value: '',
    });
    this.getInPlantList(true);
  },

  /**
   * 查询厂内物流
   */
  getInPlantList(resetPage = false) {
    if (this.data.isLoading) return;

    const newState = {};
    if (resetPage) {
      newState.currentPage = 1;
      newState.list = [];
      newState.hasMore = true;
      this.setData({
        scrollTop: 0,
      });
    }

    const app = getApp();
    const user = wx.getStorageSync('userInfo');
    const { value, inHouseCars } = this.data;

    const inHouseCarsValue = inHouseCars.value == 'all' ? '' : inHouseCars.value;
    let carNumb = '';
    let driverName = '';
    let voucherNum = '';
    // 满足姓名
    if (app.isChinese(value)) {
      // 姓名
      driverName = value;
    } else if (/^[A-Za-z0-9]*$/.test(value)) {
      // 提单号
      voucherNum = value;
    } else {
      carNumb = value;
    }

    let customerId = '';
    if (!this.data.belongCompanyValue.includes('请选择')) {
      customerId = this.data.belongCompanyValue[0];
    } else if (!this.data.carrierValue.includes('请选择')) {
      customerId = this.data.carrierValue[0];
    }

    wx.showLoading({
      title: '查询中',
    });
    this.setData({ ...newState, isLoading: true });
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        segNo: user.segNo,
        isHouseCars: inHouseCarsValue,
        vehicleNo: carNumb,
        customerId,
        driverName,
        voucherNum,
        purposeOfTransport: this.data.filterParams.keyword,
        serviceId: 'S_LI_RL_0116',
        pageNum: this.data.currentPage,
        pageSize: this.data.pageSize,
      },
      success: (res) => {
        wx.hideLoading();
        const result = res.data;
        if (result.__sys__?.status == -1) {
          this.setData({
            showTextAndTitle: true,
            content: result.__sys__.msg,
            isLoading: false,
          });
          return;
        }

        const reservationList = result.list?.map(r => {
          // 定义格式化函数并处理空值
          const format = str => {
            if (typeof str !== 'string' || !str.trim()) return ''; // 返回 null 或自定义默认值
            return str.replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1/$2/$3 $4:$5:$6');
          };

          // 使用解构赋值 + 动态字段映射
          const fields = [
            ['leaveFactoryDate', 'leaveFactoryDateStr'],
            ['checkDate', 'checkDateStr'],
            ['enterFactory', 'enterFactoryStr'],
            ['signOffTime', 'signOffTimeStr'],
            ['completeUninstallTime', 'completeUninstallTimeStr'],
            ['beginEntruckingTime', 'beginEntruckingTimeStr'],
          ];

          return fields.reduce((acc, [src, target]) => ({
            ...acc,
            [target]: format(r[src])
          }), { ...r });
        });
        
        const currentList = reservationList || [];
        this.setData({
          list: resetPage ?
            currentList :
            [...this.data.list, ...currentList],
          hasMore: currentList.length > 9,
          isLoading: false
        });
      },
      fail: () => {
        wx.hideLoading();
        this.setData({
          showTextAndTitle: true,
          content: '网络异常, 请稍后重试',
          isLoading: false,
        });
      }
    });
  },

  // 滚动触底事件
  onReachBottom: function () {
    this.debounce(function () {
      if (this.data.hasMore && !this.data.isLoading) {
        this.setData(
          { currentPage: this.data.currentPage + 1 },
          () => this.getInPlantList()
        );
      }
    }.bind(this))();
  },

  // 新增防抖函数
  debounce(func, delay = 500) {
    let timer = null;
    return function (...args) {
      if (timer) clearTimeout(timer);
      timer = setTimeout(() => {
        func.apply(this, args);
      }, delay);
    };
  },

  /**
 * 查询客户
 */
  getCustomerList() {
    const app = getApp();
    wx.showLoading({
      title: '查询中',
    });
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        segNo: app.segNo,
        customerName: this.data.belongCompanyQuery,
        reservationIdentity: this.data.reservationIdentity,
        serviceId: 'S_LI_RL_0113',
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          this.setData({
            showTextAndTitle: true,
            content: '网络异常, 请稍后重试',
          });
          return;
        }

        const result = res.data;
        if (result.__sys__?.status == -1) {
          this.setData({
            showTextAndTitle: true,
            content: result.__sys__.msg,
          });
          return;
        }

        const { list } = result;
        let belongCompanyList = list.map(l => {
          return {
            label: l.chineseUserName,
            value: l.userNum,
          }
        });

        this.setData({
          belongCompanyList,
          belongCompanyVisible: true,
          belongCompanyQueryVisible: true,
        });

      },
    });
  },

  /**
* 关闭错误提示框
*/
  closeDialog() {
    this.setData({
      showTextAndTitle: false,
    });
  },

})