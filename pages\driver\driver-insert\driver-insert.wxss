.container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 智能输入框样式 */
.smart-input-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.smart-input-header {
  margin-bottom: 12px;
}

.smart-input-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.smart-input-subtitle {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.smart-textarea {
  margin-bottom: 12px;
}

.smart-input-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* 示例文本样式 */
.smart-input-example {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
  border-left: 3px solid #0052d9;
  transition: all 0.3s ease;
}

.smart-input-example.expanded {
  background-color: #e7f3ff;
}

.example-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  display: block;
  margin-bottom: 4px;
}

.example-text {
  font-size: 14px;
  color: #0052d9;
  font-family: 'Courier New', monospace;
  word-break: break-all;
  line-height: 1.4;
}

.example-detail {
  margin-top: 8px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.example-detail.show {
  max-height: 100px;
  opacity: 1;
}

.example-detail.hide {
  max-height: 0;
  opacity: 0;
}

.detail-item {
  display: block;
  font-size: 11px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 2px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group t-input {
  margin-bottom: 16px;
}

.verification-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.verification-group t-input {
  flex-grow: 1;
  margin-right: 8px;
}

.btn-group t-button {
  margin-top: 16px;
}

.btn-group {
  height: 160rpx;
}
