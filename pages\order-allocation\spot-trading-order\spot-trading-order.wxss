/* pages/order-allocation/spot-trading-order/spot-trading-order.wxss */
.con {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.example-search {
  background-color: var(--bg-color-demo);
  padding: 16rpx 32rpx;
  flex-shrink: 0;
}

/* 筛选行样式 - 客户选择框和筛选按钮在同一行 */
.filter-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 16rpx;
  min-height: 64rpx; /* 确保行有固定高度 */
}

/* 客户选择器样式 */
.customer-selector {
  flex: 1;
  margin-right: 16rpx;
  max-width: calc(100% - 80rpx); /* 为右侧按钮预留空间 */
  overflow: hidden;
}

/* 修复下拉菜单项文本溢出 */
.customer-selector .t-dropdown-item__label,
.customer-selector .t-dropdown-item__content,
.customer-selector .t-dropdown-item,
.customer-selector text,
.customer-selector view {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: border-box;
}

/* 筛选按钮容器 */
.filter-btn-wrapper {
  flex-shrink: 0;
  width: 64rpx; /* 固定宽度 */
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 筛选弹窗样式 */
.filter-popup {
  background-color: #ffffff;
  border-radius: 16rpx 16rpx 0 0;
  padding: 0;
  max-height: 80vh;
}

.filter-header {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.filter-close {
  font-size: 48rpx;
  color: #999999;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-content {
  padding: 32rpx;
}

.filter-item {
  margin-bottom: 32rpx;
}

.filter-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.filter-input {
  width: 100%;
}

.remark-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  box-sizing: border-box;
}

.remark-input:focus {
  border-color: #1953E6;
}

.filter-footer {
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
}

.filter-reset, .filter-confirm {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
}

.filter-reset {
  background-color: #f5f5f5;
  color: #666666;
  border: 1rpx solid #e0e0e0;
}

.filter-confirm {
  background-color: #1953E6;
  color: #ffffff;
}

.container {
  height: calc(100vh - 182rpx);
  overflow: hidden;
}

.scroll-container {
  height: calc(100% - 100rpx);
  overflow: hidden;
  flex-grow: 1;
}

.order-scroll {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.dataList-empty {
  width: 100%;
  height: calc(100% - 100rpx);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.my-order-img {
  width: 150px;
  height: 150px;
  display: block;
}

.my-order-text {
  text-align: center;
  font-size: 16px;
  color: rgb(51, 51, 51);
}

.order-list {
  width: calc(100% - 64rpx);
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  margin: 0 auto 20rpx;
  border-radius: 20rpx;
  box-sizing: border-box;
}

.list-name {
  display: flex;
  flex-direction: row;
  align-items: center;
  align-content: center;
  margin-bottom: 10rpx;
}

.list-name-checked image {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
}

.list-name-number {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
  margin-left: 10rpx;
}

.list-name-start {
  font-size: 28rpx;
  color: #666666;
  line-height: 40rpx;
  margin-bottom: 10rpx;
}

.list-name-cloum {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.list-name-flex {
  flex: 0 0 50%;
}

.bomTxt {
  display: flex;
  justify-content: center;
  font-size: 12px;
  color: rgb(126, 138, 155);
  padding: 0rpx 0rpx 20rpx 0rpx;
}

.order-button {
  margin-top: 32rpx;
  width: 90%;
  margin-left: 5%;
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.whole-order {
  width: 80%;
  height: 64rpx;
  background-color: #1953E6;
  color: #ffffff;
  text-align: center;
  line-height: 64rpx;
  margin: 0 auto;
  font-size: 28rpx;
  border-radius: 8rpx;
} 