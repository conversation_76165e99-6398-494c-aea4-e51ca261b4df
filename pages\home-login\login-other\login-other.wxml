<view class="main">
  <t-input placeholder="请输入手机号码" value="{{phoneNumber}}" bindchange="onPhoneInput" maxlength="11" type="number" class="main-input">
    <view slot="suffix" style="display: flex; align-items: center">
      <view class="suffix--line"></view>
      <view class="verify input-placeholder" aria-role="button" bind:tap="selectSegNo">{{segNo}}</view>
    </view>
  </t-input>
  <t-input placeholder="请输入验证码" value="{{code}}" bindchange="onCodeInput" class="main-input">
      <t-button slot="suffix" theme="primary" size="extra-small" variant="outline" bind:tap="sendCode"> 发送验证码 </t-button>
  </t-input>

  <view class="button-example">
    <t-button theme="primary" block size="large" bind:tap="phoneLogin">登录</t-button>
  </view>

  <t-picker visible="{{segNoVisible}}" value="{{segNoValue}}" data-key="segNo" title="选择账套" cancelBtn="取消" confirmBtn="确认" usingCustomNavbar bindchange="onPickerChange">
    <!-- bindpick="onColumnChange" bindcancel="onPickerCancel" -->
    <t-picker-item options="{{segNos}}" />
  </t-picker>

  <!-- 包含"不存在"错误时显示双按钮弹框 -->
  <t-dialog 
    wx:if="{{showTemporaryReservation}}"
    visible="{{showTextAndTitle}}" 
    title="提示" 
    content="{{dialogContent}}" 
    confirm-btn="{{ confirmBtn }}" 
    cancel-btn="临时预约"
    bind:confirm="closeDialog" 
    bind:cancel="onTemporaryReservation" />

  <!-- 其他错误时显示单按钮弹框 -->
  <t-dialog 
    wx:else
    visible="{{showTextAndTitle}}" 
    title="提示" 
    content="{{dialogContent}}" 
    confirm-btn="{{ confirmBtn }}" 
    bind:confirm="closeDialog" />

  <!-- <t-overlay visible="true" duration="{{500}}" bind:click="handleOverlayClick" /> -->

  <t-overlay visible="{{overlayVisible}}">
    <view class="overlay-view">
      <t-loading theme="circular" size="40rpx" class="wrapper" />
    </view>
  </t-overlay>

  <t-picker visible="{{cityVisible}}" value="{{cityValue}}" auto-close="{{false}}" cancel-btn="取消" title="选择角色" confirmBtn="确认" usingCustomNavbar bind:confirm="onCityChange" bindcancel="onCityCancel">
    <t-picker-item options="{{citys}}"></t-picker-item>
  </t-picker>

  <t-message id="t-message" />
</view>