.my-basic {
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* 让内容左右两边对齐 */
  margin-left: 50rpx;
  margin-top: 50rpx;
  margin-right: 50rpx;
  /* 给右侧一些边距 */
}

/* 头像 */
.avatar-example:not(:last-child) {
  margin-right: 16px;
  /* 调整头像与文本的间距 */
}

/* 信息容器，垂直排列姓名和电话号码 */
.info-container {
  margin-left: 30rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-grow: 1;
  /* 让信息容器占据中间的剩余空间 */
}

/* 图标容器 */
.icon-container {
  margin-left: auto;
  /* 将图标推到最右边 */
}

/* 姓名样式 */
.name {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 4px;
  /* 调整姓名与电话号码之间的间距 */
}

/* 电话号码样式 */
.phone {
  font-size: 14px;
  color: #666;
  /* 根据需求调整颜色 */
}

.external-class-content {
  color: #fff;
  background-color: var(--td-brand-color, #0052d9);
  font-weight: 400;
}

/* 全局容器样式 */
.container {
  background-color: #f2f3f5;
  padding: 20rpx;
}

/* 部分标题样式 */
.section {
  margin-top: 20rpx;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  color: #000; /* 字体为黑色 */
  margin-bottom: 10rpx;
  margin-left: 30rpx;
  margin-top: 30rpx;
}

/* Grid 样式 */
t-grid {
  background-color: #ffffff;
  padding: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

t-grid-item {
  text-align: center;
  /* color: #333; */
}

text {
  color: #000;
  font-size: 16px;
  font-weight: 400;
}
