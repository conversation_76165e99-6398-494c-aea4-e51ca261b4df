// components/car-plate-input/car-plate-input.js
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    activeIndex: Number,
    bottom: String,
    codeArray: Array,
  },

  /**
   * 组件的初始数据
   */
  data: {
    currentKeyboard: [],
    numKeyboard: [
      ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'],
      ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'],
      ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'],
      ['Z', 'X', 'C', 'V', 'B', 'N', 'M'],
      ['港', '澳', '学'],
    ],
    disabledKeys: [],// 新增：禁用的键列表
    codeArray: ['', '', '', '', '', '', '', '新能源'],
    activeIndex: -1, // 初始化没有选中的输入框
    showProvincePicker: true, // 新增控制变量
    provinces: [ // 省份数据
      '皖', '苏', '浙', '京', '沪', '津', '渝', '冀', '晋', '辽', '吉', '黑',
      '闽', '赣', '鲁', '豫', '鄂', '湘', '粤', '琼', '川', '贵', '云', '陕',
      '甘', '青', '台', '蒙', '桂', '宁', '新', '藏', '港', '澳', '学'
    ],
    selectedProvince: '', // 保存选择的省份
    showKeyboard: false,
    sumCarNumber: 8,
    isDisabledNum: false,
    bottom:0,
    fontSize: '10px', // 默认字体大小
  },

  attached() {
    this.setData({
      showKeyboard: this.data.activeIndex > -1,
    });
    // 初始化时如果传入了codeArray属性，使用传入的值
    if (this.properties.codeArray && this.properties.codeArray.length > 0) {
      const hasContent = this.properties.codeArray.some(item => item && item !== '新能源');
      if (hasContent) {
        this.setData({
          codeArray: [...this.properties.codeArray]
        });
        if (this.properties.codeArray[0]) {
          this.setData({
            selectedProvince: this.properties.codeArray[0]
          });
        }
      }
    }
  },

  /**
   * 组件的观察器
   */
  observers: {
    // 监听外部传入的codeArray属性变化
    'codeArray': function(newVal) {
      if (newVal && newVal.length > 0) {
        // 检查是否与当前内部数据不同，避免无限循环
        const currentArray = this.data.codeArray;
        const isDifferent = !currentArray || currentArray.length !== newVal.length ||
                           currentArray.some((item, index) => item !== newVal[index]);

        if (isDifferent) {
          // 只有当数据真正不同时才更新
          this.setData({
            codeArray: [...newVal] // 使用展开运算符创建新数组
          });
          // 如果第一位有值，设置为选中的省份
          if (newVal[0]) {
            this.setData({
              selectedProvince: newVal[0]
            });
          }
        }
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 修改新能源框字体大小
    updateFontSize() {
      let fontSize = this.data.fontSize;
      const newCode = [...this.data.codeArray];

      if (newCode[7].includes('新能源')) {
        fontSize = '10px'; // 设置新能源框的字体大小
      } else {
        fontSize = '32rpx'; // 默认字体大小
      }
      this.setData({ fontSize });
    },

    // 新增隐藏键盘方法
    hideKeyboard() {
      this.setData({ showKeyboard: false });
      let newCode = [...this.data.codeArray];
      if (newCode[7].includes('新能源')) {
        newCode[7] = '';
      }
      this.triggerEvent('inputcomplete', {
        plate: `${this.data.selectedProvince}${newCode.join('')}`
      });

      // 通知父页面键盘隐藏
      this.triggerEvent('keyboardhide');
    },

    // 新增回退按钮功能
    onBackspaceTap() {
      const newCode = [...this.data.codeArray];
      newCode[this.data.activeIndex] = this.data.activeIndex == 7 ? '新能源' : '';
      this.setData({
        codeArray: newCode,
        activeIndex: Math.max(this.data.activeIndex - 1, 0)
      });
      this.updateFontSize();
      if (this.data.activeIndex == 0) {
        this.setData({
          currentKeyboard: this.data.provinces,
          showProvincePicker: true,
        })
      } else if (this.data.activeIndex == 1) {
        // 禁用数字
        this.setData({
          isDisabledNum: true,
        })
      }
    },

    // 点击键盘按键
    onKeyTap(e) {
      const value = e.currentTarget.dataset.value;
      const newCode = [...this.data.codeArray];
      newCode[this.data.activeIndex] = value;
      // 判断使用哪种键盘
      const isShowP = this.data.activeIndex == 0;
      const curIndex = this.data.activeIndex + 1;
      this.setData({
        codeArray: newCode,
        activeIndex: Math.min(curIndex, this.data.sumCarNumber),
        showProvincePicker: isShowP,
        currentKeyboard: isShowP ? this.data.provinces : this.data.numKeyboard,
        isDisabledNum: curIndex == 1,
      });

      if (this.data.activeIndex === this.data.sumCarNumber) {
        this.triggerEvent('inputcomplete', {
          plate: `${this.data.selectedProvince}${newCode.join('')}`
        });
        this.setData({ showKeyboard: false }); // 新增隐藏键盘

        // 通知父页面键盘隐藏
        this.triggerEvent('keyboardhide');
      }

      this.updateFontSize();
    },


    // 选择省份
    selectProvince(e) {
      const value = e.currentTarget.dataset.value;
      const newCode = [...this.data.codeArray];
      newCode[this.data.activeIndex] = value;

      this.setData({
        codeArray: newCode,
        activeIndex: Math.min(this.data.activeIndex + 1, this.data.sumCarNumber),
        showProvincePicker: false,
        currentKeyboard: this.data.numKeyboard,
        isDisabledNum: true, //选择省份之后一定是第二个输入框
      });

      if (this.data.activeIndex === this.data.sumCarNumber) {
        this.setData({ showKeyboard: false });
        // 通知父页面键盘隐藏
        this.triggerEvent('keyboardhide');
      }
    },

    // 聚焦到下一个输入框
    moveFocus(event) {
      const index = event.currentTarget.dataset.index;
      let newCode = [...this.data.codeArray];
      newCode[index] = event.detail.value;
      this.setData({
        codeArray: newCode,
        activeIndex: Math.min(index + 1, this.data.sumCarNumber), // 让焦点跳转到下一个输入框
        isDisabledNum: index == 1,
      });
    },

    // 车牌号框点击事件
    highlightCurrentBox(event) {
      if (!this.data.showKeyboard) {
        this.setData({ showKeyboard: true });
        // 通知父页面键盘显示
        this.triggerEvent('keyboardshow', {
          keyboardHeight: 350 // 键盘大概高度，单位rpx
        });
      }

      const currentIndex = event.currentTarget.dataset.index;
      const isShowP = currentIndex == 0;
      this.setData({
        activeIndex: Math.min(currentIndex, this.data.sumCarNumber), // 让焦点跳转到下一个输入框
        showProvincePicker: isShowP,
        currentKeyboard: isShowP ? this.data.provinces : this.data.numKeyboard,
        isDisabledNum: currentIndex == 1,
      });

    },



    blurCurrentBox() {
      this.setData({
        activeIndex: -1  // 或者设为 null，表示没有选中
      });
    },

  }
});