<!--pages/in-plant/in-plant.wxml-->
<view>
  <view class="example-search">
    <t-search value="{{value}}" placeholder="按照姓名、车牌号、提单号" action="{{'搜索'}}" bind:action-click="actionHandle" bind:change="changeHandle" bind:submit="actionHandle" bind:clear="clearValue" />

    <!-- 将两个元素包裹在 flex 容器中 -->
    <view class="filter-container">
      <!-- 下拉菜单 (宽度自适应) -->
      <t-dropdown-menu class="dropdown-menu" style="width:300rpx;background-color: transparent;position: initial;">
        <t-dropdown-item options="{{inHouseCars.options}}" value="{{inHouseCars.value}}" bindchange="onChange" />
      </t-dropdown-menu>

      <!-- 筛选按钮 (靠右对齐) -->
      <view class="filter-btn-wrapper">
        <t-button icon="filter" variant="text" bindtap="openFilter"></t-button>
      </view>
    </view>

    <!-- 抽屉组件 -->
    <t-drawer visible="{{filterVisible}}" placement="right" title="筛选条件" bind:close="closeFilter">
      <view class="filter-content">
        <!-- 筛选条件组件 -->
        <t-input label="目的地" placeholder="请输入目的地" value="{{filterParams.keyword}}" bind:change="onKeywordChange" />

        <t-cell class="mb-16" t-class-note="app-t-class" title="客户代码" arrow hover note="{{belongCompanyValue}}" bind:click="onShowBelongCompany" />
        <t-cell class="mb-16" t-class-note="app-t-class company-class" t-class-title="app-title" title="客户名称" note="{{belongCompany}}" />

        <t-cell class="mb-16" t-class-note="app-t-class" title="承运商代码" arrow hover note="{{carrierValue}}" bind:click="onShowCarrier" />
        <t-cell class="mb-16" t-class-note="app-t-class company-class" t-class-title="app-title" title="承运商名称" note="{{carrier}}" />

        <!-- 搜索所属公司-->
        <t-popup visible="{{belongCompanyQueryVisible}}" usingCustomNavbar placement="bottom" style="height: 580rpx;">
          <view class="aaa">
            <view class="example-search-popup">
              <t-search placeholder="搜索公司" bind:change="belongCompanyQueryChange" bind:submit="belongSumbit" bind:clear="belongClear" value="{{belongCompanyQuery}}" />
            </view>
            <view class="example-picker">
              <t-picker auto-close="{{false}}" visible="{{belongCompanyVisible}}" value="{{belongCompanyValue}}" data-key="belongCompany" title="选择公司" cancelBtn="取消" confirmBtn="确认" usingCustomNavbar bindchange="onPickerChange" bindcancel="belongCompanyCanCel">
                <t-picker-item options="{{belongCompanyList}}" />
              </t-picker>
            </view>
          </view>
        </t-popup>

      </view>

      <!-- 底部操作栏 -->
      <view slot="footer" class="filter-footer">
        <t-button theme="default" bindtap="resetFilter">重置</t-button>
        <t-button theme="primary" bindtap="confirmFilter">确认</t-button>
      </view>
    </t-drawer>

  </view>

  <scroll-view scroll-y style="height: calc(100vh - 380rpx);" bindscrolltolower="onReachBottom" scroll-top="{{scrollTop}}">
    <view wx:if="{{list.length > 0}}">
      <view class="card-main" wx:for="{{list}}" wx:key="index" data-key="{{index}}" bind:tap="toInfo">
        <view class="card-item">
          <view class="label">车辆跟踪号:</view>
          <view class="value">
            {{ item.CAR_TRACE_NO }}
            <t-tag class="margin-16" theme="primary">{{item.status}}</t-tag>
          </view>
        </view>
        <view class="card-item">
          <view class="label">业务类型:</view>
          <view class="value">{{item.businessType}}</view>
        </view>
        <view class="card-item">
          <view class="label">运输起始地:</view>
          <view class="value">{{item.startOfTransport}}</view>
        </view>
        <view class="card-item">
          <view class="label">运输目的地:</view>
          <view class="value">{{item.purposeOfTransport}}</view>
        </view>
        <view class="card-item">
          <view class="label">承运商/客户名称:</view>
          <view class="value">{{item.customerId}}{{item.customerName}}</view>
        </view>
        <view class="card-item">
          <view class="label">车牌号:</view>
          <view class="value">{{item.vehicleNo}}</view>
        </view>
        <view class="card-item">
          <view class="label">司机姓名:</view>
          <view class="value">{{item.DRIVER_NAME}}</view>
        </view>
        <view class="card-item">
          <view class="label">登记时间:</view>
          <view class="value">{{item.checkDateStr}}</view>
        </view>

        <view class="card-item">
          <view class="label">进厂时间:</view>
          <view class="value">{{item.enterFactoryStr}}</view>
        </view>

        <view class="card-item">
          <view class="label">当前装卸点名称:</view>
          <view class="value">{{item.currentHandPointName}}</view>
        </view>

        <view class="card-item">
          <view class="label">出厂时间:</view>
          <view class="value">{{item.leaveFactoryDateStr}}</view>
        </view>

      </view>
    </view>

    <!-- 无数据提示 -->
    <view wx:else class="empty-container">
      <t-empty icon="file-image" description="暂无提货委托数据" />
    </view>

    <!-- 底部加载状态 -->
    <view class="pagination-status" wx:if="{{list.length > 0}}">
      <text wx:if="{{hasMore}}">加载更多...</text>
      <text wx:else>没有更多数据了</text>
    </view>
  </scroll-view>

  <t-dialog visible="{{showTextAndTitle}}" title="提示" content="{{content}}" confirm-btn="{{ confirmBtn }}" bind:confirm="closeDialog" />
</view>