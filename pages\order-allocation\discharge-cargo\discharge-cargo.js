// pages/order-allocation/discharge-cargo/discharge-cargo.js
const $api = require('../../../api/request')
Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {},
    totalHeight: 0,
    screenHeight: 0,
    visible: true,
    marquee2: {
      speed: 60,
      loop: -1,
      delay: 0,
    },
    name: "",
    carTitle: "请选择车牌号",
    dateText: "请选择车牌号",
    carFlag: false,
    carName: "",
    carNumber: "",
    packId: "", //54431315800
    carNumberList: [],
    searchKeyword: '',
    searchPlaceholder: '',
    filteredCarList: [],
    selectedCarItem: null,
    scanList: [],
    byoGoods: "",
    overlayVisible: false,
    flag: '',
    typeText: "请选择扫描类型",
    scanType: "",
    scanVisible: false,
    scanTypeList: [
      { label: "板", value: "板" },
      { label: "卷", value: "卷" },
      { label: '板+卷', value: "板+卷" }],
    plateNumber: "",
    carList: [],
    OrderNumberVisible: false,
    vehicleNo: "",
    vehicleNoList: [],
    showTextAndTitle: false,
    content: '', // 提示信息
    confirmBtn: { content: '确定', variant: 'base' },
    selectedAllocateVehicleNo: '',
    queueFlag: '0', // 是否排队 0-否 1-是
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let totalHeight = wx.getWindowInfo().windowHeight
    this.setData({
      totalHeight: totalHeight,
      userInfo: wx.getStorageSync('userInfo'),
      typeText: this.data.scanTypeList[1].value,
      scanType: this.data.scanTypeList[1].value,
    })
    this.fetchCustomerOptions();
  },

  // 查询车辆信息
  getCarInfo(customerIds) {
    let data = {
      "segNo": this.data.userInfo.segNo,
      "tel": this.data.userInfo.tel,
      "customerId": customerIds,
      isNotShowToast: true,
    }
    $api.request('S_LI_RL_0069', '', data).then((res) => {
      let arr = []
      let arrData = []
      if (res.list.length > 0) {
        //  再判断carTraceNo是否有值 有值才能选说明车辆已经进厂了
        arrData = res.list.filter(item => {
          return item.carTraceNo !== ""
        });
        arr = arrData.map(item => {
          return { 
            label: item.vehicleNo, 
            value: item.vehicleNo,
            driverName: item.driverName || '未知司机',
            tel: item.tel,
            driverIdentity: item.driverIdentity,
            displayText: `${item.vehicleNo} (${item.driverName || '未知司机'})`
          };
        })
      }
      
      // 设置默认选中第一个
      const firstItem = arr.length > 0 ? arr[0] : null;
      this.setData({
        carNumberList: arr,
        filteredCarList: arr,
        carList: res.list,
        carTitle: firstItem ? firstItem.displayText : "请选择车牌号",
        carNumber: firstItem ? firstItem.value : "",
        carName: firstItem ? firstItem.label : "",
        dateText: firstItem ? firstItem.label : "请选择车牌号",
      })

    }).catch((err) => {
      this.setData({
        showTextAndTitle: true,
        content: err.data.__sys__.msg,
      });
    })
  },

  // 获取客户选项
  fetchCustomerOptions() {
    const user = wx.getStorageSync('userInfo');
    const app = getApp();
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        serviceId: 'S_LI_RL_0026',
        tel: user.tel,
        segNo: user.segNo,
        identityType: user.identityType,
        reservationIdentity: user.reservationIdentity,
      },
      success: (res) => {
        if (res?.data?.result) {
          const resultList = res.data.result.filter((item, index, self) =>
            index === self.findIndex(t => t.customerId === item.customerId && t.customerName === item.customerName)
          );
          const customerIds = resultList.map(r => r.customerId);

          this.getCarInfo(customerIds);
        }
      }
    });
  },

  // 选择车牌号
  onSerchCar() {
    this.setData({
      carFlag: true
    })
  },

  // 选车牌
  selectCar() {
    // 如果当前已经有选中的车牌号，找到对应的车牌项作为默认选中
    let defaultSelectedItem = null;
    if (this.data.carNumber) {
      defaultSelectedItem = this.data.carNumberList.find(item => 
        item.value === this.data.carNumber
      );
    }
    
    this.setData({
      carFlag: true,
      searchKeyword: '',
      searchPlaceholder: '输入车牌号或司机姓名进行筛选',
      filteredCarList: this.data.carNumberList,
      selectedCarItem: defaultSelectedItem || null
    })
  },

  // 搜索车牌号
  onSearchCar(e) {
    const keyword = e.detail.value; // 保持原始大小写
    const filteredList = this.data.carNumberList.filter(item => {
      // 不区分大小写匹配车牌号或司机姓名，但保持显示原始大小写
      return item.label.toLowerCase().includes(keyword.toLowerCase()) ||
             item.driverName.toLowerCase().includes(keyword.toLowerCase());
    });
    
    this.setData({
      searchKeyword: keyword, // 保持用户输入的原始大小写
      filteredCarList: filteredList
    });
  },

  // 选择车牌项
  selectCarItem(e) {
    const carItem = e.currentTarget.dataset.car;
    // 如果点击的是已选中的项，则取消选中
    const isCurrentlySelected = this.data.selectedCarItem && 
                               this.data.selectedCarItem.value === carItem.value &&
                               this.data.selectedCarItem.driverName === carItem.driverName;
    
    this.setData({
      selectedCarItem: isCurrentlySelected ? null : carItem
    });
  },

  // 确认选择车牌
  confirmSelectCar() {
    if (!this.data.selectedCarItem) {
      this.setData({
        showTextAndTitle: true,
        content: '请选择车牌号',
      });
      return;
    }
    
    this.setData({
      carNumber: this.data.selectedCarItem.value,
      carTitle: this.data.selectedCarItem.displayText,
      carName: this.data.selectedCarItem.label,
      dateText: this.data.selectedCarItem.label,
      carFlag: false,
      searchPlaceholder: ''
    });
  },

  // 取消选择车牌
  cancelSelectCar() {
    this.setData({
      carFlag: false,
      searchKeyword: '',
      searchPlaceholder: '',
      selectedCarItem: null
    });
  },

  carChange(e) {
    this.setData({
      dateText: e.detail.label[0],
      carName: e.detail.label[0]
    })
  },

  // 选择扫描类型
  onChangeType() {
    this.setData({
      scanVisible: true
    })
  },

  typeChange(e) {
    this.setData({
      typeText: e.detail.label[0],
      scanType: e.detail.label[0]
    })
    if (e.detail.label[0] == "卷") {
      this.setData({
        plateNumber: ""
      });
    } else {
      this.setData({
        scanList: [],
        packId: '',
      });
    }
  },

  // 捆包号查找计划
  getPlan(e) {
    const data = {
      "result": [{
        "segNo": this.data.userInfo.segNo,
        "packId": e,
      }],
      isNotShowToast: true,
    }
    $api.request('S_LI_RL_0070', '', data).then((res) => {
      if (res.result.length > 0) {
        const data = res.result[0]
        if (data.existFlag === "0") {
          this.setData({
            overlayVisible: true,
            flag: ''
          });
          return false;
        }
        const OldData = this.data.scanList;
        // 计算当前最大的顺序号
        const maxOrder = OldData.length > 0 ? Math.max(...OldData.map(item => item.order)) : 0;
        let list = [{
          order: maxOrder + 1,
          packId: e,
          // flag:"否",
          outPackFlag: "0",
          piceNum: '1',
          scanType: this.data.scanType,
          // 整合数据
          ...data
        }];
        this.setData({
          scanList: this.data.scanList.concat(list),
          packId: '',
        })
      }
    }).catch((err) => {
      this.setData({
        showTextAndTitle: true,
        content: err.data.__sys__.msg,
      });
    })
  },

  numberChange(e) {
    this.setData({
      plateNumber: e.detail.value,
    })
  },

  /**
* 关闭错误提示框
*/
  closeDialog() {
    this.setData({
      showTextAndTitle: false,
    });
  },

  // 数据验证
  checkData(e) {
    // 为板就不需要扫描
    if (this.data.scanType == "板") {
      return;
    }
    // 先判断有没有添加过这条捆包
    const object = this.data.scanList.filter(function (obj) {
      return obj.packId === e;
    });
    if (object.length > 0) {
      this.setData({
        showTextAndTitle: true,
        content: '此捆包已存在',
      });
      return;
    }
    this.getPlan(e);
  },

  packChange(e) {
    this.setData({
      packId: e.detail.value
    });
  },

  enterEvent(e) {
    this.setData({
      packId: e.detail.value,
    });
    this.checkData(this.data.packId)
  },

  onScanCode(e) {
    // 调用相机扫码API
    wx.scanCode({
      success: (res) => {
        // 扫码成功回调
        if (!res.result) {
          this.setData({
            showTextAndTitle: true,
            content: '扫描标签返回信息为空',
          });
          return;
        }

        this.setData({
          packId: res.result,
        });
        this.checkData(res.result);
      },
      fail: (err) => {
        // 扫码失败回调 
        console.error(err);
      }
    });
  },

  onChange(e) {
    let propName = e.currentTarget.dataset.key;
    let dataToUpdate = {};
    dataToUpdate[propName] = e.detail.value;
    this.setData(dataToUpdate);
  },

  radioChange(e) {
    this.setData({
      selectedAllocateVehicleNo: e.detail.value
    })
  },

  // 是否排队选择
  onQueueChange(e) {
    this.setData({
      queueFlag: e.detail.value
    });
  },

  cancle() {
    this.setData({
      overlayVisible: false,
      flag: ""
    });
  },

  confirm() {
    if (this.data.flag === "0" || this.data.flag === "1") {
      const OldData = this.data.scanList;
      // 计算当前最大的顺序号
      const maxOrder = OldData.length > 0 ? Math.max(...OldData.map(item => item.order)) : 0;
      let listItem = {};
      listItem = {
        order: maxOrder + 1,
        packId: this.data.packId,
        outPackFlag: this.data.flag,
        piceNum: '1',
        scanType: this.data.scanType,
      };

      let list = [];
      list.push(listItem);
      this.setData({
        scanList: this.data.scanList.concat(list),
        packId: '',
        overlayVisible: false
      });
    } else {
      this.setData({
        showTextAndTitle: true,
        content: '请选择是否为自带货',
      });
    }
  },

  goToList() {
    var that = this;
    wx.navigateTo({
      url: '/pages/order-allocation/modify-packaging/modify-packaging',
      success: function (res) {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('acceptDataFromDischargeCargo', { data: that.data.scanList })
      }
    })
  },

  //装卸货类型需要查询配单号
  getUnloadingType(e) {
    const data = {
      list: e,
      isNotShowToast: true, // 不使用toast弹出框
    }
    $api.request('S_LI_RL_0071', '', data).then((res) => {
      if (!res || !res.listInfo || res.listInfo.length == 0) {
        this.setData({
          showTextAndTitle: true,
          content: '未查询到配单信息',
        });
        return;
      }

      if (res.listInfo.length == 1) {
        // 直接配单
        this.orderRequest(e, res.listInfo[0].allocateVehicleNo)
      } else {
        // 弹窗选配单号后再配单
        // let radioList = res.listInfo.map(item => {
        //   return { label: item.allocateVehicleNo, value: item.allocateVehicleNo };
        // })
        const vehicleNoList = res.listInfo.map(r => ({
          ...r,
          recCreateTimeStr: r.recCreateTime.replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1/$2/$3 $4:$5:$6'),
          allocTypeName: r.allocType == '10' ? '装货配单' : '卸货配单'
        })
        )
        this.setData({
          OrderNumberVisible: true,
          vehicleNoList,
        })
      }

    }).catch((err) => {
      this.setData({
        showTextAndTitle: true,
        content: '未查询到配单信息',
      });
    })
  },

  // 配单完成按钮
  orderRequest(list, allocateVehicleNo) {
    let result = this.data.scanList;
    if (this.data.typeText.includes('板')) {
      result.push({
        packId: 'XXX',
        scanType: this.data.scanType,
        piceNum: this.data.plateNumber
      });
    }

    let data = {
      result,
      listInfo: [{
        allocateVehicleNo: allocateVehicleNo
      }],
      list: [{
        segNo: this.data.userInfo.segNo,
        driverTel: list[0].driverTel,
        vehicleNo: list[0].vehicleNo,
        handType: list[0].handType,
        carTraceNo: list[0].carTraceNo,
        autoFlag: this.data.queueFlag, // 是否排队
      }],
      recCreator: this.data.userInfo.tel,
      recCreatorName: this.data.userInfo.administrator || this.data.userInfo.driverName,
      isNotShowToast: true,
    }

    $api.request('S_LI_RL_0072', '', data).then((res) => {
      this.setData({
        showTextAndTitle: true,
        content: res.__sys__.msg,
        scanList: [],
      })

    }).catch((err) => {
      this.setData({
        showTextAndTitle: true,
        content: err.data.__sys__.msg,
      });
    })
  },

  // 配单最后完成
  orderCompletion() {
    // 先判断配单类型
    let that = this;
    const carData = this.data.carList.filter(item => {
      return item.vehicleNo === that.data.carName
    });

    if (carData.length == 0) {
      this.setData({
        showTextAndTitle: true,
        content: '请选择车牌号',
      });
      return;
    }

    if (this.data.typeText == '卷' && this.data.scanList.length == 0) {
      this.setData({
        showTextAndTitle: true,
        content: '请扫描捆包号',
      });
      return;
    }

    if (this.data.typeText == '板' && (!this.data.plateNumber || this.data.plateNumber == 0)) {
      this.setData({
        showTextAndTitle: true,
        content: '请输入板的张数',
      });
      return;
    }

    if (this.data.typeText == '板+卷' && (!this.data.plateNumber || this.data.plateNumber == 0) && this.data.scanList.length == 0) {
      this.setData({
        showTextAndTitle: true,
        content: '请输入板的张数或者扫描捆包',
      });
      return;
    }

    if (carData[0].handType == '30') {
      this.getUnloadingType(carData);
    } else {
      // 直接走配单完成
      this.orderRequest(carData, "");
    }

  },

  // 选择配单号取消
  vehicleNoCancle() {
    this.setData({
      OrderNumberVisible: false,
      vehicleNoList: [],
      vehicleNo: "",
      selectedVehicleNo: '',
    })
  },

  // 选择配单号确定
  vehicleNoConfirm() {
    if (!this.data.selectedAllocateVehicleNo) {
      this.setData({
        showTextAndTitle: true,
        content: '请选择配单号',
      });
      return;
    }

    this.setData({
      OrderNumberVisible: false,
      vehicleNoList: []
    });

    //需要自动提交配单
    let carData = this.data.carList.filter(item => {
      return item.vehicleNo === this.data.carName
    });

    this.orderRequest(carData, this.data.selectedAllocateVehicleNo)
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    setTimeout(() => {
      let query = wx.createSelectorQuery();
      query.select('#fixed-height').boundingClientRect(rect => {
        let height = rect.height;
        this.setData({
          screenHeight: height + 50
        })
      }).exec();
    }, 300);
  },

})