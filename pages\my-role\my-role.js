// pages/my-role/my-role.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    characterList: [],
    current: '',
    userInfoList: [],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let userInfo = wx.getStorageSync("userInfo");
    let userInfoList = wx.getStorageSync("userInfoList");
    const app = getApp();
    const array = userInfoList.map(r => {
      const rIdName = app.getUserName(r.reservationIdentity);
      return { label: rIdName, value: r.reservationIdentity };
    });
    const uniqueArray = array.filter((item, index, self) =>
      index === self.findIndex((t) => t.label === item.label && t.value === item.value)
    );
    
    // 将司机身份排到第一位
    const sortedArray = app.sortRoleList(uniqueArray);
    
    this.setData({
      characterList: sortedArray,
      current: userInfo.reservationIdentity,
      userInfoList,
    });
  },

  onChange(event) {
    const { value } = event.detail;
    this.setData({ current: value });
  },

  /**
   * 确定切换角色
   */
  confirmSwitch() {
    console.log(this.data.current);
    const userInfo = this.data.userInfoList.find(u => u.reservationIdentity == this.data.current);
    if (!userInfo) {
      return;
    }
    wx.setStorage({
      key: "userInfo",
      data: userInfo
    });
    wx.showToast({
      title: '切换角色成功',
      icon: 'success'
    });
    setTimeout(() => {
      wx.navigateBack({
        delta: 1
      });
    }, 1000);
  },
})