<!--pages/user-info/user-info.wxml-->

<view>
  <view>
    <t-cell hover arrow t-class-note="app-t-class" title="当前角色" note="{{reservationIdentityName}}" bind:click="onCharacter" />

    <t-picker visible="{{characterVisible}}" value="{{reservationIdentity}}" title="选择角色" cancelBtn="取消" confirmBtn="确认" usingCustomNavbar bind:confirm="onCharacterChange" bindcancel="onCharacterCancel">
      <t-picker-item options="{{characterList}}"></t-picker-item>
    </t-picker>
  </view>

  <view style="height: 1200rpx;overflow-y: auto;" wx:if="{{userInfo.reservationIdentity != 40}}">
    <view class="card-main" wx:for="{{userList}}" wx:key="index">
      <view class="card-item">
        <view class="label">{{item.reservationIdentity == '10' ? '客户' : '承运商'}}代码:</view>
        <view class="value">{{item.customerId}}</view>
      </view>
      <view class="card-item">
        <view class="label">{{item.reservationIdentity == '10' ? '客户' : '承运商'}}名称:</view>
        <view class="value">{{item.customerName}}</view>
      </view>
      <view class="card-item">
        <view class="label">身份:</view>
        <view class="value">{{item.identityType == '10' ? '管理员' : '司机'}}</view>
      </view>
      <view class="card-item">
        <view class="label">姓名:</view>
        <view class="value">{{ item.driverName }}</view>
      </view>
      <view class="card-item">
        <view class="label">手机号:</view>
        <view class="value">{{item.tel}}</view>
      </view>
      <view class="card-item">
        <view class="label">身份证号:</view>
        <view class="value">{{item.driverIdentity}}</view>
      </view>
    </view>
  </view>

  <view wx:else>
    <view class="card-main" wx:for="{{userList}}" wx:key="index">
      <view class="card-item">
        <view class="label">员工工号:</view>
        <view class="value">{{item.userId}}</view>
      </view>
      <view class="card-item">
        <view class="label">员工姓名:</view>
        <view class="value">{{item.userName}}</view>
      </view>
      <view class="card-item">
        <view class="label">员工手机号:</view>
        <view class="value">{{ item.tel }}</view>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="bottom-container" bind:tap="loginOut">
    退出登录
  </view>
</view>