<!--pages/order-allocation/spot-trading-order/spot-trading-order.wxml-->
<wxs module="threeNumForTwo" src="/utils/tool.wxs"></wxs>
<view class="con">
  <view class="example-search">
    <t-search value="{{ladingBillId}}" placeholder="请输入批次号、终到站地址搜索" action="{{'搜索'}}" bind:action-click="actionHandle" bind:change="changeHandle" bind:submit="actionHandle" bind:clear="clearValue" />
    
    <!-- 客户选择框和筛选按钮在同一行 -->
    <view class="filter-row">
      <view class="customer-selector">
        <t-dropdown-menu style="background-color: transparent;position: initial;">
          <t-dropdown-item 
            options="{{customer.options}}" 
            value="{{customer.value}}" 
            bindchange="onChange"
            disabled="{{customerList.length == 1}}"
          />
        </t-dropdown-menu>
      </view>
      
      <view class="filter-btn-wrapper">
        <t-button icon="filter" variant="text" bindtap="openFilter"></t-button>
      </view>
    </view>
  </view>
  <view class="container">
    <view class="{{dataList.length>0 ? 'scroll-container' : 'dataList-empty' }}">
      <scroll-view class="order-scroll" wx:if="{{dataList.length>0}}" scroll-y="true"   bindscrolltolower="loadMore">
        <view wx:for="{{dataList}}" wx:key="index" wx:for-item="item">
          <view class='order-list' bindtap="gotoDetails" data-listobj="{{item}}">
            <view class="list-name" catchtap="cheackList" data-index="{{index}}">
              <view class="list-name-checked">
                <image src="/assets/image/icon_UncheckBox.png" class='cheacked-img' wx:if="{{!item.flag}}"></image>
                <image src="/assets/image/icon_checkBox.png" class='cheacked-img' wx:if="{{item.flag}}"></image>
              </view>
              <view class="list-name-number">{{item.billId}}</view>
            </view>
            <view class="list-name-start">捆包数：{{item.packCount}}</view>
            <view class="list-name-start">总重：{{item.totalWeight}}</view>
          </view>
        </view>
      </scroll-view>
      <view wx:if="{{!dataList.length>0}}">
        <image src="/assets/image/empty_data.png" class="my-order-img"></image>
        <view class="my-order-text">暂无数据</view>
      </view>
    </view>
    <view class="order-button" id="order-button">
      <view class="whole-order" bindtap="cheackOrder">整单配单</view>
      <view class="whole-order" bindtap="partOrder">部分配单</view>
    </view>
  </view>
  
  <!-- 筛选弹窗 -->
  <t-popup visible="{{filterVisible}}" placement="bottom" usingCustomNavbar bind:visible-change="onFilterVisibleChange">
    <view class="filter-popup">
      <view class="filter-header">
        <view class="filter-title">筛选条件</view>
        <view class="filter-close" bindtap="closeFilter">×</view>
      </view>
      <view class="filter-content">
        <view class="filter-item">
          <view class="filter-label">备注：</view>
          <view class="filter-input">
            <input 
              type="text" 
              placeholder="请输入备注关键词" 
              value="{{filterRemark}}" 
              bindinput="onRemarkInput"
              class="remark-input"
            />
          </view>
        </view>
      </view>
      <view class="filter-footer">
        <view class="filter-reset" bindtap="resetFilter">重置</view>
        <view class="filter-confirm" bindtap="confirmFilter">确定</view>
      </view>
    </view>
  </t-popup>
  
  <t-toast id="t-toast" />
</view> 