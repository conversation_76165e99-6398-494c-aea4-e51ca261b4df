// pages/home-login.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    segNoVisible: false,
    segNoValue: '',
    segNos: [],
    showTextAndTitle: false,
    dialogContent: '',
    confirmBtn: { content: '确定', variant: 'base' },
    showTemporaryReservation: false, // 控制是否显示临时预约按钮
    isContinue: true,
    segNo: '',
    userList: [],
    cityVisible: false,
    cityValue: '',
    citys: [],
    phone: '',
    isChecked: '',
    imageSrc: '/assets/image/小程序分享码-重庆.png',
    isShowbtn: false,
    version: '',
  },

  onLoad() {
    const segNo = getApp().segNo;
    const app = getApp();

    this.setData({
      isShowbtn: 'KF000000' == segNo,
      segNo,
      version: app.globalData.version
    })
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: 'MES',
      path: '/pages/home/<USER>',
    };
  },

  /**
   * 用户点击其他手机号登录事件
   */
  onOtherPhoneLogin() {
    if (!this.data.isChecked) {
      this.setData({
        dialogContent: `请阅读并勾选用户协议`,
        showTextAndTitle: true,
        showTemporaryReservation: false,
      });
      return;
    }
    wx.navigateTo({
      url: 'login-other/login-other',
    });
  },

  radioChange(e) {
    let propName = e.currentTarget.dataset.key;
    let dataToUpdate = {};
    dataToUpdate[propName] = e.detail.value;
    this.setData(dataToUpdate);
  },

  /**
   * 手机号一键登录
   */
  getPhoneNumber(e) {
    if (!this.data.isChecked) {
      this.setData({
        dialogContent: `请阅读并勾选用户协议`,
        showTextAndTitle: true,
        showTemporaryReservation: false,
      });
      return;
    }

    if (!e || !e.detail.code) {
      return;
    }

    wx.showLoading({
      title: '加载中',
    })
    if (this.data.phone) {
      this.getSegNo();
      return;
    }

    const app = getApp();
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        // code: e.detail.code,
        ...e.detail,
        segNo: app.segNo,
        serviceId: 'S_LI_RL_0028',
      },
      success: (response) => {
        wx.hideLoading();
        if (!response || !response.data) {
          this.setData({
            dialogContent: `请求失败，请检查网络`,
            showTextAndTitle: true,
            showTemporaryReservation: false,
          });
          return;
        }

        const result = response.data;
        if (!result.phoneNumber) {
          this.setData({
            dialogContent: `未查询到手机号`,
            showTextAndTitle: true,
            showTemporaryReservation: false,
          });
          return;
        }


        if (result.phoneNumber) {
          this.setData({
            phone: result.phoneNumber,
          });
          this.getSegNo();
        }
      },
      fail: () => {
        wx.hideLoading();
        this.setData({
          dialogContent: `请求失败，请检查网络`,
          showTextAndTitle: true,
          showTemporaryReservation: false,
        });
      },
    });
  },

  onPickerCancel() {
    this.setData({ cityVisible: false, isContinue: false });
  },

  /**
 * 选择账套
 */
  getSegNo() {
    wx.showLoading({
      title: '加载中',
    })
    const app = getApp();
    wx.request({
      url: app.mesUrl, // 接口地址
      method: 'POST', // 请求方法
      data: {
        serviceId: 'S_LI_RL_0023',
      },
      success: (res) => {
        wx.hideLoading();
        if (!res.data || !res.data.list || res.data.list.length == 0) {
          this.setData({
            dialogContent: `请维护账套`,
            showTextAndTitle: true,
            showTemporaryReservation: false,
          });
        }

        const dataList = res.data.list.filter(d => d.segName.includes('重庆'));
        // const dataList = res.data.list;
        this.setData({
          segNoVisible: true,
          segNos: dataList.map(l => {
            return {
              label: l.segName,
              value: l.segNo,
            };
          }),
        })
      },
      fail: () => {
        wx.hideLoading();
        this.setData({
          dialogContent: `请求失败，请检查网络`,
          showTextAndTitle: true,
          showTemporaryReservation: false,
        });
      },
    });
  },

  closeDialog() {
    this.setData({
      showTextAndTitle: false,
      showTemporaryReservation: false,
    });
  },

  // 处理临时预约按钮点击
  onTemporaryReservation() {
    this.setData({
      showTextAndTitle: false,
      showTemporaryReservation: false,
    });
    wx.navigateTo({
      url: '/pages/tourist-reservation/tourist-reservation',
    });
  },

  onPickerChange(e) {
    const { value, label } = e.detail;
    this.setData({
      segNoVisible: false,
      segNoValue: value.join(''),
      segNo: label,
    });

    wx.showLoading({
      title: '加载中',
    })
    const app = getApp();
    wx.request({
      url: app.mesUrl, // 接口地址
      method: 'POST', // 请求方法
      data: {
        phoneNum: this.data.phone, // 请求体内容
        segNo: value.join(''),
        serviceId: 'S_LI_RL_0024',
      },
      success: (res) => {
        wx.hideLoading();
        if (res?.data?.__sys__?.status != -1) {
          const { data } = res;
          const resultList = data.list.map(m => {
            m.eiMetadata = "";
            return m;
          });

          // 存储完整的响应数据，包含 lease 字段
          wx.setStorage({
            key: 'loginResponseData',
            data: data
          });

          this.setData({
            userList: resultList,
          });
          wx.setStorage({
            key: 'auditList',
            data: data.auditList
          });
          wx.setStorage({
            key: "userInfoList",
            data: resultList,
          });
          if (resultList.length == 1) {
            wx.setStorageSync('userLoggedIn', true);  // 记录用户已登录
            let resultData = resultList[0];
            wx.setStorage({
              key: "userInfo",
              data: resultData
            });
            
            // 检查 lease 字段，如果是 "30" 则跳转到常驻车页面
            if (data.lease === "30") {
              wx.reLaunch({
                url: '/pages/resident-car/resident-car',
              });
            } else {
              wx.reLaunch({
                url: '/pages/index/index',
              });
            }
            return;
          }
          // 弹框让用户自行选择
          const sfList = resultList.map(r => {
            let rIdName = app.getUserName(r.reservationIdentity);
            return { label: rIdName, value: r.reservationIdentity };
          });
          const uniqueArray = sfList.filter((item, index, self) =>
            index === self.findIndex((t) => t.label === item.label && t.value === item.value)
          );
          
          // 将司机身份排到第一位
          const sortedArray = app.sortRoleList(uniqueArray);
          
          this.setData({
            cityVisible: true,
            citys: sortedArray,
          });
          wx.setStorage({
            key: "userInfoList",
            data: resultList,
          });
          return;
        }

        // 处理返回的错误
        const errorMsg = res?.data?.__sys__?.msg || '';
        const showTemporaryBtn = errorMsg.includes('不存在');

        this.setData({
          dialogContent: errorMsg,
          showTextAndTitle: true,
          showTemporaryReservation: showTemporaryBtn,
        });
      },
      fail: () => {
        wx.hideLoading();
        this.setData({
          dialogContent: `请求失败，请检查网络`,
          showTextAndTitle: true,
          showTemporaryReservation: false,
        });
      },
    });
  },

  onCityCancel() {
    this.setData({ cityVisible: false });
  },

  onCityChange(e) {
    let { value } = e.detail;

    const userInfo = this.data.userList.find(u => u.reservationIdentity == value.join(''));
    if (!userInfo) {
      return;
    }

    wx.setStorage({
      key: "userInfo",
      data: userInfo
    });
    wx.setStorageSync('userLoggedIn', true);  // 记录用户已登录
    
    // 检查存储的响应数据中的 lease 字段
    const responseData = wx.getStorageSync('loginResponseData');
    if (responseData && responseData.lease === "30") {
      wx.reLaunch({
        url: '/pages/resident-car/resident-car',
      });
    } else {
      wx.reLaunch({
        url: '/pages/index/index',
      });
    }
  },

  /**
   * 管理员操作指引下载
   */
  downloadAdminGuide() {
    wx.showLoading({ title: '加载中...' });
    const app = getApp();
    wx.request({
      url: app.mesUrl,
      method: 'post',
      data: {
        flag: '1', // 管理员
        serviceId: 'S_LI_RL_0111',
        segNo: app.segNo,
      },
      success: (res) => {
        console.log(res.data);
        if (!res || !res.data) {
          wx.showToast({ title: '请求失败', icon: 'error' });
          return;
        }

        const resData = res.data;
        if (resData.__sys__.status == -1) {
          wx.showToast({ title: resData.__sys__.msg, icon: 'error' });
          return;
        }
        // 获取二进制数据
        const arrayBuffer = resData.fileContent;
        this.saveAndOpenPDF(arrayBuffer, '操作指引');
      },
      fail: (err) => {
        wx.showToast({ title: '网络错误', icon: 'none' });
        console.error('请求失败:', err);
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 保存并打开 PDF
  saveAndOpenPDF(arrayBuffer, fileName) {
    const fs = wx.getFileSystemManager();
    const tempPath = `${wx.env.USER_DATA_PATH}/${fileName}.pdf`;

    // 将二进制数据写入临时文件
    fs.writeFile({
      filePath: tempPath,
      data: arrayBuffer,
      encoding: 'base64', // 关键：声明二进制编码
      success: () => {
        // 打开文档
        wx.openDocument({
          filePath: tempPath,
          fileType: 'pdf',
          showMenu: true,
          success: () => console.log('打开成功'),
          fail: (err) => {
            wx.showToast({ title: '打开失败', icon: 'none' });
            console.error('打开失败:', err);
          }
        });
      },
      fail: (err) => {
        wx.showToast({ title: '保存失败', icon: 'none' });
        console.error('写入文件失败:', err);
      }
    });
  },

  /**
   * 手动检查更新
   */
  checkUpdate() {
    // 开发环境下的模拟测试
    if (typeof wx.getUpdateManager === 'function') {
      wx.showLoading({
        title: '检查更新中...',
      });

      const updateManager = wx.getUpdateManager();

      updateManager.onCheckForUpdate((res) => {
        wx.hideLoading();
        if (res.hasUpdate) {
          wx.showToast({
            title: '发现新版本，正在下载...',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: '当前已是最新版本',
            icon: 'success'
          });
        }
      });

      updateManager.onUpdateReady(() => {
        wx.hideLoading();
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否立即重启应用？',
          success: (res) => {
            if (res.confirm) {
              // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
              updateManager.applyUpdate();
            }
          }
        });
      });

      updateManager.onUpdateFailed(() => {
        wx.hideLoading();
        wx.showToast({
          title: '更新失败，请稍后重试',
          icon: 'none'
        });
      });
    } else {
      // 开发者工具中的模拟提示
      wx.showModal({
        title: '模拟更新测试',
        content: '当前在开发者工具中，无法真实测试更新功能。请发布体验版或正式版后在手机微信中测试。',
        showCancel: false,
        confirmText: '知道了'
      });
    }
  },
})