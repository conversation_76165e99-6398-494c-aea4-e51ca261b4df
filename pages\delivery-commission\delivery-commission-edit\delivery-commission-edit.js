// pages/delivery-commission/delivery-commission-edit/delivery-commission-edit.js
import Toast from 'tdesign-miniprogram/toast/index';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    delivery: '',
    name: '',
    nameQueryVisible: false,
    nameVisible: false,
    nameValue: '',
    nameList: [],
    driverList: [],
    nameQuery: '',
    carNumber: '',
    carNumberVisible: false,
    carNumberValue: '',
    vehicleNoList: [],
    showTextAndTitle: false,
    content: '', // 提示信息
    confirmBtn: { content: '确定', variant: 'base' },
    customerName: '',
    deliveryNo: '',
    customerId: '',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const eventChannel = this.getOpenerEventChannel();
    if (!eventChannel) {
      return;
    }
    eventChannel.emit('acceptDataFromOpenedPage', { data: '' });
    eventChannel.on('acceptDataFromOpenerPage', (data) => {
      const delivery = data.data;
      const name = delivery.driverName || delivery.driver;
      // 显示转库单还是提单
      const deliveryNo = delivery.ladingBillId || delivery.transBillId;
      // 提单
      let customerName = delivery.tproviderName;
      let customerId = delivery.tproviderId;
      if (deliveryNo.includes('BL') && delivery.deliveryType == '10') {
        customerName = delivery.userName;
        customerId = delivery.userNum
      } else {
        // 转库单
        if (delivery.deleiveryType == '10') {
          customerName = delivery.packSettleUserName;
          customerId = delivery.packSettleUserNum;
        }
      }
      this.setData({
        delivery,
        name,
        deliveryNo,
        customerName,
        customerId,
        tel: delivery.driverPhone,
        carNumber: delivery.vehicleNo,
      });
      this.getName();
    });


  },

  /**
 * 隐藏选择司机弹出框
 */
  onNameQueryChange() {
    this.setData({
      nameQueryVisible: false,
      nameVisible: false,
    });
  },

  /**
 * 打开车牌号选择框
 */
  onCarNumberPicker() {
    this.setData({
      carNumberVisible: true,
    });
  },

  /**
* 搜索司机
* @param {*} e 
*/
  nameQueryChange(e) {
    const { value } = e.detail;
    const nameList = this.data.driverList.filter(a => a.driverName.includes(value)).map(r => {
      return {
        label: r.driverName,
        value: r.driverIdentity,
      };
    });
    this.setData({
      nameList,
    })
  },

  /**
 * 选择框确认事件
 * @param {*} e 
 */
  onPickerChange(e) {
    const { value, label } = e.detail;
    const { key } = e.currentTarget.dataset;
    this.setData({
      [`${key}Visible`]: false,
      [`${key}Value`]: value,
      [`${key}`]: label.join(''),
    });

    if ('name'.includes(key)) {
      const driver = this.data.driverList.find(d => d.driverIdentity == value);
      this.setData({
        tel: driver.tel,
        nameQuery: '',
        nameQueryVisible: false,
        driverIdentity: driver.driverIdentity,
        carNumber: driver.vehicleNo.length == 1 ? driver.vehicleNo[0] : '',
        vehicleNoList: driver.vehicleNo.map(v => {
          return {
            label: v,
            value: v,
          };
        }),
      });
      return;
    }

  },

  /**
 * 关闭错误提示框
 */
  closeDialog() {
    this.setData({
      showTextAndTitle: false,
    });
  },

  /**
 * 打开司机选择框
 */
  onNamePicker() {
    const app = getApp();
    wx.showLoading({
      title: '加载中',
    })
    const user = wx.getStorageSync('userInfo');
    const { MOBILE, tel, reservationIdentity } = user;
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        serviceId: 'S_LI_RL_0119',
        segNo: this.data.delivery.segNo,
        driverTel: tel || MOBILE,
        customerId: this.data.customerId,
        reservationIdentity,
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          Toast({
            context: this,
            selector: '#t-toast',
            message: '网络异常, 请稍后重试',
            theme: 'warning',
            direction: 'column',
          });
          return;
        }

        const result = res.data;
        if (result.__sys__?.status == -1) {
          Toast({
            context: this,
            selector: '#t-toast',
            message: result.__sys__.msg,
            theme: 'error',
            direction: 'column',
          });
          return;
        }

        const resultList = result.list.map(r => {
          return {
            label: r.driverName,
            value: r.driverIdentity,
          }
        });

        this.setData({
          nameList: resultList,
          driverList: result.list,
          nameVisible: true,
          nameQueryVisible: true,
        });
      },
      fail: () => {
        wx.hideLoading();
        Toast({
          context: this,
          selector: '#t-toast',
          message: '网络异常, 请稍后重试',
          theme: 'warning',
          direction: 'column',
        });
      }
    });

  },

  /**
   * 页面初始化查询司机信息
   */
  getName() {
    const app = getApp();
    wx.showLoading({
      title: '加载中',
    })
    const user = wx.getStorageSync('userInfo');
    const { MOBILE, tel, reservationIdentity } = user;
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        serviceId: 'S_LI_RL_0119',
        segNo: this.data.delivery.segNo,
        driverTel: tel || MOBILE,
        customerId: this.data.customerId,
        reservationIdentity,
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          Toast({
            context: this,
            selector: '#t-toast',
            message: '网络异常, 请稍后重试',
            theme: 'warning',
            direction: 'column',
          });
          return;
        }

        const result = res.data;
        if (result.__sys__?.status == -1) {
          Toast({
            context: this,
            selector: '#t-toast',
            message: result.__sys__.msg,
            theme: 'error',
            direction: 'column',
          });
          return;
        }

        const driver = result.list.find(r => r.driverName == this.data.name);
        if (driver) {
          const { driverIdentity, driverName, tel, vehicleNo } = driver;
          const carNum = vehicleNo.find(v => v == this.data.carNumber);
          this.setData({
            name: driverName,
            driverIdentity,
            tel,
            nameValue: [driverIdentity],
            carNumber: carNum,
            carNumberValue: [carNum],
            vehicleNoList: vehicleNo.map(v => {
              return {
                label: v,
                value: v,
              };
            }),
          });
          return;
        }

      },
      fail: () => {
        wx.hideLoading();
        Toast({
          context: this,
          selector: '#t-toast',
          message: '网络异常, 请稍后重试',
          theme: 'warning',
          direction: 'column',
        });
      }
    });
  },

  /**
   * 修改
   */
  editDelivery() {
    const { name, carNumber, delivery, driverIdentity, tel, } = this.data;
    const user = wx.getStorageSync('userInfo');
    const { reservationIdentity } = user;
    if (!name || !name.trim()) {
      this.setData({
        showTextAndTitle: true,
        content: `请选择司机`,
      });
      return;
    }

    if (!carNumber) {
      this.setData({
        showTextAndTitle: true,
        content: `请选择车牌号`,
      });
      return;
    }
    const app = getApp();
    wx.showLoading({
      title: '加载中',
    });

    const list = {
      ...delivery,
      transBillId: delivery.ladingBillId || delivery.transBillId,
      vehicleNo: carNumber,
      reservationIdentity,
      driverName: name,
      driverIdentity,
      driverTel: tel,
    };

    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        serviceId: 'S_LI_RL_0121',
        list,
        // driverIdentity: user.driverIdentity,
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          Toast({
            context: this,
            selector: '#t-toast',
            message: '网络异常, 请稍后重试',
            theme: 'warning',
            direction: 'column',
          });
          return;
        }

        const result = res.data;
        if (result.__sys__?.status == -1) {
          Toast({
            context: this,
            selector: '#t-toast',
            message: result.__sys__.msg,
            theme: 'error',
            direction: 'column',
          });
          return;
        }
        Toast({
          context: this,
          selector: '#t-toast',
          message: result.__sys__.msg,
          theme: 'success',
          direction: 'column',
        });

        setTimeout(() => {
          wx.navigateBack();
        }, 1000);
      },
      fail: () => {
        wx.hideLoading();
        Toast({
          context: this,
          selector: '#t-toast',
          message: '网络异常, 请稍后重试',
          theme: 'warning',
          direction: 'column',
        });
      }
    });
  },
})