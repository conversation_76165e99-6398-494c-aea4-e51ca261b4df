<view class="theme-card">
  <t-checkbox-group bind:change="onChange" value="{{value}}" options="{{options}}">
  </t-checkbox-group>
</view>

<view class="bottom-container">
  <!-- <text></text> -->
  <t-divider content="请拍照纸质签收单并上传" t-class-content="t-class-content1" />
  <view class="plate-button-wrapper">
    <view class="wrapper">
      <t-upload media-type="{{['image']}}" files="{{originFiles}}" gridConfig="{{gridConfig}}" bind:success="handleSuccess" bind:remove="handleRemove" />
    </view>

    <t-button wx:if="{{isDisable}}" block theme="primary" size="large" bind:tap="separatUploadImg">
      上传
    </t-button>

    <t-button block theme="primary" size="large" bind:tap="packSave" disabled="{{isDisable}}">
      部分捆包签收
    </t-button>
    <t-button block theme="primary" size="large" bind:tap="entirOrderSign" disabled="{{isDisable}}">
      整单签收
    </t-button>
  </view>

</view>

<!-- 弹出写字板 -->
<t-popup visible="{{showPopup}}" bind:close="togglePopup" placement="center">
  <view class="canvas-container">
  <text>收货仓库收货人签名</text>
    <canvas id="myCanvas" style="width: 300px; height: 350px; border: 1px solid #ccc;" type="2d" bindtouchstart="startDraw" bindtouchmove="moveDraw" bindtouchend="endDraw"></canvas>
    <view class="buttons">
      <!-- <button bindtap="clearCanvas">清空重写</button> -->
      <t-button size="large" theme="danger" bindtap="clearCanvas">清空重写</t-button>
      <!-- <button bindtap="saveCanvas">确认提交</button> -->
      <t-button size="large" theme="primary" bindtap="saveCanvas">确认提交</t-button>
    </view>
  </view>
  <view class="block">
    <t-icon t-class="close-btn" name="close-circle" size="64rpx" color="#fff" bind:tap="onClose" />
  </view>
</t-popup>

<t-dialog visible="{{showTextAndTitle}}" title="提示" content="{{content}}" confirm-btn="{{ confirmBtn }}" bind:confirm="closeDialog" />

<t-toast id="t-toast" />

