// pages/driver/driver-edit/driver-edit.js
import Message from 'tdesign-miniprogram/message/index';
import Toast from 'tdesign-miniprogram/toast/index';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    driver: '',
    driverName: '',
    showTextAndTitle: false,
    content: '', // 提示信息
    confirmBtn: { content: '确定', variant: 'base' },
    driverIdentity: '',
    tel: '',
    vehicleNo: [],
    showWithInput: false,
    dialogCarNum: "",
    showWarnConfirm: false,
    code: '',
    isFocus: false,
    reservationIdentityName: '',
    statusName: '',
    activeIndex: 0, // 子组件是否高亮
    codeArray: ['', '', '', '', '', '', '', '新能源'], // 新增车牌成功之后, 清空车牌
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const user = wx.getStorageSync('userInfo');
    this.setData({
      reservationIdentityName: user.reservationIdentity == '10' ? '客户' : '承运商',
    })
    const eventChannel = this.getOpenerEventChannel();
    if (!eventChannel) {
      return;
    }
    eventChannel.emit('acceptDataFromOpenedPage', { data: '' });
    eventChannel.on('acceptDataFromOpenerPage', (data) => {
      const resultData = data.data;
      this.setData({
        driver: data.data,
        tel: resultData.tel,
        driverIdentity: resultData.driverIdentity,
        vehicleNo: resultData.vehicleNo,
        driverName: resultData.driverName,
        statusName: resultData.status,
      });
    });
  },


  /**
 * input框伪双向绑定
 * @param {*} e 
 */
  onInputChang(e) {
    const { key } = e.currentTarget.dataset;
    const { value } = e.detail;
    this.setData({
      [key]: value,
    });
  },

  /** 子页面方法 */
  onInputComplete(event) {
    const { plate } = event.detail; // 获取子组件传递的车牌号
    console.log('车牌号:', plate); // 打印或处理车牌号

    // 更新父页面的 carNumber 数据
    this.setData({
      dialogCarNum: plate
    });
  },

  /**
   * 打开删除确认框
   */
  delete() {
    this.setData({
      showWarnConfirm: true,
    });

  },

  /**
   * 确认删除
   * @param {*} e 
   */
  confirmDelete(e) {
    this.setData({
      showWarnConfirm: false,
    });
    if (e.type == 'cancel') {
      return;
    }

    wx.showLoading({
      title: '删除中',
    });
    const { tel, driverIdentity, vehicleNo, driver, driverName } = this.data;
    const app = getApp();
    const result = {
      segNo: driver.segNo,
      unitCode: driver.segNo,
      customerId: driver.customerId,
      customerName: driver.customerName,
      driverName,
      tel,
      driverIdentity,
      recCreator: driver.administrator,
      recCreatorName: driver.administrator,
      uuid: driver.uuid,
      vehicleNo,
    };

    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        serviceId: 'S_LI_RL_0014',
        result,
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          this.setData({
            showTextAndTitle: true,
            content: '网络异常, 请稍后重试',
          });
          return;
        }

        // 业务逻辑
        const result = res.data;
        if (result?.__sys__?.status == -1) {
          this.setData({
            showTextAndTitle: true,
            content: result?.__sys__?.msg,
          });
          return;
        }

        const msg = result.__sys__.msg;
        if (msg.includes('预约')) {
          this.setData({
            showTextAndTitle: true,
            content: msg,
          });
          return;
        }

        Toast({
          context: this,
          selector: '#t-toast',
          message: '删除成功',
          theme: 'success',
          direction: 'column',
        });

        // 删除成功之后回退上一页
        wx.navigateBack();
      },
      fail: (res) => {
        wx.hideLoading();
        this.setData({
          showTextAndTitle: true,
          content: '网络异常, 请稍后重试',
        });
      },
    });

  },

  /**
   * 保存
   */
  save() {
    // 判断手机号是否正确
    const { tel, driverIdentity, vehicleNo, code, driver, driverName } = this.data;
    const app = getApp();
    if (!app.isPhone(tel)) {
      this.setData({
        showTextAndTitle: true,
        content: '请填写正确的手机号',
      });
      return;
    }

    const isIdCardPattern = /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/.test(driverIdentity);
    if (!isIdCardPattern) {
      this.setData({
        showTextAndTitle: true,
        content: '请填写正确的身份证号',
      });
      return;
    }

    wx.showLoading({
      title: '保存中',
    });
    // 拼装数据
    const user = wx.getStorageSync('userInfo');
    const result = {
      segNo: driver.segNo,
      unitCode: driver.segNo,
      customerId: driver.customerId,
      customerName: driver.customerName,
      driverName,
      tel,
      driverIdentity,
      recCreator: user.tel,
      recCreatorName: user.administrator,
      uuid: driver.uuid,
      vehicleNo: vehicleNo[0],
      messageCode: code,
    };

    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        serviceId: 'S_LI_RL_0013',
        result,
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          this.setData({
            showTextAndTitle: true,
            content: '网络异常, 请稍后重试',
          });
          return;
        }

        // 业务逻辑
        const result = res.data;
        if (result?.__sys__?.status == -1) {
          this.setData({
            showTextAndTitle: true,
            content: result?.__sys__?.msg,
          });
          return;
        }

        const { tel, driverIdentity, driverName, status } = result.result;
        this.setData({
          vehicleNo: result.vehicleNo,
          tel,
          driverIdentity,
          driverName,
          code: '',
          statusName: status,
        });

        Toast({
          context: this,
          selector: '#t-toast',
          message: '保存成功',
          theme: 'success',
          direction: 'column',
        });
      },
      fail: (res) => {
        wx.hideLoading();
        this.setData({
          showTextAndTitle: true,
          content: '网络异常, 请稍后重试',
        });
      },
    });

  },

  /**
   * 显示增加车牌号弹出框
   */
  showDialog() {
    this.setData({
      showWithInput: true,
    });

    // 使用 setTimeout 延时确保弹框完全打开后聚焦
    setTimeout(() => {
      this.setData({
        isFocus: true
      });
    }, 200);
  },

  /**
 * 关闭增加车牌号弹出框
 */
  closeInsertCarDialog(e) {
    if (e.type != 'confirm') {
      this.setData({
        showWithInput: false,
      });
      return;
    }
    const app = getApp();
    let isCarNumber = app.isCarNumb(this.data.dialogCarNum);
    if (!isCarNumber) {
      Message.error({
        context: this,
        offset: [90, 32],
        duration: 5000,
        content: `车牌号错误, 请检查`,
      });
      return;
    }

    this.setData({
      showWithInput: false,
    });
    wx.showLoading({
      title: '加载中',
    });

    // 拼装数据
    let { vehicleNo } = this.data;
    vehicleNo.push(this.data.dialogCarNum);
    this.updateVehicleNo(this.data.dialogCarNum, '增加');
  },

  /**
   * 删除车牌
   */
  removeVehicleNo(e) {
    if (this.data.vehicleNo.length == 1) {
      this.setData({
        showTextAndTitle: true,
        content: '必须保留至少一个车牌号',
      });
      return;
    }

    const keyIndex = e.currentTarget.dataset.key;

    let carNum = this.data.vehicleNo[keyIndex];
    this.updateVehicleNo(carNum, '删除');
  },

  /**
   * 提交修改车牌
   * @param {*} e 
   */
  updateVehicleNo(vehicleNo, typeMessage) {
    wx.showLoading({
      title: '加载中',
    });

    // 拼装数据
    let { driver } = this.data;

    const user = wx.getStorageSync('userInfo');
    const app = getApp();
    let result = {
      segNo: driver.segNo,
      unitCode: driver.segNo,
      customerId: driver.customerId,
      customerName: driver.customerName,
      driverName: this.data.driverName,
      tel: this.data.tel,
      driverIdentity: this.data.driverIdentity,
      vehicleNo,
      recCreator: user.tel,
      recCreatorName: user.administrator,
      uuid: driver.uuid,
      isVhicleNo: 1,
    };

    if (typeMessage == '删除') {
      result = {
        ...result,
        deFlag: 1
      }
    }

    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        serviceId: 'S_LI_RL_0013',
        result,
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          this.setData({
            showTextAndTitle: true,
            content: '网络异常, 请稍后重试',
          });
          return;
        }

        // 业务逻辑
        const result = res.data;
        if (result?.__sys__?.status == -1) {
          this.setData({
            showTextAndTitle: true,
            content: result?.__sys__?.msg,
          });
          return;
        }

        this.setData({
          vehicleNo: result.vehicleNo,
        });
        Toast({
          context: this,
          selector: '#t-toast',
          message: `${typeMessage}车牌成功`,
          theme: 'success',
          direction: 'column',
        });
        this.setData({
          codeArray: ['', '', '', '', '', '', '', '新能源'],
          activeIndex: 0,
        });
      },
      fail: (res) => {
        wx.hideLoading();
        this.setData({
          showTextAndTitle: true,
          content: '网络异常, 请稍后重试',
        });
      },
    });
  },

  /**
 * 关闭错误提示框
 */
  closeDialog() {
    this.setData({
      showTextAndTitle: false,
    });

    if (this.data.content.includes('系统自动取消')) {
      wx.navigateBack();
    }
  },

  /**
 * 发送验证码
 */
  sendCode() {
    // 判断手机号是否正确
    const { tel, driver, driverName } = this.data;
    const isPhoneNumber = /^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(tel);
    if (!isPhoneNumber) {
      this.setData({
        showTextAndTitle: true,
        content: '请填写正确的手机号',
      });
      return;
    }

    wx.showLoading({
      title: '发送中',
    })
    const app = getApp();
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        serviceId: 'S_LI_RL_0017',
        driverTel: tel,
        driverName: driverName,
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          this.setData({
            showTextAndTitle: true,
            content: '网络异常, 请稍后重试',
          });
          return;
        }

        const result = res.data;
        if (result.__sys__?.status == -1) {
          this.setData({
            showTextAndTitle: true,
            content: result.__sys__.msg,
          });
          return;
        }

        Toast({
          context: this,
          selector: '#t-toast',
          message: '发送成功',
          theme: 'success',
          direction: 'column',
        });
      },
    });
  },
})