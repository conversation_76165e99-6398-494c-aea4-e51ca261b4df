/* pages/order-allocation/loading-order-detail/loading-order-detail.wxss */
/* page{
    width: 100%;
    height: 100%;
} */
.container{
    width: 100%;
    height: 100%;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    box-sizing: border-box;
}
.scroll-container{
    width: 100%;
    height: calc(100% - 100rpx);
    flex-grow:1;
}
.order-scroll{ 
    width: 100%;
    height: 100%;
    overflow: auto;
 }
.dataList-empty{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.order-list{
    width:calc(100% - 64rpx);
    padding:20rpx 30rpx;
    background-color: #ffffff;
    margin: 0 auto 20rpx;
    border-radius: 20rpx;
    box-sizing: border-box;
}
.list-name{
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    align-content: center;
    margin-bottom: 10rpx;
}
.list-name-checked image{
    width: 36rpx;
    height: 36rpx;
    display: flex;
    align-items: center;
}
.list-name-number{
    font-size: 32rpx;
    color: #333333;
    font-weight: 500;
    margin-left: 10rpx;
}
.list-name-start{
    font-size: 28rpx;
    color: #666666;
    line-height: 40rpx;
    margin-bottom: 10rpx;
}
.list-name-cloum{
    width: 100%;
    font-size: 28rpx;
    color: #666666;
    line-height: 40rpx;
    display:flex;
    flex-direction: row;
    flex-wrap: wrap;
}
.list-name-cloum-one{
    flex:0 0 50%;
    margin-bottom: 10rpx;
}

.bomTxt {
display: flex;
justify-content: center;
font-size: 12px;
color: rgb(126, 138, 155);
padding: 0rpx 0rpx 20rpx 0rpx;
}
.order-button{
    width: 100%;
    height: 100rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
} 
.whole-order{
    width: 80%;
    height:64rpx;
    background-color:#1953E6;
    color: #ffffff;
    text-align: center;
    line-height: 64rpx;
    margin: 0 auto;
    font-size: 28rpx;
    border-radius: 8rpx;
}
.my-order-img{
    width: 150px;
    height: 150px;
    display: block;
}
.my-order-text{
    text-align: center;
	font-size: 16px;
	color: rgb(51, 51, 51);
}