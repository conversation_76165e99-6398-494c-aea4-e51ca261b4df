<view class="example-search">
  <t-search value="{{value}}" placeholder="搜索装卸点, 按照司机、车牌号" action="{{'搜索'}}" bind:action-click="actionHandle" bind:change="changeHandle" bind:submit="actionHandle" bind:clear="clearValue" />

  <!-- <t-dropdown-menu style="width: 15em; background-color: transparent;position: initial;">
    <t-dropdown-item options="{{customer.options}}" value="{{customer.value}}" bindchange="onChange" />
  </t-dropdown-menu> -->
</view>

<!-- 状态筛选器 -->
<view class="status-filter">
  <view class="filter-label">状态筛选：</view>
  <view class="filter-buttons">
    <view class="filter-btn {{status1Active ? 'active' : ''}}" bindtap="toggleStatus" data-status="20">
      生效
    </view>
    <view class="filter-btn {{status0Active ? 'active' : ''}}" bindtap="toggleStatus" data-status="99">
      完成
    </view>
  </view>
</view>

<scroll-view scroll-y style="height: calc(100vh - 350rpx);" scroll-top="{{scrollTop}}">
  <view wx:if="{{wasteMaterialList.length > 0}}">
    <view class="card-main" data-index="{{index}}" wx:for="{{wasteMaterialList}}" wx:key="index">
      <view class="card-header">
        <view class="vehicle-info">
          <view class="label">车牌号/司机:</view>
          <view class="value">
            <view class="vehicle-text">{{item.vehicleNo}}/{{item.driverName}}              <t-tag theme="{{item.status == '20' ? 'success' : 'danger'}}" size="small">
                {{item.status == '20' ? '生效' : '完成'}}
              </t-tag></view>
          </view>
        </view>

      </view>
      <view class="card-item">
        <view class="label">装卸点:</view>
        <view class="loading-points-container">
          <view class="loading-points">
            <view class="point-tag" wx:for="{{item.handPointList}}" wx:key="targetHandPointId" wx:for-item="point">
              {{point.handPointName}}
            </view>
            <view wx:if="{{!item.handPointList || item.handPointList.length === 0}}" class="no-points">暂无</view>
          </view>
        </view>
      </view>
      <view class="card-item">
        <view class="label">创建时间:</view>
        <view class="value">{{item.dateStr}}</view>
      </view>

      <view class="button-container">
        <t-button class="action-button" size="medium" theme="primary" variant="outline" catchtap="editItem" data-index="{{index}}">修改装卸点</t-button>
        <t-button class="action-button" size="medium" theme="success" variant="outline" catchtap="startQueue" data-item="{{item}}" data-index="{{index}}">启动排队</t-button>
        <t-button class="action-button" size="medium" theme="danger" variant="outline" catchtap="cancelQueue" data-item="{{item}}" data-index="{{index}}">取消排队</t-button>
      </view>
    </view>
  </view>

  <!-- 无数据提示 -->
  <view wx:else class="empty-container">
    <t-empty icon="file-image" description="暂无装卸点记录" />
  </view>

  <t-dialog visible="{{showTextAndTitle}}" title="提示" content="{{content}}" confirm-btn="{{ confirmBtn }}" bind:confirm="closeDialog" />
</scroll-view>

<!-- 装卸点多选对话框 -->
<t-dialog visible="{{loadingPointVisible}}" title="修改装卸点" cancel-btn="取消" confirm-btn="确认" bind:confirm="confirmLoadingPoints" bind:cancel="cancelLoadingPoints">
  <view slot="content">
    <scroll-view scroll-y class="dialog-content" enhanced show-scrollbar="{{false}}" bindtouchstart="true">
      <t-checkbox-group value="{{selectedLoadingPointValue}}" bind:change="onLoadingPointChange">
        <t-checkbox wx:for="{{loadingPointList}}" wx:key="value" value="{{item.value}}" label="{{item.label}}" />
      </t-checkbox-group>
    </scroll-view>
  </view>
</t-dialog>

<!-- Toast 提示 -->
<t-toast id="t-toast" />
<t-message id="t-message" />