// pages/driver/driver-insert.js
import Toast from 'tdesign-miniprogram/toast/index';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    phoneNumber: "",
    customerCodeText: '请选择客户',
    customerCodeValue: '',
    customerCodeVisible: false,
    customerCodeList: [],
    customerName: '客户名称',
    name: '',
    tel: '',
    idCard: '',
    carNumber: '',
    code: '', // 验证码
    userInfo: '', // 用户信息
    showTextAndTitle: false,
    content: '', // 提示信息
    confirmBtn: { content: '确定', variant: 'base' },
    reservationIdentityName: '',
    activeIndex: -1, // 子组件是否高亮
    smartInputText: '', // 智能输入框文本
    smartInputFocused: false, // 智能输入框是否聚焦
    carPlateArray: ['', '', '', '', '', '', '', '新能源'], // 车牌输入数组
    keyboardHeight: 0, // 键盘高度
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const user = wx.getStorageSync('userInfo');
    const { segNo, administrator, tel, reservationIdentity } = user;
    let userInfoList = wx.getStorageSync('userInfoList').filter(u => u.reservationIdentity == reservationIdentity);
    const list = userInfoList.map(m => {
      return {
        label: `${m.customerId}-${m.customerName}`,
        value: m.customerId,
      };
    });
    const rName = reservationIdentity == '10' ? '客户' : '承运商';
    this.setData({
      userInfo: user,
      reservationIdentityName: rName,
      customerCodeText: `请选择${rName}`,
      customerName: `${rName}名称`
    });

    if (list.length > 1) {
      this.setData({
        customerCodeList: list,
      });
      return;
    }

    const resultData = userInfoList[0];
    this.setData({
      customerCodeText: resultData.customerId,
      customerCodeValue: resultData.customerId,
      customerName: resultData.customerName,
      customerCodeList: list,
    });
  },

  onPage(e) {
    const isInside = e.target.dataset.key == "car-number-div";
    if (!isInside) {
      // 如果点击的是车牌输入框外部
      this.setData({
        activeIndex: -1, // 移除高亮
      });
    }
  },

  /** 子页面方法 */
  onInputComplete(event) {
    const { plate } = event.detail; // 获取子组件传递的车牌号
    console.log('车牌号:', plate); // 打印或处理车牌号

    // 更新父页面的 carNumber 数据
    this.setData({
      carNumber: plate
    });
  },

  /**
   * 车牌输入键盘显示
   */
  onKeyboardShow(event) {
    const { keyboardHeight } = event.detail;
    this.setData({
      keyboardHeight: keyboardHeight || 350
    });

    // 增加底部间距后，自动滚动到页面底部，确保车牌输入区域可见
    setTimeout(() => {
      wx.pageScrollTo({
        scrollTop: 999999, // 滚动到页面底部
        duration: 300
      });
    }, 100); // 稍微延迟，等待页面布局更新
  },

  /**
   * 车牌输入键盘隐藏
   */
  onKeyboardHide() {
    this.setData({
      keyboardHeight: 0
    });
  },

  /**
   * 显示承运商/客户代码选择框
   */
  onCustomerCodePicker() {
    this.setData({
      customerCodeVisible: true,
    });
  },

  /**
   * 确定客户代码
   * @param {*} e 
   */
  onCustomerCodeChange(e) {
    const { value, label } = e.detail;
    const arr = label[0].split('-');

    this.setData({
      customerCodeVisible: false,
      customerCodeValue: value,
      customerCodeText: value.join(' '),
      customerName: arr[1],
    });
  },

  /**
   * input框伪双向绑定
   * @param {*} e
   */
  onInputChang(e) {
    const { key } = e.currentTarget.dataset;
    const { value } = e.detail;
    this.setData({
      [key]: value,
    });
  },

  /**
   * 智能输入框内容变化
   * @param {*} e
   */
  onSmartInputChange(e) {
    const { key } = e.currentTarget.dataset;
    const { value } = e.detail;
    this.setData({
      [key]: value,
    });
  },

  /**
   * 清空智能输入框
   */
  onClearSmartInput() {
    this.setData({
      smartInputText: '',
    });
  },

  /**
   * 智能输入框聚焦
   */
  onSmartInputFocus() {
    this.setData({
      smartInputFocused: true,
    });
  },

  /**
   * 智能输入框失焦
   */
  onSmartInputBlur() {
    this.setData({
      smartInputFocused: false,
    });
  },

  /**
   * 智能解析文本
   */
  onSmartParse() {
    const { smartInputText } = this.data;
    if (!smartInputText.trim()) {
      return;
    }

    const parsedData = this.parseSmartInput(smartInputText);

    // 更新表单数据
    const updateData = {};
    console.log(parsedData);
    if (parsedData.name) updateData.name = parsedData.name;
    if (parsedData.tel) updateData.tel = parsedData.tel;
    if (parsedData.idCard) updateData.idCard = parsedData.idCard;
    if (parsedData.carNumber) {
      updateData.carNumber = parsedData.carNumber;
      // 如果识别到车牌号，需要更新车牌输入组件的显示
      this.updateCarPlateDisplay(parsedData.carNumber);
    }

    this.setData(updateData);

    // 显示解析结果提示
    const results = [];
    if (parsedData.name) results.push(`姓名: ${parsedData.name}`);
    if (parsedData.tel) results.push(`电话: ${parsedData.tel}`);
    if (parsedData.idCard) results.push(`身份证: ${parsedData.idCard}`);
    if (parsedData.carNumber) results.push(`车牌: ${parsedData.carNumber}`);

    if (results.length > 0) {
      Toast({
        context: this,
        selector: '#t-toast',
        message: `识别成功: ${results.join(', ')}`,
        theme: 'success',
        direction: 'column',
      });
    } else {
      Toast({
        context: this,
        selector: '#t-toast',
        message: '未识别到有效信息，请检查输入格式',
        theme: 'warning',
        direction: 'column',
      });
    }
  },



  /**
   * 更新车牌输入组件的显示
   * @param {string} carNumber 车牌号
   */
  updateCarPlateDisplay(carNumber) {
    if (!carNumber || carNumber.length < 7) return;

    // 解析车牌号：第一个字符是省份，后面是字母和数字
    const province = carNumber.charAt(0);
    const remaining = carNumber.substring(1);

    // 构建车牌数组
    const newCarPlateArray = ['', '', '', '', '', '', '', '新能源'];
    newCarPlateArray[0] = province;

    // 填充剩余字符
    for (let i = 0; i < remaining.length && i < 6; i++) {
      newCarPlateArray[i + 1] = remaining.charAt(i);
    }

    // 如果是新能源车牌（8位），最后一位放在新能源位置
    if (carNumber.length === 8) {
      newCarPlateArray[7] = remaining.charAt(6);
    } else {
      newCarPlateArray[7] = '新能源';
    }

    // 更新车牌输入组件的数据
    this.setData({
      carPlateArray: newCarPlateArray,
      activeIndex: -1 // 取消高亮
    });

    console.log('更新车牌显示:', newCarPlateArray);
  },

  /**
   * 解析智能输入的文本
   * @param {string} text 输入的文本
   * @returns {object} 解析结果
   */
  parseSmartInput(text) {
    const result = {
      name: '',
      tel: '',
      idCard: '',
      carNumber: ''
    };

    const app = getApp();

    // 优先识别带标签的格式
    this.parseWithLabels(text, result, app);

    // 如果带标签格式没有识别完全，再用通用格式补充
    if (!result.name || !result.tel || !result.idCard || !result.carNumber) {
      this.parseWithoutLabels(text, result, app);
    }

    return result;
  },

  /**
   * 解析带标签的格式，如"姓名：张三"
   * @param {string} text 输入文本
   * @param {object} result 结果对象
   * @param {object} app app实例
   */
  parseWithLabels(text, result, app) {
    // 姓名标签识别：姓名、司机姓名、司机、名字等
    const nameLabels = /(姓名|司机姓名|司机|名字|驾驶员)\s*[:：]\s*([\u4e00-\u9fa5]{2,4})/g;
    let nameMatch = nameLabels.exec(text);
    if (nameMatch && app.isChinese(nameMatch[2])) {
      result.name = nameMatch[2];
    }

    // 手机号标签识别：手机号、电话、联系方式、手机等
    const phoneLabels = /(手机号|电话|联系方式|手机|司机手机号|联系电话)\s*[:：]\s*(1[3-9]\d{9})/g;
    let phoneMatch = phoneLabels.exec(text);
    if (phoneMatch && app.isPhone(phoneMatch[2])) {
      result.tel = phoneMatch[2];
    }

    // 身份证标签识别：身份证号、身份证、证件号等
    const idCardLabels = /(身份证号|身份证|证件号|身份证号码)\s*[:：]\s*([1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X))/gi;
    let idCardMatch = idCardLabels.exec(text);
    if (idCardMatch) {
      const upperIdCard = idCardMatch[2].toUpperCase();
      if (app.regIdCard(upperIdCard)) {
        result.idCard = upperIdCard;
      }
    }

    // 车牌号标签识别：车牌号、车牌、车辆号牌等
    const carLabels = /(车牌号|车牌|车辆号牌|号牌)\s*[:：]\s*([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼港澳使领][A-Z][A-Z0-9]{5,6})/g;
    let carMatch = carLabels.exec(text);
    if (carMatch && app.isCarNumb(carMatch[2])) {
      result.carNumber = carMatch[2];
    }
  },

  /**
   * 解析无标签的通用格式
   * @param {string} text 输入文本
   * @param {object} result 结果对象
   * @param {object} app app实例
   */
  parseWithoutLabels(text, result, app) {
    // 手机号识别：使用app.js中的手机号验证规则
    if (!result.tel) {
      const phoneRegex = /1[3-9]\d{9}/g;
      const phoneMatches = text.match(phoneRegex);
      if (phoneMatches && phoneMatches.length > 0) {
        for (let phone of phoneMatches) {
          if (app.isPhone(phone)) {
            result.tel = phone;
            break;
          }
        }
      }
    }

    // 身份证号识别：使用app.js中的身份证验证规则
    if (!result.idCard) {
      const idCardRegex = /[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)/gi;
      const idCardMatches = text.match(idCardRegex);
      if (idCardMatches && idCardMatches.length > 0) {
        for (let idCard of idCardMatches) {
          const upperIdCard = idCard.toUpperCase();
          if (app.regIdCard(upperIdCard)) {
            result.idCard = upperIdCard;
            break;
          }
        }
      }
    }

    // 车牌号识别：使用app.js中的车牌号正则表达式，只支持一辆车
    if (!result.carNumber) {
      const carNumberRegex = /[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼港澳使领][A-Z][A-Z0-9]{5,6}/g;
      const carNumberMatches = text.match(carNumberRegex);
      if (carNumberMatches && carNumberMatches.length > 0) {
        for (let carNumber of carNumberMatches) {
          if (app.isCarNumb(carNumber)) {
            result.carNumber = carNumber;
            break;
          }
        }
      }
    }

    // 姓名识别：移除已识别的信息后，提取可能的姓名
    if (!result.name) {
      let nameText = text;
      // 移除手机号
      nameText = nameText.replace(/1[3-9]\d{9}/g, '');
      // 移除身份证号
      nameText = nameText.replace(/[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)/gi, '');
      // 移除车牌号
      nameText = nameText.replace(/[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼港澳使领][A-Z][A-Z0-9]{5,6}/g, '');
      // 移除标签
      nameText = nameText.replace(/(姓名|司机姓名|司机|名字|驾驶员|手机号|电话|联系方式|手机|身份证号|身份证|证件号|车牌号|车牌|车辆号牌|号牌)\s*[:：]/g, '');

      // 提取中文姓名（2-4个中文字符）
      const nameRegex = /[\u4e00-\u9fa5]{2,4}/g;
      const nameMatches = nameText.match(nameRegex);
      if (nameMatches && nameMatches.length > 0) {
        for (let name of nameMatches) {
          if (app.isChinese(name)) {
            result.name = name;
            break;
          }
        }
      }
    }
  },

  /**
   * 关闭错误提示框
   */
  closeDialog() {
    this.setData({
      showTextAndTitle: false,
    });
  },

  /**
   * 新增
   */
  insert() {
    // 判断手机号是否正确
    const { tel, carNumber, idCard, customerCodeText, customerName, code, reservationIdentityName, name } = this.data;
    if (customerCodeText.includes('请选择')) {
      this.setData({
        showTextAndTitle: true,
        content: `请选择${reservationIdentityName}代码`,
      });
      return;
    }

    if (!name) {
      this.setData({
        showTextAndTitle: true,
        content: `请填写姓名`,
      });
      return;
    }
    const app = getApp();
    const isPhoneNumber = app.isPhone(tel);
    if (!isPhoneNumber) {
      this.setData({
        showTextAndTitle: true,
        content: '请填写正确的手机号',
      });
      return;
    }

    const carList = carNumber.split(/[,，]/);
    let isCarNumber = true;
    let carIndex = -1;
    for (let index = 0; index < carList.length; index++) {
      isCarNumber = app.isCarNumb(carList[index]);
      if (!isCarNumber) {
        carIndex = index;
        break;
      }
    }
    if (!isCarNumber) {
      this.setData({
        showTextAndTitle: true,
        content: `第${carIndex + 1}个车牌号错误, 请检查`,
      });
      return;
    }

    const isIdCardPattern = /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/.test(idCard);
    if (!isIdCardPattern) {
      this.setData({
        showTextAndTitle: true,
        content: '请填写正确的身份证号',
      });
      return;
    }

    wx.showLoading({
      title: '新增中',
    })
    const { segNo, unitCode, administrator, reservationIdentity, driverName } = this.data.userInfo;
    const result = {
      segNo,
      unitCode,
      customerId: customerCodeText,
      customerName,
      driverName: name,
      tel,
      driverIdentity: idCard,
      vehicleNo: carNumber,
      recCreator: this.data.userInfo.tel ?? driverName,
      recCreatorName: administrator ?? driverName,
      messageCode: code,
      reservationIdentity,
    };
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        result,
        serviceId: 'S_LI_RL_0012',
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          this.setData({
            showTextAndTitle: true,
            content: '网络异常, 请稍后重试',
          });
          return;
        }

        const result = res.data;
        if (result.__sys__?.status == -1) {
          this.setData({
            showTextAndTitle: true,
            content: result.__sys__.msg,
          });
          return;
        }

        Toast({
          context: this,
          selector: '#t-toast',
          message: '新增成功',
          theme: 'success',
          direction: 'column',
        });

        // 返回主页面
        setTimeout(() => {
          wx.navigateBack({
            delta: 1
          });
        }, 1000);
      },
    });
  },

  /**
   * 发送验证码
   */
  sendCode() {
    // 判断手机号是否正确
    const { tel, name, userInfo } = this.data;
    const isPhoneNumber = /^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(tel);
    if (!isPhoneNumber) {
      this.setData({
        showTextAndTitle: true,
        content: '请填写正确的手机号',
      });
      return;
    }

    wx.showLoading({
      title: '发送中',
    })
    const app = getApp();
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        driverTel: tel,
        driverName: name,
        recCreator: userInfo.tel,
        recCreatorName: userInfo.administrator,
        serviceId: 'S_LI_RL_0017',
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          this.setData({
            showTextAndTitle: true,
            content: '网络异常, 请稍后重试',
          });
          return;
        }

        const result = res.data;
        if (result.__sys__?.status == -1) {
          this.setData({
            showTextAndTitle: true,
            content: result.__sys__.msg,
          });
          return;
        }

        Toast({
          context: this,
          selector: '#t-toast',
          message: '发送成功',
          theme: 'success',
          direction: 'column',
        });
      },
    });
  },
})