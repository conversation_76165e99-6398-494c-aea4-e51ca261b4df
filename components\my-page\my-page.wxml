<view>
  <view class="my-basic">
    <t-avatar class="avatar-example" t-class-content="external-class-content">{{firstName}}</t-avatar>
    <view class="info-container">
      <text class="name">{{name}}</text>
      <text class="phone">{{phoneNum}}</text>
    </view>
    <view class="icon-container">
      <t-icon name="user" size="30" bind:tap="onEditTap"></t-icon>
    </view>
  </view>

  <view class="container">
    <view class="section">
      <t-grid column="3" class="block" theme="card">
        <view class="section-title">
          <text>我的功能</text>
        </view>
        <t-grid-item wx:if="{{userInfo.identityType == '20'}}" text="我的车辆" image="/assets/image/我的车辆.svg" bind:tap="toMyCar" />
        <t-grid-item text="我的预约" wx:if="{{userInfo.reservationIdentity != '40'}}" image="/assets/image/我的预约2.svg" bind:tap="toMyReservation" />
        <t-grid-item text="我的角色" image="/assets/image/1角色管理.svg" bind:tap="toMyRole" />
        <t-grid-item text="废料提货" wx:if="{{userInfo.reservationIdentity == '30' || userInfo.reservationIdentity == '50'}}" image="/assets/image/物流查询.svg" bind:tap="toInPlant" />
      </t-grid>
    </view>
  </view>
</view>