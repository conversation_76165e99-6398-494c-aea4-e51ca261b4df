// pages/insert-user/insert-user.js
import Toast from 'tdesign-miniprogram/toast/index';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    identityVisible: false, // 是否显示选择身份
    identityValue: '', // 显示身份
    identity: '请选择身份', // 选择的身份
    identityList: [
      { label: '客户车辆管理员', value: '10' },
      { label: '承运商车辆管理员', value: '20' },
      { label: '司机', value: '30' },
    ],
    name: '',
    tel: '',
    idCard: '',
    activeIndex: -1, // 子组件是否高亮
    carNumber: '',
    showTextAndTitle: false,
    confirmBtn: { content: '确定', variant: 'base' },
    content: '', // 提示信息
    belongCompany: '', // 所属公司
    belongCompanyVisible: false,
    belongCompanyValue: '请选择所属公司',
    belongCompanyName: '',
    belongCompanyQueryVisible: false,
    belongCompanyQuery: '',
    belongCompanyList: [],
    entrust: '请选择委托运输', // 委托运输
    entrustVisible: false,
    entrustList: [
      { label: '受宝钢的承运商委托', value: '10' },
      { label: '受宝钢的客户委托', value: '20' },
    ],
    entrustValue: '',
    customerAllList: [],
    code: '', // 验证码
    reservationIdentity: '10', //10:客户, 20:承运商
    showMsg: false,
    msgContent: '',
    confirmMsgBtn: { content: '知道了', variant: 'base' },
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
 * input框伪双向绑定
 * @param {*} e 
 */
  onInputChang(e) {
    const { key } = e.currentTarget.dataset;
    const { value } = e.detail;
    this.setData({
      [key]: value,
    });
  },

  /**
   * 显示选择身份框
   */
  onShowIdentity() {
    this.setData({
      identityVisible: true,
    });
  },

  onShowEntrust() {
    this.setData({
      entrustVisible: true,
    });
  },

  /**
   * 显示公司选择框
   */
  onShowBelongCompany() {
    const { identity, identityValue, entrust, entrustValue } = this.data;
    if (identity.includes('选择')) {
      this.setData({
        showTextAndTitle: true,
        content: '请先选择身份',
      });
      return;
    }

    if (identityValue.includes('30') && entrust.includes('选择')) {
      this.setData({
        showTextAndTitle: true,
        content: '请选择委托运输',
      });
      return;
    }

    const idV = identityValue[0];
    // 客户
    if (idV == '10' || (idV == '30' && entrustValue.includes('20'))) {
      this.setData({
        reservationIdentity: '10',
      });
    } else {
      this.setData({
        reservationIdentity: '20',
      });
    }
    this.getCustomerList();

    // this.setData({
    //   belongCompanyVisible: true,
    // });
  },

  /**
   * 根据公司编号和编码查询
   */
  belongCompanyQueryChange(e) {
    const { value } = e.detail;
    this.setData({
      belongCompanyQuery: value,
    })
  },

  /**
   * 确定选择框值
   */
  onPickerChange(e) {
    const { value, label } = e.detail;
    const { key } = e.currentTarget.dataset;
    this.setData({
      [`${key}Visible`]: false,
      [`${key}Value`]: value,
      [`${key}`]: label,
    });

    // 如果为身份, 那么清空数据
    if (key == 'identity') {
      this.setData({
        entrust: '请选择委托运输', // 委托运输
        belongCompany: '', // 所属公司
        belongCompanyVisible: false,
        belongCompanyValue: '请选择所属公司',
        belongCompanyName: '',
        belongCompanyQueryVisible: false,
        belongCompanyQuery: '',
        belongCompanyList: [],
      });
    }

    if (key == 'belongCompany') {
      this.setData({
        belongCompanyVisible: false,
        belongCompanyQueryVisible: false,
        belongCompanyQuery: '',
      });
    }
  },

  /** 子页面方法 */
  onInputComplete(event) {
    const { plate } = event.detail; // 获取子组件传递的车牌号
    console.log('车牌号:', plate); // 打印或处理车牌号

    // 更新父页面的 carNumber 数据
    this.setData({
      carNumber: plate
    });
  },

  /**
 * 关闭错误提示框
 */
  closeDialog() {
    this.setData({
      showTextAndTitle: false,
    });
  },

  closeMsgDialog() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 隐藏选择司机弹出框
   */
  belongCompanyCanCel() {
    this.setData({
      belongCompanyVisible: false,
      belongCompanyQueryVisible: false,
    });
  },

  belongSumbit() {
    this.getCustomerList();
  },

  belongClear() {
    this.setData({
      belongCompanyQuery: '',
    });
    this.getCustomerList();
  },

  /**
   * 查询客户
   */
  getCustomerList() {
    const app = getApp();
    wx.showLoading({
      title: '查询中',
    });
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        segNo: app.segNo,
        customerName: this.data.belongCompanyQuery,
        reservationIdentity: this.data.reservationIdentity,
        serviceId: 'S_LI_RL_0113',
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          this.setData({
            showTextAndTitle: true,
            content: '网络异常, 请稍后重试',
          });
          return;
        }

        const result = res.data;
        if (result.__sys__?.status == -1) {
          this.setData({
            showTextAndTitle: true,
            content: result.__sys__.msg,
          });
          return;
        }

        const { list } = result;
        let belongCompanyList = list.map(l => {
          return {
            label: l.chineseUserName,
            value: l.userNum,
          }
        });

        this.setData({
          belongCompanyList,
          belongCompanyVisible: true,
          belongCompanyQueryVisible: true,
        });

      },
    });
  },

  /**
 * 新增
 */
  insert() {
    // 判断手机号是否正确
    const { tel, carNumber, idCard, belongCompany, belongCompanyValue, code, identity, name, identityValue, entrust } = this.data;

    if (identity.includes('请选择')) {
      this.setData({
        showTextAndTitle: true,
        content: `请选择身份`,
      });
      return;
    }

    if (belongCompanyValue.includes('请选择')) {
      this.setData({
        showTextAndTitle: true,
        content: `请选择所属公司`,
      });
      return;
    }

    if (!name) {
      this.setData({
        showTextAndTitle: true,
        content: `请填写姓名`,
      });
      return;
    }
    const app = getApp();
    const isPhoneNumber = app.isPhone(tel);
    if (!isPhoneNumber) {
      this.setData({
        showTextAndTitle: true,
        content: '请填写正确的手机号',
      });
      return;
    }

    let isCarNumber = app.isCarNumb(carNumber);
    if (identity.includes('30') && !isCarNumber) {
      this.setData({
        showTextAndTitle: true,
        content: `车牌号错误, 请检查`,
      });
      return;
    }

    const isIdCardPattern = app.regIdCard(idCard);
    if (!isIdCardPattern) {
      this.setData({
        showTextAndTitle: true,
        content: '请填写正确的身份证号',
      });
      return;
    }

    wx.showLoading({
      title: '注册中',
    })

    const result = {
      segNo: app.segNo,
      customerId: belongCompanyValue[0],
      customerName: belongCompany[0],
      adminDriverName: name,
      tel,
      driverIdentity: idCard,
      vehicleNo: carNumber,
      recCreator: tel,
      recCreatorName: name,
      messageCode: code,
      reservationIdentity: identityValue[0],
    };
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        result,
        serviceId: 'S_LI_RL_0114',
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          wx.hideLoading();
          this.setData({
            showTextAndTitle: true,
            content: '网络异常, 请稍后重试',
          });
          return;
        }

        const result = res.data;
        if (result.__sys__?.status == -1) {
          wx.hideLoading();
          this.setData({
            showTextAndTitle: true,
            content: result.__sys__.msg,
          });
          return;
        }

        let msg = '';
        // 客户车辆管理员
        if (identityValue[0] == '10') {
          msg = '您已提交注册信息，请联系宝钢方对口营销人员审核';
        } else if (identityValue[0] == '20') {
          // 承运商车辆管理员
          msg = '您已提交注册信息，请联系宝钢方对口承运商管理人员审核';
        } else {
          // 司机
          if (entrust[0].includes('承运商')) {
            // 受宝钢的承运商
            msg = '您已提交注册信息，请联系对应承运商调度管理人员审核';
          } else {
            // 受宝钢的客户
            msg = '您已提交注册信息，请联系对应客户调度管理人员审核';
          }
        }

        this.setData({
          showMsg: true,
          msgContent: msg,
        });
      },
    });
  },

  /**
 * 发送验证码
 */
  sendCode() {
    // 判断手机号是否正确
    const app = getApp();
    const { tel, name } = this.data;
    if (!name) {
      this.setData({
        showTextAndTitle: true,
        content: '请填写姓名',
      });
      return;
    }

    const isPhoneNumber = app.isPhone(tel);
    if (!isPhoneNumber) {
      this.setData({
        showTextAndTitle: true,
        content: '请填写正确的手机号',
      });
      return;
    }

    wx.showLoading({
      title: '发送中',
    });
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        driverTel: tel,
        driverName: name,
        recCreator: tel,
        recCreatorName: name,
        serviceId: 'S_LI_RL_0017',
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          this.setData({
            showTextAndTitle: true,
            content: '网络异常, 请稍后重试',
          });
          return;
        }

        const result = res.data;
        if (result.__sys__?.status == -1) {
          this.setData({
            showTextAndTitle: true,
            content: result.__sys__.msg,
          });
          return;
        }

        Toast({
          context: this,
          selector: '#t-toast',
          message: '发送成功',
          theme: 'success',
          direction: 'column',
        });
      },
    });
  },
})