// pages/customer-sign/customer-sign-list/customer-sign-list.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    customerSignList: [],
    showTextAndTitle: false,
    content: '', // 提示信息
    confirmBtn: { content: '确定', variant: 'base' },
    carList: [],
    customer: {
      value: '0',
      options: [{
        value: '0',
        label: '未签收',
      }, {
        value: '1',
        label: '已签收',
      }],
    },
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadData(); // 你的初始化数据请求
  },

  onShow() {
    let pages = getCurrentPages();
    let currPage = pages[pages.length - 1]; // 获取当前页面
    if (currPage.data.needReload) {
      this.onLoad(currPage.options); // 重新执行 onLoad
      currPage.setData({ needReload: true }); // 重置标志
    }
  },

  loadData() {
    const user = wx.getStorageSync('userInfo');
    const app = getApp();
    // 查询司机下面的车牌号
    const { segNo, driverName, tel } = user
    wx.request({
      url: app.mesUrl,
      method: 'POST', // 请求方法
      data: {
        tel,
        segNo,
        driverName,
        serviceId: 'S_LI_RL_0027',
      },
      success: (res) => {
        if (res?.data?.result?.length == 0) {
          return;
        }

        this.setData({
          carList: res.data.result,
        });
        this.queryCustomerList();
      },
    });
  },

  /**
 * 客户下拉框选择
 * @param {*} e 
 */
  onChange(e) {
    this.setData({
      'customer.value': e.detail.value,
    });
    this.queryCustomerList();
  },

  /**
* 伪双向绑定搜索框
* @param {*} e 
*/
  changeHandle(e) {
    const { value } = e.detail;
    this.setData({
      value,
    });
  },

  actionHandle() {
    this.queryCustomerList();
  },

  queryCustomerList() {
    const app = getApp();
    const user = wx.getStorageSync('userInfo');
    wx.showLoading({
      title: '加载中',
    });
    wx.request({
      url: app.mesUrl,
      method: 'POST',
      data: {
        segNo: user.segNo,
        vehicleNo: this.data.carList,
        voucherNum: this.data.value,
        signatureFlag: this.data.customer.value,
        serviceId: 'S_LI_RL_0043',
      },
      success: (res) => {
        wx.hideLoading();
        if (!res || !res.data || res.statusCode != 200) {
          this.setData({
            showTextAndTitle: true,
            content: '网络异常, 请稍后重试',
          });
          return;
        }

        const result = res.data;
        if (result.__sys__?.status == -1) {
          this.setData({
            showTextAndTitle: true,
            content: result.__sys__.msg,
          });
          return;
        }
        const reservationList = result.list.map(r => {
          const dateString = r.leaveFactoryDate;
          const formattedDate = dateString.replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1/$2/$3 $4:$5:$6');
          r['dateStr'] = formattedDate;
          return r;
        });
        this.setData({
          customerSignList: reservationList,
        });
      },
      fail: () => {
        wx.hideLoading();
        this.setData({
          showTextAndTitle: true,
          content: '网络异常, 请稍后重试',
        });
      }
    });
  },

  /**
* 关闭错误提示框
*/
  closeDialog() {
    this.setData({
      showTextAndTitle: false,
    });
  },

  /**
   * 进入明细页面
   */
  toInfo(e) {
    const index = e.currentTarget.dataset.key;
    const custInfo = this.data.customerSignList[index];
    wx.navigateTo({
      url: '/pages/customer-sign/customer-sign-info/customer-sign-info',
      events: {
        // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
        acceptDataFromOpenedPage: () => { },
      },
      success: (res) => {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('acceptDataFromOpenerPage', { data: custInfo, })
      }
    });
  },
})