<!--pages/delivery-commission/delivery-commission-edit/delivery-commission-edit.wxml-->
<view>
  <view class="container">
    <view class="form-group">
      <t-cell class="mb-16" t-class-note="app-t-class" title="提单/转库单号" note="{{deliveryNo}}" />
      <t-cell class="mb-16" t-class-note="app-t-class" title="承运商/客户名称" note="{{customerName}}" />

      <t-cell class="mb-16" t-class-note="app-t-class" title="始发站仓库" note="{{delivery.ladingSpotName}}" />
      <t-cell class="mb-16" t-class-note="app-t-class" title="终到站仓库" note="{{delivery.destSpotName}}" />

      <t-cell class="mb-16" t-class-note="app-t-class" title="司机姓名" arrow hover note="{{name}}" bind:click="onNamePicker" />

      <!-- 搜索司机 -->
      <t-popup visible="{{nameQueryVisible}}" usingCustomNavbar placement="bottom" style="height: 580rpx;">
        <view class="aaa">
          <view class="example-search">
            <t-search placeholder="搜索司机" bind:change="nameQueryChange" value="{{nameQuery}}" />
          </view>
          <view class="example-picker">
            <t-picker auto-close="{{false}}" visible="{{nameVisible}}" value="{{nameValue}}" data-key="name" title="选择司机" cancelBtn="取消" confirmBtn="确认" usingCustomNavbar bindchange="onPickerChange" bindcancel="onNameQueryChange">
              <t-picker-item options="{{nameList}}" />
            </t-picker>
          </view>
        </view>
      </t-popup>

      <t-cell class="mb-16" t-class-note="app-t-class" title="手机号" note="{{tel}}" />

      <t-cell class="mb-16" title="车牌号" t-class-note="app-t-class" arrow hover note="{{carNumber}}" bind:click="onCarNumberPicker" />
      <t-picker visible="{{carNumberVisible}}" value="{{carNumberValue}}" data-key="carNumber" title="选择车牌号" cancelBtn="取消" confirmBtn="确认" usingCustomNavbar bindchange="onPickerChange">
        <t-picker-item options="{{vehicleNoList}}" />
      </t-picker>

    </view>

    <view class="btn-group">
      <t-button theme="primary" block bind:tap="editDelivery">修改</t-button>
    </view>

    <t-dialog visible="{{showTextAndTitle}}" title="提示" content="{{content}}" confirm-btn="{{ confirmBtn }}" bind:confirm="closeDialog" />

    <t-toast id="t-toast" />
  </view>


</view>