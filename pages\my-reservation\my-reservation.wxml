<view class="example-search">
  <t-search value="{{value}}" placeholder="搜索预约单, 按照司机、手机号" action="{{'搜索'}}" bind:action-click="actionHandle" bind:change="changeHandle" bind:submit="actionHandle" bind:clear="clearValue" />

  <t-dropdown-menu style="width: 15em; background-color: transparent;position: initial;">
    <t-dropdown-item options="{{customer.options}}" value="{{customer.value}}" bindchange="onChange" />
  </t-dropdown-menu>
</view>


<scroll-view scroll-y style="height: calc(100vh - 300rpx);" bindscrolltolower="onReachBottom" scroll-top="{{scrollTop}}">
  <view wx:if="{{reservationList.length > 0}}">
    <view class="card-main" data-index="{{index}}" wx:for="{{reservationList}}" wx:key="index" bind:tap="toInfo">
      <view class="card-item">
        <view class="label">预约单号:</view>
        <view class="value">{{item.reservationNumber}}
          <t-tag class="margin-16" style="margin-right:0.5em;" theme="{{item.statusClass}}">{{item.statusName }}</t-tag>
          <t-tag class="margin-16" theme="primary">{{ item.typeName }}</t-tag>
        </view>
      </view>
      <!-- 其他卡片内容... -->
      <view class="card-item">
        <view class="label">司机姓名/车牌号:</view>
        <view class="value">{{item.driverName}}/{{item.vehicleNo}}</view>
      </view>
      <view class="card-item">
        <view class="label" style="min-width: 8em;">承运商/客户名称:</view>
        <view class="value">{{item.customerName}}</view>
      </view>
      <view class="card-item" wx:if="{{item.segNo == 'KF000000'}}">
        <view class="label">预约日期:</view>
        <view class="value">{{item.dateStr + ' ' + item.reservationTime}}</view>
      </view>
      <view class="card-item" wx:else>
        <view class="label">预约日期:</view>
        <view class="value">{{item.dateJCStr}}</view>
      </view>

      <view class="icon-container">
        <t-button wx:if="{{isShowEdit}}" style="padding: 0;margin-left: 0;" size="large" theme="primary" variant="text" catchtap="editItem" data-index="{{index}}">修改预约</t-button>
        <t-button style="padding: 0;margin-right: 0;" size="large" theme="danger" variant="text" catchtap="deleteItem" data-index="{{index}}">取消预约</t-button>
      </view>

      <view class="icon-container" wx:if="{{item.typeName == '废料提货' && userInfo.reservationIdentity == '30'}}">
        <t-button style="padding: 0;margin-left: 0;" size="large" theme="primary" variant="text" catchtap="openLoadPointDialog" data-index="{{index}}">增加装卸点</t-button>
      </view>
    </view>


  </view>
  <!-- 无数据提示 -->
  <view wx:else class="empty-container">
    <t-empty icon="file-image" description="暂无预约记录" />
  </view>

  <t-dialog visible="{{showLoadPoint}}" title="请选择装卸点" cancel-btn="取消" confirm-btn="{{ loadPointBtn }}" bind:confirm="addLoadPoint" bind:cancel="closeLoadPoint">
    <view slot="content">
      <scroll-view scroll-y class="dialog-content" enhanced show-scrollbar="{{false}}" bindtouchstart="true">
        <t-checkbox-group t-class="box" borderless bind:change="radioChange">
          <t-checkbox wx:for="{{loadPointList}}" wx:key="handPointId" value="{{item.handPointId}}" label="{{item.handPointName}}" />
          <!-- <t-checkbox block="{{false}}" value="checkbox2" label="多选标题" />
          <t-checkbox block="{{false}}" value="checkbox3" label="上限四字" /> -->
        </t-checkbox-group>
        <!-- <radio-group bindchange="radioChange">
          <view class="radio-item" wx:for="{{loadPointList}}" wx:key="handPointId">
            <label class="radio-label">
              <radio value="{{item.handPointId}}" checked="{{selectedHandPointId === item.handPointId}}" color="#0052d9" />
              <text class="radio-text">{{item.handPointName}}</text>
            </label>
          </view>
        </radio-group> -->
      </scroll-view>
    </view>
  </t-dialog>

  <t-dialog visible="{{showTextAndTitle}}" title="提示" content="{{content}}" confirm-btn="{{ confirmBtn }}" bind:confirm="closeDialog" />

  <!-- 底部加载状态 -->
  <view class="pagination-status" wx:if="{{reservationList.length > 0}}">
    <text wx:if="{{hasMore}}">加载更多...</text>
    <text wx:else>没有更多数据了</text>
  </view>
</scroll-view>

<t-message id="t-message" />