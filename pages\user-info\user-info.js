// pages/user-info/user-info.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    userList: [],
    reservationIdentity: '',
    reservationIdentityName: '',
    characterVisible: false,
    characterList: [],
    userInfoList: [],
    userInfo: '',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let userInfo = wx.getStorageSync("userInfo");
    let userInfoList = wx.getStorageSync("userInfoList");
    const app = getApp();
    const { segNo, identityType, tel, reservationIdentity } = userInfo;

    const array = userInfoList.map(r => {
      const rIdName = app.getUserName(r.reservationIdentity);
      return { label: rIdName, value: r.reservationIdentity };
    });
    const uniqueArray = array.filter((item, index, self) =>
      index === self.findIndex((t) => t.label === item.label && t.value === item.value)
    );
    
    // 将司机身份排到第一位
    const sortedArray = app.sortRoleList(uniqueArray);
    
    this.setData({
      reservationIdentity,
      userInfoList,
      characterList: sortedArray,
      reservationIdentityName: app.getUserName(reservationIdentity),
      userInfo,
    })
    this.queryList(tel, segNo, identityType, reservationIdentity);
  },

  /**
   * 显示选择角色框
   */
  onCharacter() {
    this.setData({ characterVisible: true });
  },

  /**
   * 关闭选择角色框
   */
  onCharacterCancel() {
    this.setData({ characterVisible: false });
  },

  /**
   * 确定角色
   * @param {*} e 
   */
  onCharacterChange(e) {
    let { value } = e.detail;
    let valStr = value.join('');
    const app = getApp();
    const userInfo = this.data.userInfoList.find(u => u.reservationIdentity == valStr);
    if (!userInfo) {
      return;
    }
    const { segNo, identityType, tel, reservationIdentity } = userInfo;
    this.setData({
      reservationIdentity: value,
      reservationIdentityName: app.getUserName(reservationIdentity),
      userInfo,
    });

    wx.setStorage({
      key: "userInfo",
      data: userInfo
    });

    this.queryList(tel, segNo, identityType, reservationIdentity);
  },

  /** 查询数据 */
  queryList(tel, segNo, identityType, reservationIdentity) {
    const app = getApp();
    wx.showLoading({
      title: '加载中',
    })
    wx.request({
      url: app.mesUrl,
      method: 'POST', // 请求方法
      data: {
        tel,
        segNo,
        identityType,
        reservationIdentity,
        serviceId: 'S_LI_RL_0026',
      },
      success: (res) => {
        wx.hideLoading();
        if (res?.data?.result) {
          const result = res.data.result;
          this.setData({
            userList: result,
          });
          return;
        }
        this.setData({
          userList: [],
        });
      },
      fail: () => wx.hideLoading(),
    });
  },

  /**
   * 退出登录
   */
  loginOut() {
    wx.clearStorageSync();
    wx.reLaunch({
      url: '/pages/home-login/home-login'
    })
  },
})