/* pages/order-allocation/spot-trading-order-detail/spot-trading-order-detail.wxss */
.container {
  padding: 20rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  min-height: 100vh;
  background-color: #f5f5f5;
  box-sizing: border-box;
}

/* 表单区域样式 */
.form-section {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2rpx solid #f0f0f0;
  padding-bottom: 20rpx;
}

.select-all-btn {
  font-size: 28rpx;
  color: #1953E6;
  background-color: #f0f7ff;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  border: 1rpx solid #1953E6;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.required {
  color: #ff4444;
  margin-left: 4rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  box-sizing: border-box;
  background-color: #ffffff;
}

.form-input:focus {
  border-color: #1953E6;
}

.form-input.disabled {
  background-color: #f5f5f5;
  color: #999999;
}

/* 车牌号选择器样式 */
.car-selector {
  position: relative;
}

.car-select-btn {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  box-sizing: border-box;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.car-select-btn .placeholder {
  color: #999999;
}

.car-select-btn .selected {
  color: #333333;
}

.car-select-btn .arrow {
  color: #999999;
  font-size: 24rpx;
  transform: rotate(90deg);
}

.car-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10rpx);
  transition: all 0.3s ease;
  overflow: hidden;
}

.car-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

.search-box {
  padding: 20rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #eee;
}

.search-input {
  width: 100%;
  height: 80rpx;
  background: white;
  border: 2rpx solid #e3f2fd;
  border-radius: 25rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.search-input:focus {
  border-color: #2196f3;
  box-shadow: 0 4rpx 16rpx rgba(33, 150, 243, 0.2);
}

.car-list {
  max-height: 560rpx;
  overflow-y: auto;
  background: white;
}

.car-item {
  padding: 36rpx 48rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  font-size: 28rpx;
  color: #333;
}

.car-plate {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.driver-name {
  font-size: 26rpx;
  color: #666;
  font-weight: 400;
}

.car-item:last-child {
  border-bottom: none;
}

.car-item.selected {
  background: linear-gradient(90deg, #e3f2fd 0%, #bbdefb 100%);
}

.car-item.selected .car-plate {
  color: #1976d2;
  font-weight: 700;
}

.car-item.selected .driver-name {
  color: #1976d2;
  font-weight: 500;
}

.car-item.selected::after {
  content: "✓";
  position: absolute;
  right: 48rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #1976d2;
  font-weight: bold;
  font-size: 36rpx;
}

.no-data {
  padding: 80rpx 48rpx;
  text-align: center;
  color: #999;
  font-size: 28rpx;
  background: white;
}

.dropdown-actions {
  display: flex;
  border-top: 1rpx solid #eee;
  background: #fafafa;
}

.btn-cancel,
.btn-confirm {
  flex: 1;
  padding: 36rpx;
  text-align: center;
  font-size: 32rpx;
  transition: all 0.2s ease;
  font-weight: 500;
}

.btn-cancel {
  color: #666;
  border-right: 1rpx solid #eee;
}

.btn-confirm {
  color: #2196f3;
  font-weight: 600;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333333;
  box-sizing: border-box;
  background-color: #ffffff;
}

.form-textarea:focus {
  border-color: #1953E6;
}

/* 货物列表样式 */
.goods-list {
  max-height: 600rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  background-color: #fafafa;
}

.order-scroll {
  width: 100%;
  max-height: 600rpx;
  overflow: auto;
}

.dataList-empty {
  width: 100%;
  height: 300rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.my-order-img {
  width: 100rpx;
  height: 100rpx;
  display: block;
}

.my-order-text {
  text-align: center;
  font-size: 28rpx;
  color: #999999;
  margin-top: 20rpx;
}

.order-list {
  width: calc(100% - 40rpx);
  padding: 20rpx;
  background-color: #ffffff;
  margin: 10rpx 20rpx;
  border-radius: 12rpx;
  box-sizing: border-box;
  border: 1rpx solid #f0f0f0;
}

.list-name {
  display: flex;
  flex-direction: row;
  align-items: center;
  align-content: center;
  margin-bottom: 15rpx;
}

.list-name-checked {
  margin-right: 15rpx;
}

.list-name-checked image {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
}

.list-name-number {
  font-size: 30rpx;
  color: #333333;
  font-weight: 600;
}

.list-name-start {
  font-size: 26rpx;
  color: #666666;
  line-height: 40rpx;
  margin-bottom: 8rpx;
}

.list-name-cloum {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-top: 10rpx;
}

.list-name-cloum-one {
  flex: 0 0 50%;
  font-size: 24rpx;
  color: #666666;
  line-height: 36rpx;
  margin-bottom: 6rpx;
}

/* 提交区域样式 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  border-top: 1rpx solid #e0e0e0;
  z-index: 100;
}

.selected-count {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 15rpx;
  text-align: center;
}

.submit-btn {
  width: 100%;
  height: 80rpx;
  background-color: #1953E6;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn:active {
  background-color: #1640CC;
}

.submit-btn[disabled] {
  background-color: #cccccc;
  color: #ffffff;
}

/* 为底部提交按钮预留空间 */
.container {
  padding-bottom: 200rpx;
} 