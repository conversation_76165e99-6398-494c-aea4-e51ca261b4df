<!--pages/driver/driver-list/driver-list.wxml-->
<view>
  <view class="example-search">
    <t-search value="{{value}}" placeholder="搜索司机, 按照姓名、手机号、车牌" action="{{'搜索'}}" bind:action-click="actionHandle" bind:change="changeHandle" bind:submit="actionHandle" bind:clear="clearValue" />

    <t-dropdown-menu style="width: 18em; background-color: transparent;position: initial;">
      <t-dropdown-item options="{{customer.options}}" value="{{customer.value}}" bindchange="onChange" />
    </t-dropdown-menu>

  </view>
  <view class="card-main" wx:for="{{driverList}}" wx:key="index" bind:tap="toInfo" data-key="{{index}}">
    <view class="card-item">
      <view class="label">姓名:</view>
      <view class="value">
        {{ item.driverName }}
        <t-tag class="margin-16" theme="{{item.status == '10' ? 'primary' : 'success'}}">{{item.status == '10' ? '新增' : '生效'}}</t-tag>
      </view>
    </view>
    <view class="card-item" >
      <view class="label">手机号:</view>
      <view class="value">{{item.tel}}</view>
    </view>
    <view class="card-item" >
      <view class="label">车牌号:</view>
      <view class="value">{{item.vehicleNo}}</view>
    </view>

    <!-- 新增独立的审核按钮 -->
    <view class="audit-wrapper" wx:if="{{item.status == '10'}}">
      <t-button theme="primary" size="extra-small" variant="outline" catchtap="onAudit" data-index="{{index}}">审核</t-button>
    </view>
  </view>

  <t-dialog visible="{{showTextAndTitle}}" title="提示" content="{{content}}" confirm-btn="{{ confirmBtn }}" bind:confirm="closeDialog" />
</view>