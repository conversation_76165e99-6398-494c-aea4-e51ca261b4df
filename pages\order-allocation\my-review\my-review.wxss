/* pages/order-allocation/my-review/my-review.wxss */
.con{
    width: 100%;
    height: 100%;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    box-sizing: border-box;
    /* background-color: #ffffff; */
}
.custom-tabs {
   height: 100%;
   display: flex;
   flex-direction: column;
  }
  .t-tabs__nav.t-tabs__nav--evenly {
    width: 100%;
    background: #f6f6f6;
}
  .t-tabs__content{
    flex: 1;
    background: #f6f6f6;
  }
  .custom-panel__content{
      width: 100%;
      height: 100%;
      box-sizing: border-box;
  }
  .to-examine{
      display: flex;
      flex-direction: column;
  }
  .example-search {
    height: 82rpx;
    background-color: var(--bg-color-demo);
    padding: 16rpx 32rpx;
    flex-shrink: 0;
}
.data-list{
    flex: 1;
    overflow: hidden;
}
.container-main{
    width: 100%;
    height: 100%;
    padding:0 32rpx;
    box-sizing: border-box;
    overflow: auto;
}
.my-list{
    width:100%;
    padding:20rpx 30rpx;
    background-color: #ffffff;
    margin: 0 auto 20rpx;
    border-radius: 20rpx;
    box-sizing: border-box;
    font-size: 16px;
    line-height: 1.5;
}
.my-list-li{
    display: flex;
    margin-bottom:5px;
}
.list-name{
    display: flex;
    flex-direction: row;
    align-items: center;
    align-content: center;
    margin-bottom: 10rpx;
}
.list-name-checked image{
    width: 32rpx;
    height: 32rpx;
    display: flex;
    align-items: center;
}
.list-name-number{
    font-size: 32rpx;
    color: #333333;
    font-weight: 500;
    margin-left: 10rpx;
}
.my-list-li-name{
    /* width: 160rpx; */
    color: #666666;
    /* flex-shrink: 0;
    text-align: justify;
    text-align-last: justify; */
}
.my-list-li-value{
    color: #333333;
    flex: 1;
}
.dataList-empty{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.my-order-img{
    width: 150px;
    height: 150px;
    display: block;
    margin-bottom: 20rpx;
}
.my-order-text{
    text-align: center;
	font-size: 16px;
	color: rgb(51, 51, 51);
}
/* -- */
.reservation-order{
    width: calc(100% - 64rpx);
    margin:0 auto;
    height:64rpx;
    background-color:#1953E6;
    color: #ffffff;
    text-align: center;
    line-height: 64rpx;
    margin: 18rpx auto;
    font-size: 28rpx;
    border-radius: 8rpx;
}