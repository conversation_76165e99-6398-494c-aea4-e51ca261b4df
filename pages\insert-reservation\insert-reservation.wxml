<!--pages/insert-reservation/insert-reservation.wxml-->
<!-- 在页面的 WXML 文件中实现表单布局 -->
<view class="container">
  <t-cell class="mb-16" t-class-note="app-t-class" title="{{reservationIdentityName}}代码" arrow hover note="{{customerCode}}" bind:click="oncustomerCodePicker" />

  <t-picker visible="{{customerCodeVisible}}" value="{{customerCodeValue}}" data-key="customerCode" title="选择{{reservationIdentityName}}代码" cancelBtn="取消" confirmBtn="确认" usingCustomNavbar bindchange="onPickerChange">
    <t-picker-item options="{{customerCodeList}}" />
  </t-picker>

  <t-input disabled t-class-input="app-t-class" t-class-label="app-title" label="{{reservationIdentityName}}名称" align="right" value="{{customerName}}" />

  <t-cell class="mb-16" t-class-note="app-t-class" title="姓名" arrow hover note="{{name}}" bind:click="onNamePicker" />

  <!-- 搜索司机 -->
  <t-popup visible="{{nameQueryVisible}}" usingCustomNavbar placement="bottom" style="height: 580rpx;">
    <view class="aaa">
      <view class="example-search">
        <t-search placeholder="搜索司机" bind:change="nameQueryChange" value="{{nameQuery}}" />
      </view>
      <view class="example-picker">
        <t-picker auto-close="{{false}}" visible="{{nameVisible}}" value="{{nameValue}}" data-key="name" title="选择司机" cancelBtn="取消" confirmBtn="确认" usingCustomNavbar bindchange="onPickerChange" bindcancel="onNameQueryChange">
          <t-picker-item options="{{nameList}}" />
        </t-picker>
      </view>
    </view>
  </t-popup>

  <t-input disabled label="手机号" t-class-input="app-t-class" placeholder="司机手机号" align="right" value="{{tel}}" />
  <t-input disabled label="身份证号" t-class-input="app-t-class" placeholder="司机身份证号" align="right" value="{{driverIdentity}}" />

  <t-cell class="mb-16" title="车牌号" t-class-note="app-t-class" arrow hover note="{{carNumber}}" bind:click="onCarNumberPicker" />
  <t-picker visible="{{carNumberVisible}}" value="{{carNumberValue}}" data-key="carNumber" title="选择车牌号" cancelBtn="取消" confirmBtn="确认" usingCustomNavbar bindchange="onPickerChange">
    <t-picker-item options="{{vehicleNoList}}" />
  </t-picker>

  <t-cell class="mb-16" title="业务类型" t-class-note="app-t-class" arrow hover note="{{type}}" bind:click="onTypePicker" />
  <t-picker visible="{{typeVisible}}" value="{{typeValue}}" data-key="type" title="选择业务类型" cancelBtn="取消" confirmBtn="确认" usingCustomNavbar bindchange="onPickerChange">
    <t-picker-item options="{{typeList}}" />
  </t-picker>

  <t-input label="运输起始地" wx:if="{{segNo == 'KF000000'}}" placeholder="请输入起始地" value="{{startOrigin}}" disabled="{{isDisableFormStart}}" data-key="startOrigin" bindchange="onInputChang">
    <t-button slot="suffix" theme="primary" size="extra-small" data-param="10" data-seachtext="起始地" bind:tap="onStartOriginClick" disabled="{{isDisableFormStart}}"> 选择起始地 </t-button>
  </t-input>

  <t-input label="运输目的地" wx:if="{{segNo == 'KF000000'}}" placeholder="请输入目的地" value="{{endOrigin}}" disabled="{{isDisableFormEnd}}" data-key="endOrigin" bindchange="onInputChang">
    <t-button slot="suffix" theme="primary" size="extra-small" data-param="20" data-seachtext="目的地" bind:tap="onStartOriginClick" disabled="{{isDisableFormEnd}}"> 选择目的地 </t-button>
  </t-input>

  <!-- 运输起始地弹出层 -->
  <t-popup visible="{{startOriginVisible}}" usingCustomNavbar bind:visible-change="onStartVisibleChange" placement="bottom" style="height: 580rpx;">
    <view class="aaa">
      <view class="example-search">
        <t-search placeholder="搜索{{searchText}}" bind:change="changeHandle" value="{{searchValue}}" />
      </view>
      <view class="example-picker">
        <t-picker auto-close="{{false}}" visible="{{addrVisible}}" value="{{addrValue}}" usingCustomNavbar cancelBtn="取消" confirmBtn="确认" bindchange="onAddrChange" bindcancel="onPickerCancel">
          <t-picker-item options="{{addr}}" />
        </t-picker>
      </view>
    </view>
  </t-popup>

  <t-cell wx:if="{{segNo == 'JC000000'}}" class="mb-16" title="预约起始日期" t-class-note="app-t-class" arrow hover note="{{dateText || ''}}" bind:click="showPicker" t-class="panel-item" />
  <t-cell wx:else class="mb-16" title="预约日期" t-class-note="app-t-class" arrow hover note="{{dateText || ''}}" bind:click="showPicker" t-class="panel-item" />

  <t-cell wx:if="{{segNo == 'JC000000'}}" class="mb-16" title="预约截止日期" t-class-note="app-t-class" arrow hover note="{{deadlineText || ''}}" bind:click="showDeadPicker" t-class="panel-item" />

  <!-- 年月日 -->
  <block wx:if="{{segNo == 'KF000000'}}">
    <t-date-time-picker title="选择日期" visible="{{dateVisible}}" mode="date" defaultValue="{{date}}" format="YYYYMMDD" bindchange="onColumnChange" bindcancel="hidePicker" start="{{start}}" />
  </block>
  <!-- 年月日时分秒 -->
  <block wx:if="{{segNo === 'JC000000'}}">
    <t-date-time-picker title="选择日期和时间" visible="{{dateVisible}}" mode="minute" value="{{date}}" start="{{start}}" end="{{end}}" format="YYYYMMDDHHmm" bindchange="onColumnChange" bindcancel="hidePicker" />
    <t-date-time-picker title="选择预约截止日期" visible="{{deadVisible}}" mode="minute" value="{{deadDate}}" start="{{deadStart}}" end="{{deadEnd}}" format="YYYYMMDDHHmm" bindchange="onDeadChange" bindcancel="hidePicker" />

    <t-cell class="mb-16" title="拜访单位" t-class-note="app-t-class" arrow hover note="{{visitUnit}}" bind:click="onVisitUnitPicker" />
    <t-picker visible="{{visitUnitVisible}}" value="{{visitUnitValue}}" data-key="visitUnit" title="选择拜访单位" cancelBtn="取消" confirmBtn="确认" usingCustomNavbar bindchange="onPickerChange">
      <t-picker-item options="{{visitUnitList}}" />
    </t-picker>

    <t-cell title="是否有自带货">
      <view slot="note">
        <t-radio-group default-value="0" borderless t-class="box" bind:change="onBringChange">
          <view style="margin-right: 22rpx;display: inline-block;">
            <t-radio block="{{false}}" label="是" value="1" />
          </view>
          <t-radio block="{{false}}" label="否" value="0" />
        </t-radio-group>
      </view>
    </t-cell>
  </block>

  <view wx:if="{{isBringGood == 1}}">
    <t-textarea label="自带货描述" t-class-label="is-bring-textarea" placeholder="请输自带货描述" disableDefaultPadding="{{true}}" autosize="{{autosize}}" bind:change="onBringTextChange" />

  </view>

  <block wx:if="{{segNo == 'KF000000'}}">
    <t-cell class="mb-16" title="预约时段" t-class-note="app-t-class" arrow hover note="{{period}}" bind:click="onPeriodPicker" />
    <t-picker visible="{{periodVisible}}" value="{{periodValue}}" data-key="period" title="选择预约时段" cancelBtn="取消" confirmBtn="确认" usingCustomNavbar bindchange="onPickerChange">
      <t-picker-item options="{{periodList}}">
        <block wx:for="{{periodList}}" wx:key="index" wx:for-item="option">
          <view slot="label-suffix--{{index}}" class="label-suffix">
            <t-tag size="medium" theme="{{ option.tag > 0 ? 'success' : 'warning' }}">{{option.tag}}</t-tag>
          </view>
        </block>
      </t-picker-item>
    </t-picker>
  </block>

  <view class="btn-group">
    <t-button theme="primary" block bind:tap="submit">提交</t-button>
  </view>

  <t-dialog visible="{{showTextAndTitle}}" title="{{title}}" confirm-btn="{{ confirmBtn }}" bind:confirm="closeDialog">
    <scroll-view slot="content" type="list" scroll-y class="long-content" id="scrollH" bindscroll="handleScroll">
      <rich-text class="content-container" id="dialogScroll" nodes="{{content}}"></rich-text>
    </scroll-view>
  </t-dialog>

  <t-dialog visible="{{showMsg}}" title="通知" content="您已预约成功。预约后超过4小时不允许取消预约或修改预约，未在预约装卸日到达的记为爽约，爽约两次将被禁止预约1个月，请确保预约的准确性。" confirm-btn="{{ confirmMsgBtn }}" bind:confirm="closeMsgDialog" />

  <t-message id="t-message" />
  <t-toast id="t-toast" />
</view>