/* pages/resident-car/resident-car.wxss */

.container {
  padding: 20rpx;
  padding-bottom: 140rpx;
  /* 为底部固定按钮留出空间 */
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索区域 */
.search-container {
  margin-bottom: 20rpx;
}

/* 筛选行 */
.filter-row {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 0 20rpx;
}

.t-dialog-class {
  width: unset !important;
}

.customer-selector {
  flex: 1;
}

/* 新增按钮 */
.add-button-container {
  position: fixed;
  bottom: 20rpx;
  left: 0;
  right: 0;
  padding: 20rpx;
  background-color: #fff;
  border-top: 1rpx solid #e6e6e6;
  z-index: 999;
}

/* 列表区域 */
.list-container {
  margin-bottom: 20rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;
}

/* 车辆项目 */
.car-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.car-info {
  flex: 1;
  margin-right: 20rpx;
}

.car-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.car-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
}

.detail-item .label {
  color: #666;
  margin-right: 8rpx;
  min-width: 120rpx;
}

.detail-item .value {
  color: #333;
  flex: 1;
}

.car-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
}

.no-more {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 40rpx 0;
}

/* 新增表单 */
.add-form {
  padding: 20rpx 0;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.form-label::after {
  content: " *";
  color: #e34d59;
}

.form-item:last-child .form-label::after {
  content: "";
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding: 16rpx;
    padding-bottom: 140rpx;
    /* 为底部固定按钮留出空间 */
  }

  .car-item {
    padding: 20rpx;
  }

  .car-number {
    font-size: 32rpx;
  }

  .detail-item {
    font-size: 26rpx;
  }
}