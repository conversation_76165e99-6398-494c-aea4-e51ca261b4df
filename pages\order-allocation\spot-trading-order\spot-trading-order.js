// pages/order-allocation/spot-trading-order/spot-trading-order.js
import Toast from 'tdesign-miniprogram/toast/index';
const $api = require('../../../api/request')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {},
    dataList: [],
    checkedList: [], // 已勾选的数据列表
    searchDataList: [], // 搜索结果数据列表
    triggered: false,
    ladingBillId: '',
    editObj: '', // 要修改的对象
    isEdit: false,
    // 客户选择相关
    customer: {
      value: '',
      options: []
    },
    customerList: [], // 客户列表
    // 筛选相关
    filterVisible: false, // 筛选弹窗显示状态
    filterRemark: '', // 筛选备注
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      userInfo: wx.getStorageSync('userInfo')
    })

    // 初始化客户选择选项
    this.initCustomerOptions();

    this.resetData();

  },

  // 初始化客户选择选项
  initCustomerOptions() {
    let userInfoList = wx.getStorageSync('userInfoList');

    // 初始化默认的customer对象
    let defaultCustomer = {
      value: '',
      options: []
    };

    if (userInfoList && userInfoList.length > 0) {
      // 对客户列表进行去重处理，基于customerId
      const uniqueCustomers = [];
      const customerIdSet = new Set();

      userInfoList.forEach(user => {
        if (!customerIdSet.has(user.customerId)) {
          customerIdSet.add(user.customerId);
          uniqueCustomers.push(user);
        }
      });

      this.setData({
        customerList: uniqueCustomers
      });

      let options = uniqueCustomers.map(user => ({
        label: user.customerName,
        value: user.customerId
      }));

      // 如果有多个客户，添加"全部"选项作为默认选项
      let defaultValue = '';
      if (uniqueCustomers.length > 1) {
        options.unshift({
          label: '全部',
          value: ''
        });
        defaultValue = ''; // 默认选择"全部"
      } else if (uniqueCustomers.length === 1) {
        // 如果只有一个客户，自动选择该客户
        defaultValue = uniqueCustomers[0].customerId;
      }

      defaultCustomer = {
        value: defaultValue,
        options: options
      };
    }

    // 确保customer对象始终被设置
    this.setData({
      customer: defaultCustomer
    });
  },

  // 重置数据
  resetData() {
    this.setData({
      dataList: []
    });
    this.getList();
  },

  onPullDownRefresh() {
    this.setData({
      triggered: true
    });
    this.resetData();
    wx.stopPullDownRefresh();
    this.setData({
      triggered: false
    });
  },

  // 客户选择改变
  onChange(e) {
    if (e && e.detail !== undefined) {
      this.setData({
        'customer.value': e.detail.value
      });
      this.resetData();
    }
  },

  // 获取现货交易配单列表
  getList() {
    const { userInfo, customer, filterRemark } = this.data;
    let userInfoList = wx.getStorageSync('userInfoList') || [];

    // 根据选择的客户过滤
    if (customer && customer.value) {
      userInfoList = userInfoList.filter(u => u.customerId === customer.value);
    }

    const customerIds = userInfoList.map(m => m.customerId);

    let data = {
      "segNo": userInfo.segNo,
      // "segNo": 'KB000000',
      "tel": userInfo.tel,
      "driverName": userInfo.driverName,
      reservationIdentity: userInfo.reservationIdentity,
      // customerId: customerIds,
      ladingBillId: this.data.ladingBillId || '', // 搜索关键词
      remark: filterRemark || '' // 备注筛选
    }

    $api.request('S_LI_RL_0195', '', data).then((res) => {
      if (res && res.List) {

        this.setData({
          dataList: res.List,
          checkedList: [] // 重置选中列表
        });
      }
    }).catch((err) => {
      console.log(err);
    })
  },

  // 搜索处理
  actionHandle(e) {
    this.resetData();
  },

  // 搜索框输入变化
  changeHandle(e) {
    if (e && e.detail != undefined) {
      this.setData({
        ladingBillId: e.detail.value
      });
    }
  },

  // 清空搜索
  clearValue() {
    this.setData({
      ladingBillId: ''
    });
    this.resetData();
  },

  // 打开筛选弹窗
  openFilter() {
    this.setData({
      filterVisible: true
    });
  },

  // 关闭筛选弹窗
  closeFilter() {
    this.setData({
      filterVisible: false
    });
  },

  // 筛选弹窗状态变化
  onFilterVisibleChange(e) {
    this.setData({
      filterVisible: e.detail.visible
    });
  },

  // 备注输入
  onRemarkInput(e) {
    if (e && e.detail !== undefined) {
      this.setData({
        filterRemark: e.detail.value
      });
    }
  },

  // 重置筛选
  resetFilter() {
    this.setData({
      filterRemark: ''
    });
  },

  // 确认筛选
  confirmFilter() {
    this.setData({
      filterVisible: false
    });
    this.resetData();
  },

  // 单项选择
  cheackList(e) {
    let index = e.currentTarget.dataset.index;
    let dataList = this.data.dataList;
    let checkedList = this.data.checkedList;

    dataList[index].flag = !dataList[index].flag;

    if (dataList[index].flag) {
      // 添加到选中列表
      if (checkedList.findIndex(item => item.ladingBillId === dataList[index].ladingBillId) === -1) {
        checkedList.push(dataList[index]);
      }
    } else {
      // 从选中列表移除
      checkedList = checkedList.filter(item => item.ladingBillId !== dataList[index].ladingBillId);
    }

    this.setData({
      dataList,
      checkedList
    });
  },

  // 跳转到详情页面
  gotoDetails(e) {
    const item = e.currentTarget.dataset.listobj;
    wx.navigateTo({
      url: '/pages/order-allocation/spot-trading-order-detail/spot-trading-order-detail',
      events: {
        // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
        acceptDataFromSpotTradingOrderDetail: (data) => {
          console.log(data)
        }
      },
      success: (res) => {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('acceptDataFromSpotTradingOrder', { data: item })
      }
    })
  },

  // 整单配单
  cheackOrder() {
    if (this.data.checkedList.length === 0) {
      Toast({
        context: this,
        selector: '#t-toast',
        message: '请先选择数据',
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/order-allocation/spot-trading-order-detail/spot-trading-order-detail',
      events: {
        acceptDataFromSpotTradingOrderDetail: (data) => {
          console.log(data)
        }
      },
      success: (res) => {
        res.eventChannel.emit('acceptDataFromSpotTradingOrder', {
          checkList: this.data.checkedList,
          isWholeOrder: true
        })
      }
    })
  },

  // 部分配单
  partOrder() {
    if (this.data.checkedList.length === 0) {
      Toast({
        context: this,
        selector: '#t-toast',
        message: '请先选择数据',
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/order-allocation/spot-trading-order-detail/spot-trading-order-detail',
      events: {
        acceptDataFromSpotTradingOrderDetail: (data) => {
          console.log(data)
        }
      },
      success: (res) => {
        res.eventChannel.emit('acceptDataFromSpotTradingOrder', {
          checkList: this.data.checkedList,
          isWholeOrder: false
        })
      }
    })
  }
}) 