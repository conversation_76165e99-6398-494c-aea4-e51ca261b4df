// pages/order-allocation/modify-packaging/modify-packaging.js
Page({

    /**
     * 页面的初始数据
     */
    data: {
        dataList:[],
        overlayVisible:false,
        showConfirm:false,
        deld:[],
        order:'',
        num:'',
        packId:'',
        flag:'',
        scanType:'',
        index:0
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        var that=this;
        const eventChannel = this.getOpenerEventChannel()
        // 监听acceptDataFromOpenerPage事件，获取上一页面通过eventChannel传送到当前页面的数据
        eventChannel.on('acceptDataFromDischargeCargo', function(data) {
            console.log(data)
            that.setData({
                dataList:data.data
            })
        })
    },
    cheackOne(e){
        let index=e.currentTarget.dataset.index;  
        let dataList = this.data.dataList;
        dataList[index].active=!dataList[index].active
        this.setData({
            dataList: dataList
        })
        console.log(dataList)
    },
    // 修改扫描过的捆包
    editInfor(e){
        var item = e.currentTarget.dataset.item
        console.log(item)
        this.setData({
            overlayVisible:true,
            order:item.order,
            piceNum:item.piceNum,
            packId:item.packId,
            flag:item.outPackFlag,
            scanType:item.scanType,
            index:e.currentTarget.dataset.index
        })   
    },
    // 修改扫描顺序和板数
    numberChange(e){
        // 先判断是不是正整数
        if(/(^[0-9]*$)/.test(e.detail.value)){
            let propName = e.currentTarget.dataset.name;
            let dataToUpdate = {};
            dataToUpdate[propName] = e.detail.value;
            this.setData(dataToUpdate);
        }else{
            wx.showToast({
                title: '只能输入正整数',
                icon: 'none',
                duration: 1000
              })  
        }
    },
    // 自带货和板材类型
    onChange(e){
        let propName = e.currentTarget.dataset.name;
        let dataToUpdate = {};
        dataToUpdate[propName] = e.detail.value;
        this.setData(dataToUpdate);
    },
    cancle(){
        this.setData({
            overlayVisible:false
        })  
    },
    confirm(){
        console.log(this.data.piceNum)
        if(this.data.scanType=="板"&&parseInt(this.data.piceNum)>0||this.data.scanType=="卷"){
        // 先把修改的值赋值到数组里
        var oldData=this.data.dataList
        oldData[this.data.index].order=this.data.order;
        oldData[this.data.index].outPackFlag=this.data.flag;
        oldData[this.data.index].scanType=this.data.scanType;
        oldData[this.data.index].piceNum=this.data.piceNum;
        // 需要重新排列顺序
        console.log(oldData)
        let userSort = []
        userSort = oldData.sort((item1, item2) =>
          item1.order-item2.order
        );
        // 重新计算顺序，确保顺序连续（1,2,3...）
        userSort = userSort.map((item, index) => {
            return {
                ...item,
                order: index + 1
            }
        });
        this.setData({
            overlayVisible:false,
            dataList:userSort
        }) 
    }else{
        wx.showToast({
            title: '板材类型为板时板数为必填！！！',
            icon: 'none',
            duration: 2000
          })
    }
    },
    cheackOrder(){
        let detailsList=[]
        detailsList = this.data.dataList.filter(item => {
            return item.active==true
        }) 
        if(detailsList.length>0){
            this.setData({
                showConfirm:true
            }) 
            //收集要删除数据的id
            this.setData({
                deld:detailsList
            })
        }else{
            wx.showToast({
                title: '请先勾选数据',
                icon: 'none',
                duration: 1500
              })
        }
    },
    cancelDialog(){
        this.setData({
            showConfirm:false
        })  
    },
    comparativeData(arr1, arr2){
        const idsToExclude = new Set(arr2.map(item => item.order));
        return arr1.filter(item => !idsToExclude.has(item.order));
    },
    // 确认删除
    confirmDialog(){
        var arr=this.comparativeData(this.data.dataList,this.data.deld)
        // 删除后重新计算顺序，确保顺序连续
        arr = arr.map((item, index) => {
            return {
                ...item,
                order: index + 1
            }
        });
        console.log(arr)
        this.setData({
            dataList:arr,
            showConfirm:false
        })  
    },
    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {
        let pages = getCurrentPages();   //获取小程序页面栈
        let beforePage = pages[pages.length -2];  //获取上个页面的实例对象
        beforePage.setData({      //直接修改上个页面的数据（可通过这种方式直接传递参数）
            scanList:this.data.dataList
        });
    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    }
})