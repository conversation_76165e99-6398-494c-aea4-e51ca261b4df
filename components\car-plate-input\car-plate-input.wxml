<view class="container">
  <view class="plate-display">
    <view class="code-boxes">
      <block wx:for="{{codeArray}}" wx:key="index">
        <view class="{{activeIndex === index ? 'code-box active' : 'code-box'}}" style="font-size: {{index === 7 ? fontSize : '32rpx'}}" data-index="{{index}}" bindtap="highlightCurrentBox" bindblur="blurCurrentBox" data-id="{{index}}">
          {{item}}
        </view>
      </block>
    </view>
  </view>

  <view class="custom-keyboard" wx:if="{{showKeyboard}}" style="bottom: {{bottom}};">
    <view class="close-btn" bindtap="hideKeyboard">关闭</view>

    <!-- 省份键盘 -->
    <block wx:if="{{showProvincePicker}}">
      <view class="province-keyboard">
        <block wx:for="{{provinces}}" wx:key="index">
          <view class="key" bindtap="selectProvince" data-value="{{item}}">
            {{item}}
          </view>
        </block>

        <view class="key backspace-key" bindtap="onBackspaceTap" style="{{bottom == 0 ? '' : 'margin-left: auto;'}}">⌫</view>
      </view>
    </block>


    <!-- 调整后的字母数字键盘 -->
    <block wx:else>
      <view class="key-row">
        <view class="key {{isDisabledNum ? 'disabled' : ''}}" wx:for="{{currentKeyboard[0]}}" wx:key="*this" bindtap="onKeyTap" data-value="{{item}}">{{item}}</view>
      </view>
      <view class="key-row">
        <view class="key {{item == 'I' || (activeIndex > 1 && item == 'O') ? 'disabled' : ''}}" wx:for="{{currentKeyboard[1]}}" wx:key="*this" bindtap="onKeyTap" data-value="{{item}}">{{item}}</view>
      </view>
      <view class="key-row">
        <view class="key" wx:for="{{currentKeyboard[2]}}" wx:key="*this" bindtap="onKeyTap" data-value="{{item}}">{{item}}</view>
      </view>
      <view class="key-row last-row">
        <view class="key" wx:for="{{currentKeyboard[3]}}" wx:key="*this" bindtap="onKeyTap" data-value="{{item}}">{{item}}</view>
      </view>
      <view class="key-row last-row">
        <view class="key" wx:for="{{currentKeyboard[4]}}" wx:key="*this" bindtap="onKeyTap" data-value="{{item}}">{{item}}</view>
        <view class="key backspace-key" bindtap="onBackspaceTap">⌫</view>
      </view>
    </block>
  </view>
</view>